NotificationV1:
  type: object
  properties:
    title:
      type: string
      description: Tí<PERSON><PERSON> da notificação
      example: "título da notificação"
    message:
      type: string
      description: Mensagem da notificação
      example: "mensagem da notificação"
    user_id:
      type: string
      format: uuid
      description: ID do usuário que criou a notificação (apenas para criação)
      example: "user_id"
    destiny_users:
      type: array
      items:
        type: string
        format: uuid
      description: IDs dos usuários destinatários
      example: ["user_id_1", "user_id_2"]
    destiny_departments:
      type: array
      items:
        type: string
        format: uuid
      description: IDs dos departamentos destinatários
      example: ["department_id_1", "department_id_2"]
  required:
    - title
    - message

NotificationUpdateV1:
  type: object
  properties:
    title:
      type: string
      description: Título da notificação
      example: "título da notificação"
    message:
      type: string
      description: Mensagem da notificação
      example: "mensagem da notificação"
    destiny_users:
      type: array
      items:
        type: string
        format: uuid
      description: IDs dos usuários destinatários
      example: ["user_id_1", "user_id_2"]
    destiny_departments:
      type: array
      items:
        type: string
        format: uuid
      description: IDs dos departamentos destinatários
      example: ["department_id_1", "department_id_2"]

NotificationResponseV1:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID único da notificação
      example: "notification_id"
    title:
      type: string
      description: Título da notificação
      example: "título da notificação"
    message:
      type: string
      description: Mensagem da notificação
      example: "mensagem da notificação"
    user_id:
      type: string
      format: uuid
      description: ID do usuário que criou a notificação
      example: "user_id"
    created_at:
      type: string
      format: date-time
      description: Data de criação
      example: "2024-01-15T10:30:00Z"
    updated_at:
      type: string
      format: date-time
      description: Data de atualização
      example: "2024-01-15T10:30:00Z"

NotificationCreateResponseV1:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID da notificação criada
      example: "notification_id"
