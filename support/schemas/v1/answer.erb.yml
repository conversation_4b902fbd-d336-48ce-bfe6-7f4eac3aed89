AnswerActionV1:
  type: object
  properties:
    user_id:
      type: string
      format: uuid
      description: ID do usuário que está executando a ação
      example: "user_id"
  required:
    - user_id

AnswerSuccessResponseV1:
  type: object
  properties:
    success:
      type: boolean
      description: Indica se a operação foi bem-sucedida
      example: true
    guids:
      type: array
      items:
        type: string
        format: uuid
      description: Lista de IDs das respostas afetadas
      example: ["answer_id"]
    warning:
      type: array
      items:
        type: string
      description: Lista de avisos (se houver)
      example: []

AnswerErrorResponseV1:
  type: object
  properties:
    errors:
      type: array
      items:
        type: string
      description: Lista de erros
      example: ["Erro ao processar a resposta"]
