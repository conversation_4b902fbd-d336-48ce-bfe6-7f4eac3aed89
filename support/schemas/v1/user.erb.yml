UserV1:
  type: object
  properties:
    name:
      type: string
      description: Nome completo do usuário
      example: "nome do usuário"
    email:
      type: string
      format: email
      description: Email do usuário
      example: "email do usuário"
    password:
      type: string
      description: <PERSON><PERSON> do usu<PERSON>rio (obrigatória para provider email)
      example: "senha do usuário"
    password_confirmation:
      type: string
      description: <PERSON><PERSON><PERSON><PERSON> da senha (obrigatória para provider email)
      example: "senha do usuário"
    department_ids:
      type: array
      items:
        type: string
        format: uuid
      description: IDs dos departamentos do usuário
      example: ["department_id"]
    provider:
      type: string
      description: Provedor de autenticação
      default: email
      example: "email"
    limited:
      type: boolean
      description: Se o usuário tem acesso limitado
      default: false
      example: false
    coordinator:
      type: boolean
      description: Se o usuário é coordenador
      default: false
      example: false
    notification:
      type: boolean
      description: Se o usuário recebe notificações
      default: false
      example: false
    approved:
      type: boolean
      description: Se o usuário foi aprovado
      default: true
      example: true
    create_confirmed:
      type: boolean
      description: Se deve criar o usuário já confirmado
      default: false
      example: false
    chat_enabled:
      type: boolean
      description: Se o chat está habilitado
      default: false
      example: false
    block_menus:
      type: array
      items:
        type: string
      description: Menus bloqueados para o usuário
      example: ["dashboard", "search", "favorites", "bulk_saving_answers", "answer_versions", "report", "statistics"]
  required:
    - name
    - email
    - provider

UserResponseV1:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID único do usuário
      example: "user_id"
    name:
      type: string
      description: Nome completo do usuário
      example: "nome do usuário"
    email:
      type: string
      format: email
      description: Email do usuário
      example: "email do usuário"
    limited:
      type: boolean
      description: Se o usuário tem acesso limitado
      example: false
    coordinator:
      type: boolean
      description: Se o usuário é coordenador
      example: false
    notification:
      type: boolean
      description: Se o usuário recebe notificações
      example: false
    approved:
      type: boolean
      description: Se o usuário foi aprovado
      example: true
    chat_enabled:
      type: boolean
      description: Se o chat está habilitado
      example: false
    created_at:
      type: string
      format: date-time
      description: Data de criação
      example: "2024-01-15T10:30:00Z"
    updated_at:
      type: string
      format: date-time
      description: Data de atualização
      example: "2024-01-15T10:30:00Z"
