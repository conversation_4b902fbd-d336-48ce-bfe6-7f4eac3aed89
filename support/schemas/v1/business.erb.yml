BusinessXmlRequestV1:
  type: string
  format: xml
  description: XML contendo dados de business e conteúdos para criação/atualização
  example: |
    <?xml version="1.0" encoding="UTF-8"?>
    <fourmdg>
      <business guid="business_id" name="Business Name" group_guid="group_id">
        <contents>
          <content guid="content_id" updated_at="2024-01-15T10:30:00Z" created_by_id="user_id" created_by_name="User Name" current_step_id="step_id" current_step_name="Step Name">
            <step label="Step Name" user="user_id" current_user="current_user_id" guid="step_id">
              <template guid="template_id">
                <field guid="field_id" label="Field Label" value="Field Value"/>
              </template>
            </step>
          </content>
        </contents>
      </business>
    </fourmdg>

BusinessSuccessResponseV1:
  type: object
  properties:
    success:
      type: boolean
      description: Indica se a operação foi bem-sucedida
      example: true
    guids:
      type: array
      items:
        type: string
        format: uuid
      description: Lista de IDs dos conteúdos criados/atualizados
      example: ["content_id_1", "content_id_2"]
    warning:
      type: array
      items:
        type: string
      description: Lista de avisos (se houver)
      example: []

BusinessErrorResponseV1:
  type: object
  properties:
    errors:
      type: array
      items:
        type: string
      description: Lista de erros
      example: ["Erro ao processar o XML"]

BusinessDeleteRequestV1:
  type: object
  properties:
    user_who_deleted_id:
      type: string
      format: uuid
      description: ID do usuário que está deletando o conteúdo
      example: "user_id"
    deletion_reason:
      type: string
      description: Motivo da exclusão
      example: "Conteúdo obsoleto"

BusinessDeleteSuccessResponseV1:
  type: object
  properties:
    success:
      type: boolean
      description: Indica se a exclusão foi bem-sucedida
      example: true

BusinessNotFoundResponseV1:
  type: object
  properties:
    success:
      type: boolean
      description: Indica se a operação foi bem-sucedida
      example: false
    errors:
      type: string
      description: Mensagem de erro
      example: "Record not found"
