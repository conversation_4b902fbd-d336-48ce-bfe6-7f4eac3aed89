ErrorResponseV1:
  type: object
  properties:
    errors:
      type: array
      items:
        type: string
      description: Lista de erros de validação
      example: ["Email já está em uso", "Senha é muito curta"]

NotFoundResponseV1:
  type: object
  properties:
    status:
      type: integer
      description: Código de status HTTP
      example: 404
    error:
      type: string
      description: Mensagem de erro
      example: "Not Found"
    exception:
      type: string
      description: Tipo da exceção
      example: "#<ActiveRecord::RecordNotFound: Couldn't find User with 'id'=invalid_id>"

UnauthorizedResponseV1:
  type: object
  properties:
    error:
      type: string
      description: Mensagem de erro de autorização
      example: "Unauthorized"
