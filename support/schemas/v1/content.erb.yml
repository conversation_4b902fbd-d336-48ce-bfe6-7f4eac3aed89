ContentQueryParametersV1:
  type: object
  properties:
    business_id:
      type: string
      format: uuid
      description: ID do business
      example: "business_id"
    contents:
      type: boolean
      description: Se deve incluir conteúdos na resposta
      default: true
      example: true
    structures:
      type: boolean
      description: Se deve incluir estruturas na resposta
      default: false
      example: false
    deleted:
      type: boolean
      description: Se deve incluir conteúdos deletados
      default: false
      example: false
    start:
      type: string
      format: date-time
      description: Data de início para filtrar por updated_at
      example: "2024-01-01T00:00:00Z"
    end:
      type: string
      format: date-time
      description: Data de fim para filtrar por updated_at
      example: "2024-12-31T23:59:59Z"
    content_id:
      type: string
      format: uuid
      description: ID específico do conteúdo
      example: "content_id"
    sub_content_id:
      type: string
      format: uuid
      description: ID específico do sub-conteúdo
      example: "sub_content_id"
    parent_id:
      type: string
      format: uuid
      description: ID do conteúdo pai
      example: "parent_content_id"
    page:
      type: integer
      description: Número da página para paginação
      default: 1
      example: 1
  required:
    - business_id

ContentXmlResponseV1:
  type: string
  format: xml
  description: Resposta em formato XML contendo os dados dos conteúdos
  example: |
    <?xml version="1.0" encoding="UTF-8"?>
    <fourmdg tenant="test">
      <business guid="business_id" name="Business Name" group_guid="group_id">
        <contents>
          <content guid="content_id" updated_at="2024-01-15T10:30:00Z" created_by_id="user_id" created_by_name="User Name" current_step_id="step_id" current_step_name="Step Name">
            <step label="Step Name" user="user_id" current_user="current_user_id" guid="step_id">
              <template guid="template_id">
                <field guid="field_id" label="Field Label" value="Field Value"/>
              </template>
            </step>
          </content>
        </contents>
        <structures>
          <steps>
            <step guid="step_id" label="Step Name">
              <template guid="template_id" name="Template Name" description="Template Description" variable="template_variable"/>
            </step>
          </steps>
          <templates>
            <template guid="template_id" variable="template_variable">
              <field guid="field_id" type="text" size="100" height="1" tooltip="Field Tooltip" order="1" required="true" enabled="true" visible="true" label="Field Label" input_variable="input_var" output_variable="output_var" show_on_list="true" show_on_form="true">
                <option label="Option Label" value="option_value"/>
              </field>
            </template>
          </templates>
        </structures>
      </business>
    </fourmdg>
