AdministratorV1:
  type: object
  properties:
    name:
      type: string
      description: Nome completo do administrador
      example: "nome do usuário"
    email:
      type: string
      format: email
      description: Email do administrador
      example: "email do usuário"
    password:
      type: string
      description: Senha do administrador
      example: "senha do usuário"
    password_confirmation:
      type: string
      description: Con<PERSON><PERSON><PERSON> da senha
      example: "senha do usuário"
    approved:
      type: boolean
      description: Se o administrador foi aprovado
      default: false
      example: true
    owner:
      type: boolean
      description: Se o administrador é proprietário
      default: false
      example: false
  required:
    - name
    - email
    - password
    - password_confirmation

AdministratorResponseV1:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID único do administrador
      example: "administrator_id"
    name:
      type: string
      description: Nome completo do administrador
      example: "nome do usuário"
    email:
      type: string
      format: email
      description: Email do administrador
      example: "email do usuário"
    approved:
      type: boolean
      description: Se o administrador foi aprovado
      example: true
    owner:
      type: boolean
      description: Se o administrador é proprietário
      example: false
    created_at:
      type: string
      format: date-time
      description: Data de criação
      example: "2024-01-15T10:30:00Z"
    updated_at:
      type: string
      format: date-time
      description: Data de atualização
      example: "2024-01-15T10:30:00Z"

ErrorResponseV1:
  type: object
  properties:
    errors:
      type: array
      items:
        type: string
      description: Lista de erros de validação
      example: ["Email já está em uso", "Senha é muito curta"]
