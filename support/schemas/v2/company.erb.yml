Company:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID único da empresa
    name:
      type: string
      description: Nome da empresa
    subdomain:
      type: string
      description: Subdomínio da empresa
    api_key:
      type: string
      description: Chave de API da empresa
    created_at:
      type: string
      format: date-time
      description: Data de criação
    updated_at:
      type: string
      format: date-time
      description: Data de atualização
  required:
    - id
    - name
    - subdomain

CompanyResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID da empresa criada/atualizada
  required:
    - id
