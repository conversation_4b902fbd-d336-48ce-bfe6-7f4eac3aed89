ErrorResponse:
  type: object
  properties:
    errors:
      oneOf:
        - type: string
        - type: array
          items:
            type: string
      description: Mensagem(ns) de erro
  required:
    - errors

NotFoundResponse:
  type: object
  properties:
    error:
      type: string
      description: Mensagem de erro
      example: "Registro não encontrado"
  required:
    - error

SuccessResponse:
  type: object
  properties:
    message:
      type: string
      description: Mensagem de sucesso
      example: "Operação realizada com sucesso"
  required:
    - message

UnauthorizedResponse:
  type: object
  properties:
    error:
      type: string
      description: Mensagem de erro de autorização
  required:
    - error
