Administrator:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID único do administrador
    email:
      type: string
      format: email
      description: Email do administrador
    name:
      type: string
      description: Nome completo do administrador
    approved:
      type: boolean
      description: Se o administrador está aprovado
    confirmed_at:
      type:
        - string
        - "null"
      format: date-time
      description: Data de confirmação do administrador
      nullable: true
    deleted_at:
      type:
        - string
        - "null"
      format: date-time
      description: Data de exclusão do administrador
      nullable: true
    created_at:
      type: string
      format: date-time
      description: Data de criação
    updated_at:
      type: string
      format: date-time
      description: Data de atualização
  required:
    - id
    - email
    - name

AdministratorResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID do administrador criado/atualizado
  required:
    - id
