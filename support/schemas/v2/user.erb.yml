User:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID único do usuário
    email:
      type: string
      format: email
      description: Email do usuário
    name:
      type: string
      description: Nome completo do usuário
    limited:
      type: boolean
      description: Se o usuário tem acesso limitado
    coordinator:
      type: boolean
      description: Se o usuário é coordenador
    notification:
      type: boolean
      description: Se o usuário recebe notificações
    approved:
      type: boolean
      description: Se o usuário está aprovado
    confirmed_at:
      type:
        - string
        - "null"
      format: date-time
      description: Data de confirmação do usuário
      nullable: true
    deleted_at:
      type:
        - string
        - "null"
      format: date-time
      description: Data de exclusão do usuário
      nullable: true
    chat_enabled:
      type: boolean
      description: Se o chat está habilitado para o usuário
    block_menus:
      type: array
      items:
        type: string
      description: Lista de menus bloqueados para o usuário
    allow_password_change:
      type: boolean
      description: Se o usuário pode alterar a senha
    department_ids:
      type: array
      items:
        type: string
        format: uuid
      description: IDs dos departamentos do usuário
    created_at:
      type: string
      format: date-time
      description: Data de criação
    updated_at:
      type: string
      format: date-time
      description: Data de atualização
  required:
    - id
    - email
    - name

UserResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID do usuário criado/atualizado
  required:
    - id
