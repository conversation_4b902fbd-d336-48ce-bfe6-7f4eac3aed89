Department:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID único do departamento
    name:
      type: string
      description: Nome do departamento
    limited:
      type: boolean
      description: Se o departamento tem acesso limitado
    deleted_at:
      type:
        - string
        - "null"
      format: date-time
      description: Data de exclusão do departamento
      nullable: true
    created_at:
      type: string
      format: date-time
      description: Data de criação
    updated_at:
      type: string
      format: date-time
      description: Data de atualização
  required:
    - id
    - name

DepartmentResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: ID do departamento criado/atualizado
  required:
    - id
