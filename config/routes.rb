require 'sidekiq_unique_jobs/web'
require 'sidekiq/bulk'
require 'sidekiq-scheduler/web'

Rails.application.routes.draw do
  concern :activate do
    member do
      patch :activate
    end
  end

  mount LetterOpenerWeb::Engine, at: '/letter_opener' if Rails.env.development?
  mount Rswag::Ui::Engine => '/api-docs'
  mount Rswag::Api::Engine => '/api-docs'

  if Rails.env.production? || Rails.env.staging? || Rails.env.academy? || Rails.env.uat?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      ActiveSupport::SecurityUtils.secure_compare(Digest::SHA256.hexdigest(username), Digest::SHA256.hexdigest('4mdg-admin')) &
        ActiveSupport::SecurityUtils.secure_compare(Digest::SHA256.hexdigest(password), Digest::SHA256.hexdigest('#EXB3864~6Vw!>'))
    end
  end

  mount Sidekiq::Web => '/sidekiq'

  scope :api, defaults: { format: 'json' } do
    resource :healthcheck, only: :show do
      get '/database', to: 'healthchecks#database_check'
    end

    get '/send_issue_to_sentry', to: 'healthchecks#send_issue_to_sentry'

    scope :v1 do
      mount_devise_token_auth_for 'User', at: 'auth', controllers: {
        confirmations: 'overrides/confirmations',
        sessions: 'overrides/sessions',
        passwords: 'overrides/passwords',
        omniauth_callbacks: 'overrides/omniauth_callbacks',
        registrations: 'overrides/registrations',
        unlocks: 'overrides/unlocks'
      }

      mount_devise_token_auth_for 'Administrator', at: 'administrator_auth', skip: %i[registrations], controllers: {
        omniauth_callbacks: 'overrides/omniauth_callbacks'
      }

      get :dashboard, to: 'dashboard#index'
      get 'dashboard/duplicated_fields', to: 'dashboard#duplicated_fields'
      get :report, to: 'dashboard#report'

      resources :auditorships, only: %i[index] do
        get :summary, on: :collection
      end

      resources :users, concerns: :activate do
        member do
          put :welcome_video_watched
          put :lock
          put :unlock
        end

        collection do
          get :notifications
          get :top_answers
          post :token_to_confirm_step
        end
      end

      resources :administrators, concerns: :activate
      resources :business_groups, concerns: :activate
      resources :search do
        collection do
          get :search
          post :build_query
        end
      end
      resources :dependent_field_rules
      resources :validation_field_rules

      resources :data_profile, only: :index do
        collection do
          get '/field_stats', to: 'data_profile#field_stats'
          get '/similarity', to: 'data_profile#similarity'
        end
      end

      resources :companies, only: %i[index create update show destroy] do
        collection do
          get ':subdomain/detail', to: 'companies#detail' # public endpoint
          get ':subdomain/current', to: 'companies#current' # requires credentials
          get ':subdomain/get_name_by_subdomain', to: 'companies#get_name_by_subdomain' # requires credentials

          patch :update_theme
          patch :update_allowed_ips
          patch :update_token_lifespan
          patch :update_data_replacement

          get :business_groups
          get :find_business
          get :content_columns
          get :content_datatable, constraints: { format: 'datatable' }
        end

        member do
          put :update_elasticsearch_index
          delete :remove_attachment
        end
      end

      resources :answers, only: %i[verify_token] do
        member do
          get :verify_token
        end
      end

      resources :businesses, concerns: :activate do
        member do
          get :fields
          get :export_all_steps_models
        end

        resources :show_on_list_fields, only: %i[index create update destroy] do
          collection do
            post :bulk_create
          end
        end
        resources :steps, concerns: :activate

        resources :contents, only: %i[index] do
          collection do
            post '/', constraints: { format: 'json' }, to: 'contents#create'
            post '/', constraints: { format: 'datatable' }, to: 'contents#index'
          end
        end
      end

      resources :templates, concerns: :activate do
        resources :fields, concerns: :activate
      end

      resources :fields do
        member do
          get :available_options
          patch :activate
          post :add_option
        end

        collection do
          get :pk_fields_for_business
        end

        resources :field_validations, shallow: true, only: %i[index create destroy]
      end

      resources :menus, only: :index

      resources :translations, only: %i[index create destroy] do
        collection do
          post :migrate_data
        end
      end

      resources :step_templates, only: %i[create destroy] do
        member do
          patch :order
        end
      end

      resources :step_permissions, only: %i[create destroy index] do
        collection do
          get :for_current_user
        end
      end

      resources :departments, only: %i[create index show update]
      resources :troubleshootings, only: :index
      resources :dependent_reference_fields
      resources :answer_versions, only: :index

      resources :bulk_saving_answers, only: %i[index show update] do
        collection do
          post :bulk_alteration
          post :bulk_alteration_preview
          post '/', constraints: { format: 'json' }, to: 'bulk_saving_answers#create'
          post '/', constraints: { format: 'datatable' }, to: 'bulk_saving_answers#index'
          post :fill
        end

        member do
          put :process_orphans
        end
      end

      resources :bulk_destroying_contents, only: %i[create index show update] do
        collection do
          delete :destroy_all_contents
          delete :destroy_selected_contents
        end

        member do
          put :process_orphans
        end
      end

      resources :favorites, only: %i[create destroy index] do
        collection do
          get :as_menu
        end
      end

      resources :themes, only: :index do
        collection do
          get :style
        end
      end

      resources :business_headers, only: %i[create destroy index] do
        collection do
          get :field_with_value
        end
      end

      resources :contents, only: %i[show destroy update create] do
        member do
          get :show_on_list_values
          get :show_on_form_values
          get :show_modifications
          get :note
          get :reference_detail

          patch :restore
          get :all_values
          get :summary_values
        end

        resources :answers, only: %i[update] do
          member do
            patch :authorize
            patch :reject
            patch :revision

            post :show, to: 'answers#show'
            post :validate
            post :validate_dynamic_dependent
          end
        end
      end

      resources :notifications, only: %i[index] do
        member do
          post 'read'
        end
      end

      resource :chat, only: %i[] do
        collection do
          post :login
          get :users
        end
      end

      resources :statistics
      resources :widgets, only: %i[show create update destroy]
      resources :contacts, only: %i[create index show update destroy]
    end
  end

  scope :external, defaults: { format: 'json' } do
    scope :v2 do
      resources :administrators, only: %i[index show create update], controller: 'external/v2/administrators'
      resources :users, only: %i[create destroy index show], controller: 'external/v2/users'
      resources :departments, only: %i[index show create update destroy], controller: 'external/v2/departments'
      put 'users', controller: 'external/v2/users', action: :update

      resources :businesses, concerns: :activate do
        resources :contents, only: %i[create update show], controller: 'external/v2/contents'
        put 'contents', controller: 'external/v2/contents', action: :upsert

        post "/contents/show", to: 'external/v2/contents#index'
      end
    end
  end

  scope :external, defaults: { format: 'xml' } do
    resources :companies, only: %i[create], controller: 'external/companies', defaults: { format: 'json' }

    resources :contents, only: %i[index], controller: 'external/contents' do
      resources :steps, only: [] do
        patch :changing, on: :member, to: 'external/answers#changing', defaults: { format: 'json' }

        patch :authorize, on: :member, to: 'external/answers#authorize', defaults: { format: 'json' }
        patch :reject, on: :member, to: 'external/answers#reject', defaults: { format: 'json' }
      end
    end

    resources :sub_contents, only: :index, controller: 'external/sub_contents'
    resources :businesses, only: %i[create update destroy], controller: 'external/businesses'
    resources :sub_businesses, only: %i[create update destroy], controller: 'external/sub_businesses'
    resources :users, only: %i[create destroy update], controller: 'external/users'
    resources :administrators, only: %i[create destroy], controller: 'external/administrators'
    resources :notifications, only: %i[show create update destroy], controller: 'external/notifications', format: 'json'
  end
end
