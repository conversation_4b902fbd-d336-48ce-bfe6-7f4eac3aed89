:concurrency: 10

development:
  :verbose: true
sandbox:
  :verbose: true
test:
  :verbose: false

:queues:
  - [mailers, 7]
  - [statistics, 6]
  - [save_answer_high_priority_worker, 5]
  - [bulk_alteration_high_priority_worker, 5]
  - [save_answer_low_priority_worker, 4]
  - [bulk_alteration_low_priority_worker, 4]
  - [save_answer_staff_high_priority_worker, 3]
  - [bulk_alteration_staff_high_priority_worker, 3]
  - [save_answer_staff_low_priority_worker, 2]
  - [bulk_alteration_staff_low_priority_worker, 2]
  - [bulk_deletion_setup, 2]
  - [bulk_deletion, 1]
  - default

#cron parser: https://github.com/floraison/fugit
:scheduler:
  :schedule:
    delete_unused_drafts:
      cron: "00 01 * * *" # every day at 01h
      class: DeleteUnusedDraftsWorker
    delete_old_troubleshootings:
      cron: "00 01 * * *" # every day at 01h
      class: DeleteOldTroubleshootingsWorker
    update_cloudwatch_queue_statistics:
      every: 2 minutes
      class: UpdateCloudwatchQueueStatisticsWorker
    reprocess_incomplete_bulk_saving_answers:
      every: 30 minutes
      class: ReprocessIncompleteBulkSavingAnswersWorker
    check_not_indexed_contents:
      every: 60 minutes
      class: CheckOutdatedContentsInElasticsearchWorker
    check_not_indexed_businesses_keys:
      cron: "00 01 * * *" # every day at 01h
      class: CheckNotIndexedBusinessesKeysWorker
