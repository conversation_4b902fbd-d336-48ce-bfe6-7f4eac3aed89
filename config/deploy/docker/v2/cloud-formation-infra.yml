AWSTemplateFormatVersion: '2010-09-09'
Description: Template para gerenciamento da infraestrutura do 4MDG para o ambiente de ${BRANCH}
Parameters:
  DesiredEc2Capacity:
    Default: ${DESIRED_EC2_CAPACITY}
    Description: Capacidade desejada para o ASG do EC2
    MinValue: 0
    Type: Number
  MaxAsgSize:
    Default: ${MAX_ASG_SIZE}
    Description: Quantidade máxima de máquinas do ASG
    MinValue: 1
    Type: Number
  MinAsgSize:
    Default: ${MIN_ASG_SIZE}
    Description: Quantidade mínima de máquinas do ASG
    MinValue: 0
    Type: Number
  TargetAsgCapacity:
    Default: ${TARGET_ASG_CAPACITY}
    Description: Capacity reservation desejada para o ASG
    MaxValue: 100
    MinValue: 1
    Type: Number
  WebACLARN:
    Default: arn:aws:wafv2:us-east-1:370999586507:global/webacl/4mdg-cloudfront/3fe55c92-ab85-4966-b057-c48d570d3dc5
    Type: String
  VPC:
    Default: vpc-ba95bac0
    Type: AWS::EC2::VPC::Id
  Subnets:
    Default: subnet-41544626, subnet-adf1e883
    Type: List<AWS::EC2::Subnet::Id>
  PrivateSubnets:
    Default: subnet-0d94492186658bcf1, subnet-0afa37da079e5b81d
    Type: List<AWS::EC2::Subnet::Id>
Mappings:
  EnvInformation:
    ClusterName:
      Value: ${CLUSTER_NAME}
    Env:
      Value: ${BRANCH}
Resources:
  SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Join ['', [Security group para instancias do 4MDG, !FindInMap [EnvInformation, Env, Value]]]
      GroupName: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-SG']]
      SecurityGroupEgress:
        - CidrIp: 0.0.0.0/0
          IpProtocol: -1
      SecurityGroupIngress:
        - CidrIp: 0.0.0.0/0
          Description: Frontend
          FromPort: 80
          IpProtocol: tcp
          ToPort: 80
        - CidrIp: 0.0.0.0/0
          Description: SSH
          FromPort: 22
          IpProtocol: tcp
          ToPort: 22
        - CidrIp: 0.0.0.0/0
          Description: SSL
          FromPort: 443
          IpProtocol: tcp
          ToPort: 443
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
      VpcId: !Ref VPC
  SecurityGroupIngress:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref SecurityGroup
      IpProtocol: -1
      SourceSecurityGroupId: !Ref SecurityGroup
      SourceSecurityGroupOwnerId: !Ref AWS::AccountId
  LaunchTemplate:
    Type: AWS::EC2::LaunchTemplate
    Properties:
      LaunchTemplateData:
        BlockDeviceMappings:
          - Ebs:
              DeleteOnTermination: true
              Encrypted: false
              VolumeSize: 30
              VolumeType: gp3
            DeviceName: /dev/xvda
        DisableApiTermination: false
        EbsOptimized: false
        IamInstanceProfile:
          Arn: !Sub arn:aws:iam::${AWS::AccountId}:instance-profile/ecsInstanceRole
        ImageId: ami-0fb290c25a8f57cfa
        InstanceType: t4g.medium
        KeyName: 4mdg
        MetadataOptions:
          HttpTokens: required
        Monitoring:
          Enabled: true
        SecurityGroupIds:
          - !Ref SecurityGroup
        TagSpecifications:
          - ResourceType: instance
            Tags:
              - Key: Stack
                Value: !FindInMap [EnvInformation, Env, Value]
              - Key: Name
                Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
          - ResourceType: volume
            Tags:
              - Key: Stack
                Value: !FindInMap [EnvInformation, Env, Value]
              - Key: Name
                Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
        UserData:
          Fn::Base64: !Sub
            - |
              #!/bin/bash
              echo ECS_CLUSTER=${Cluster} >> /etc/ecs/ecs.config;echo ECS_BACKEND_HOST= >> /etc/ecs/ecs.config;
              echo ECS_INSTANCE_ATTRIBUTES={\"purpose\": \"general\"} >> /etc/ecs/ecs.config;
              echo ECS_CONTAINER_STOP_TIMEOUT=60 >> /etc/ecs/ecs.config;
              dnf install ec2-instance-connect -y
            - Cluster: !FindInMap [EnvInformation, ClusterName, Value]
      LaunchTemplateName: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-LT']]
  AutoScalingGroup:
    Type: AWS::AutoScaling::AutoScalingGroup
    Properties:
      AutoScalingGroupName: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-ASG']]
      CapacityRebalance: true
      Cooldown: 90
      DesiredCapacity: !Ref DesiredEc2Capacity
      HealthCheckGracePeriod: 30
      HealthCheckType: EC2
      MaxSize: !Ref MaxAsgSize
      MinSize: !Ref MinAsgSize
      MixedInstancesPolicy:
        InstancesDistribution:
          OnDemandAllocationStrategy: prioritized
          OnDemandBaseCapacity: 1
          OnDemandPercentageAboveBaseCapacity: 30
          SpotAllocationStrategy: capacity-optimized
        LaunchTemplate:
          LaunchTemplateSpecification:
            LaunchTemplateId: !Ref LaunchTemplate
            Version: !GetAtt LaunchTemplate.LatestVersionNumber
          Overrides:
            - InstanceType: t4g.medium
            - InstanceType: t4g.large
      NewInstancesProtectedFromScaleIn: false
      VPCZoneIdentifier: !Ref PrivateSubnets
  CapacityProvider:
    Type: AWS::ECS::CapacityProvider
    Properties:
      AutoScalingGroupProvider:
        AutoScalingGroupArn: !Ref AutoScalingGroup
        ManagedScaling:
          MaximumScalingStepSize: 10000
          MinimumScalingStepSize: 1
          Status: ENABLED
          TargetCapacity: !Ref TargetAsgCapacity
        ManagedTerminationProtection: DISABLED
      Name: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-CP']]
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
  Cluster:
    Type: AWS::ECS::Cluster
    Properties:
      CapacityProviders: [!Ref CapacityProvider]
      ClusterName: !FindInMap [EnvInformation, ClusterName, Value]
      ClusterSettings:
        - Name: containerInsights
          Value: disabled
      DefaultCapacityProviderStrategy:
        - CapacityProvider: !Ref CapacityProvider
          Weight: 1
          Base: 0
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
  LoadBalancer:
    DependsOn: LogsS3BucketPolicy
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      LoadBalancerAttributes:
        - Key: access_logs.s3.enabled
          Value: true
        - Key: access_logs.s3.bucket
          Value: !Ref LogsS3Bucket
        - Key: access_logs.s3.prefix
          Value: elb
        - Key: idle_timeout.timeout_seconds
          Value: 120
      Name: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-LB']]
      SecurityGroups:
        - !Ref SecurityGroup
      Subnets: !Ref Subnets
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
  LogsS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-logs']]
      OwnershipControls:
        Rules:
          - ObjectOwnership: BucketOwnerPreferred
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
  LogsS3BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref LogsS3Bucket
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: arn:aws:iam::************:root
            Action:
              - s3:PutObject
            Resource: !Join ['', [!GetAtt LogsS3Bucket.Arn, '/elb/AWSLogs/', !Ref AWS::AccountId, '/*']]
  FrontendS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-frontend']]
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
  FrontendS3BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref FrontendS3Bucket
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: cloudfront.amazonaws.com
            Action:
              - s3:GetObject
            Resource: !Join ['', [!GetAtt FrontendS3Bucket.Arn, '/*']]
            Condition:
              StringEquals:
                AWS:SourceArn:
                  ${cloudFrontDistributionsArns}
  CloudFrontS3OriginAccessControl:
    Type: AWS::CloudFront::OriginAccessControl
    Properties:
      OriginAccessControlConfig:
        Description: Origin access control para cloudFront do 4MDG para o ambiente de ${BRANCH}
        Name: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-access-control']]
        OriginAccessControlOriginType: s3
        SigningBehavior: always
        SigningProtocol: sigv4
  CloudFrontResponseHeadersPolicy:
    Type: AWS::CloudFront::ResponseHeadersPolicy
    Properties:
      ResponseHeadersPolicyConfig:
        Comment: Enable CORS
        CorsConfig:
          AccessControlAllowCredentials: false
          AccessControlAllowHeaders:
            Items:
              - '*'
          AccessControlAllowMethods:
            Items:
              - ALL
          AccessControlAllowOrigins:
            Items:
              - '*'
          AccessControlMaxAgeSec: 600
          OriginOverride: true
        Name: !Join ['', [!FindInMap [EnvInformation, ClusterName, Value], '-response-headers-policy']]
        SecurityHeadersConfig:
          StrictTransportSecurity:
            AccessControlMaxAgeSec: 31536000
            IncludeSubdomains: true
            Override: false
          ContentSecurityPolicy:
            ContentSecurityPolicy: "default-src 'self' data:; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://*.clarity.ms https://*.released.so https://www.googletagmanager.com https://4mdg-web.s3.amazonaws.com https://*.4mdg.com.br https://*.mdmacademy.com.br https://ajax.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://client.rum.us-east-1.amazonaws.com; frame-src 'self' https://www.youtube.com/ https://player.vimeo.com/; style-src 'self' 'unsafe-inline' https://*.released.so https://4mdg-web.s3.amazonaws.com https://cdn.jsdelivr.net https://fonts.googleapis.com; img-src 'self' data: https://c.bing.com https://*.clarity.ms https://*.released.so https://dwamxgqy3aotj.cloudfront.net https://raw.githubusercontent.com https://*.4mdg.com.br https://*.mdmacademy.com.br https://*.s3.amazonaws.com; object-src 'none'; font-src 'self' data: https://*.released.so https://fonts.gstatic.com; connect-src 'self' https://*.clarity.ms https://*.released.so https://www.google-analytics.com/ https://4mdg-web.s3.amazonaws.com wss://chatbot.4mdg.com.br https://chatbot.4mdg.com.br https://cdn.datatables.net https://www.youtube.com/ https://player.vimeo.com/ https://cognito-identity.us-east-1.amazonaws.com https://sts.us-east-1.amazonaws.com https://dataplane.rum.us-east-1.amazonaws.com/ wss://tsock.us1.twilio.com/v3/wsconnect; worker-src blob: ;"
            Override: true
          ContentTypeOptions:
            Override: true
          ReferrerPolicy:
            ReferrerPolicy: "same-origin"
            Override: true
        CustomHeadersConfig:
          Items:
            - Header: Permissions-Policy
              Override: true
              Value: "camera=(), display-capture=(), fullscreen=*, geolocation=(), microphone=()"
  ${cloudFrontDistributions}
  RedirectS3GetRequestsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - edgelambda.amazonaws.com
                - lambda.amazonaws.com
            Action:
              - 'sts:AssumeRole'
      Description: IAM Role para a lambda function de redirecionamento de requests do ambiente de ${BRANCH}
      Path: /service-role/
      Policies:
        - PolicyName: !Join ['', [redirectS3GetRequests-role-, !FindInMap [EnvInformation, Env, Value], '-policy']]
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - logs:PutLogEvents
                Resource: 'arn:aws:logs:*:*:*'
      RoleName: !Join ['', [redirectS3GetRequests-role-, !FindInMap [EnvInformation, Env, Value]]]
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
  RedirectS3GetRequestsLambda:
    Type: AWS::Lambda::Function
    Properties:
      Code:
        ZipFile: |
          exports.handler = function(event, context, callback) {
            const { request } = event.Records[0].cf;

            if (request.method === 'GET' && !request.uri.match(/\..+$/)) {
              console.log(request.uri);

              request.uri = '/index.html';
            }

            callback(null, request);
          };
      Description: Lambda function para redirecionamento de requests do frontend para o ambiente de ${BRANCH}
      FunctionName: !Join ['', [redirectS3GetRequests-, !FindInMap [EnvInformation, Env, Value]]]
      Handler: index.handler
      Role: !GetAtt RedirectS3GetRequestsRole.Arn
      Runtime: nodejs18.x
      Tags:
        - Key: Name
          Value: !Join ['', [4mdg-, !FindInMap [EnvInformation, Env, Value]]]
  RedirectS3GetRequestsLambdaVersion:
    Type: AWS::Lambda::Version
    Properties:
      FunctionName: !Ref RedirectS3GetRequestsLambda
Outputs:
  LoadBalancer:
    Description: A reference to the Application Load Balancer
    Value: !Ref LoadBalancer
    Export:
      Name: !Sub "${AWS::StackName}:LoadBalancer"
  LoadBalancerDNSName:
    Description: A reference to the Application Load Balancer DNS name
    Value: !GetAtt LoadBalancer.DNSName
    Export:
      Name: !Sub "${AWS::StackName}:LoadBalancerDNSName"
