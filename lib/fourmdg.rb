require 'thor'
require 'byebug'
require 'net/ssh'
require 'net/scp'
require 'English'

module Fourmdg
  class CLI < Thor
    include Thor::Actions

    HOSTS = {
      mdmacademy: 'fourmdg-sandbox-aurora-cluster.cluster-c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      sandbox: 'fourmdg-sandbox-aurora-cluster.cluster-c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      staging: 'fourmdg-staging.c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      production: 'fourmdg-production.c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      uat: 'fourmdg-sandbox-aurora-cluster.cluster-c7mmsyhovoiu.us-east-1.rds.amazonaws.com'
    }.freeze
    USER = 'postgres'.freeze
    PASSWORD = 'I5EgpXdpgwvr8W'.freeze
    DATABASES = {
      mdmacademy: 'mdmacademy',
      sandbox: 'fourmdg_sandbox',
      staging: 'fourmdg_staging',
      production: 'fourmdg-api_production',
      uat: 'fourmdg_uat',
    }.freeze
    REMOTE_DUMP_HOSTS = {
      mdmacademy: '**************',
      sandbox: '**************',
      staging: '**************',
      production: '**************',
      uat: '**************'
    }.freeze
    REMOTE_DUMP_USER = 'ubuntu'.freeze
    REMOTE_DUMP_KEY = './keys/database_dump_instance.pem'.freeze
    DUMP_FILE = 'fourmdg.dump'.freeze

    def self.exit_on_failure?
      true
    end

    desc 'dump', 'Efetua o dump do banco escolhido'
    def dump
      environment = ask 'Informe o ambiente que deseja fazer o dump:', %i[blue bold], limited_to: HOSTS.keys.map(&:to_s)

      say 'Listando as bases disponíveis...'

      Net::SSH.start(REMOTE_DUMP_HOSTS[environment.to_sym], REMOTE_DUMP_USER, keys: REMOTE_DUMP_KEY) do |ssh|
        result = ''

        ssh.exec!("PGPASSWORD=#{PASSWORD} psql -h #{HOSTS[environment.to_sym]} -U #{USER} -d #{DATABASES[environment.to_sym]} -c 'SELECT subdomain FROM public.companies'") do |_ch, stream, data|
          say data.force_encoding('UTF-8')

          result << data if stream == :stdout
        end

        subdomains = []

        result.each_line.with_index do |line, index|
          next if [0, 1, result.lines.size - 1, result.lines.size - 2].include?(index)

          subdomains << line.strip
        end

        selected = []

        subdomains = subdomains.sort

        select_subdomain(selected, subdomains - selected)
        select_subdomain(selected, subdomains - selected) while yes? 'Deseja escolher mais clientes? (digite y ou yes para selecionar mais)', %i[yellow bold]

        selected.unshift('shared_extensions')
        selected.unshift('public')

        say "\r\nEfetuado dump dos schemas #{selected.join(', ')}..."

        to_skip = (subdomains - selected).map { |str| "-N #{str}" }.join(' ')

        result = ssh.exec!("PGPASSWORD=#{PASSWORD} pg_dump -h #{HOSTS[environment.to_sym]} -U #{USER} -d #{DATABASES[environment.to_sym]} -v -O -Fc -f #{DUMP_FILE} #{to_skip}") do |_ch, _stream, data|
          say data.force_encoding('UTF-8')
        end

        say "\r\nDump gerado com sucesso.", %i[green bold]
      end

      Net::SCP.download!(REMOTE_DUMP_HOSTS[environment.to_sym], REMOTE_DUMP_USER, "/home/<USER>/#{DUMP_FILE}", DUMP_FILE, ssh: { keys: REMOTE_DUMP_KEY }) do |_ch, name, sent, total|
        say "\rTransferindo #{name}...#{sent}/#{total} "
      end

      Net::SSH.start(REMOTE_DUMP_HOSTS[environment.to_sym], REMOTE_DUMP_USER, keys: REMOTE_DUMP_KEY) do |ssh|
        say "\r\nApagando dump...", %i[yellow bold]
        ssh.exec!("rm -rf /home/<USER>/#{DUMP_FILE}")
      end

      say "\r\Arquivo #{DUMP_FILE} transferido com sucesso.", %i[green bold]
      say 'Para restaurar execute: ', %i[green bold]
      say "pg_restore --clean -d fourmdg_development -h localhost -U postgres -v -O < #{DUMP_FILE}", %i[cyan bold]
    end

    private

    def select_subdomain(selected, available)
      subdomain = ask 'Informe o cliente que deseja incluir no dump:', %i[blue bold], limited_to: available

      selected << subdomain
    end
  end
end
