require 'rails_helper'

RSpec.describe BusinessGroup, type: :model do
  describe 'associations' do
    it { is_expected.to have_many(:businesses) }
  end

  describe 'validations' do
    subject { create(:business_group) }

    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to be_valid }
  end

  describe 'normalize' do
    it 'normalizes record' do
      business_group = create(:business_group, name: "   Test Name   ")
      expect(business_group.name).to eq("Test Name")
    end
  end

  describe 'translated_attribute' do
    let(:business_group) { create(:business_group) }
    let(:attribute) { ["description", "name"].sample }
    let!(:en) { create(:translation, language: :english, attribute_name: attribute, actable: business_group)}
    let!(:es) { create(:translation, language: :spanish, attribute_name: attribute, actable: business_group)}
    let!(:pt) { create(:translation, language: :portuguese, attribute_name: attribute, actable: business_group)}

    before { Company.current.update! enable_internationalization: true }

    context 'when I18n locale is set to english' do
      before { I18n.locale = 'en' }

      it 'returns the english attribute translation' do
        expect(business_group.translated_attribute(attribute)).to eq(en.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(business_group.translated_attribute(attribute, true)).to eq(business_group[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(business_group.translated_attribute(attribute)).to eq(business_group[attribute])
        end
      end

    end

    context 'when I18n locale is set to spanish' do
      before { I18n.locale = 'es' }

      it 'returns the spanish attribute translation' do
        expect(business_group.translated_attribute(attribute)).to eq(es.translated_text)
      end 

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(business_group.translated_attribute(attribute, true)).to eq(business_group[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(business_group.translated_attribute(attribute)).to eq(business_group[attribute])
        end
      end
    end

    context 'when I18n locale is set to portuguese' do
      before { I18n.locale = 'pt-BR' }

      it 'returns the brazilian portuguese attribute translation' do
        expect(business_group.translated_attribute(attribute)).to eq(pt.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(business_group.translated_attribute(attribute, true)).to eq(business_group[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(business_group.translated_attribute(attribute)).to eq(business_group[attribute])
        end
      end
    end
  end
end
