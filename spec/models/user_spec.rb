require 'rails_helper'
require 'date'

RSpec.describe User, type: :model do
  subject { build(:user) }

  describe 'validations' do
    it { is_expected.to be_valid }
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:email) }

    context 'ddi' do
      context 'when present' do
        it 'fails if the ddi is bigger than 4 characters long' do
          subject.ddi = '12345'

          expect(subject).to be_invalid
          expect(subject.errors.of_kind?(:ddi, :too_long)).to be_truthy
        end

        it 'fails if the ddi is smaller than 1 characters long' do
          subject.ddi = ''

          expect(subject).to be_invalid
          expect(subject.errors.of_kind?(:ddi, :too_short)).to be_truthy
        end
      end

      context 'when not present' do
        before { subject.ddi = nil }

        it 'fails if phone and ddd present' do
          expect(subject).to be_invalid
          expect(subject.errors.of_kind?(:ddi, :too_short)).to be_truthy
        end

        it 'does not fail when ddi and phone are not present' do
          subject.ddd = nil
          subject.phone = nil

          expect(subject).to be_valid
          expect(subject.errors.of_kind?(:ddi, :too_short)).to be_falsey
        end
      end
    end

    context 'ddd' do
      context 'when present' do
        it 'fails if the ddd is bigger than 4 characters long' do
          subject.ddd = '12345'

          expect(subject).to be_invalid
          expect(subject.errors.of_kind?(:ddd, :too_long)).to be_truthy
        end
      end

      context 'when not present' do
        before { subject.ddd = nil }

        it 'fails if phone present and ddi 55' do
          subject.ddi = '55'

          expect(subject).to be_invalid
          expect(subject.errors.full_messages).to eq(['Ddd deve ser preenchido'])
        end

        it 'fails if phone present and ddi 1' do
          subject.ddi = '1'

          expect(subject).to be_invalid
          expect(subject.errors.full_messages).to eq(['Ddd deve ser preenchido'])
        end

        it 'does not fail when ddi and phone are not present' do
          subject.ddi = nil
          subject.phone = nil

          expect(subject).to be_valid
          expect(subject.errors.of_kind?(:ddd, :too_short)).to be_falsey
        end
      end
    end

    context 'phone' do
      context 'when present' do
        it 'fails if the phone is bigger than 9 characters long' do
          subject.phone = '12345678901'

          expect(subject).to be_invalid
          expect(subject.errors.of_kind?(:phone, :too_long)).to be_truthy
        end

        it 'fails if the phone is smaller than 7 characters long' do
          subject.phone = '123456'

          expect(subject).to be_invalid
          expect(subject.errors.of_kind?(:phone, :too_short)).to be_truthy
        end
      end

      context 'when not present' do
        before { subject.phone = nil }

        it 'fails when ddi and ddd present' do
          expect(subject).to be_invalid
          expect(subject.errors.of_kind?(:phone, :too_short)).to be_truthy
        end

        it 'does not fail when ddi and ddd are not present' do
          subject.ddi = nil
          subject.ddd = nil

          expect(subject).to be_valid
          expect(subject.errors.of_kind?(:phone, :too_short)).to be_falsey
        end
      end
    end

    context 'uniqueness email' do
      context 'when email for same provider already exists' do
        before { create(:user, email: subject.email) }

        it { is_expected.to be_invalid }

        it 'has messages' do
          subject.valid?

          expect(subject.errors.full_messages).to include 'email já está em uso'
        end
      end

      context 'when email for other provider already exists' do
        before { create(:user, email: subject.email, provider: 'google_oauth2', uid: '134687651385764654') }

        it { is_expected.to be_invalid }

        it 'has messages' do
          subject.valid?

          expect(subject.errors.full_messages).to include 'email já está em uso'
        end
      end
    end

    context 'password' do
      before do
        subject.password = password
        subject.valid?
      end

      context 'with only 8 digits' do
        let(:password) { '44666666' }

        it 'has message' do
          expect(subject.errors.full_messages).to include 'senha é muito curta (mínimo: 14 caracteres)'
        end
      end

      context 'with only 10 numbers' do
        let(:password) { '5465498465465465' }

        it 'has message' do
          expect(subject.errors.full_messages).to eq(['senha deve conter ao menos uma letra minúscula', 'senha deve conter ao menos um símbolo', 'senha deve conter ao menos uma letra maiúscula'])
        end
      end

      context 'with valid password' do
        let(:password) { 'Gv=66>5s4r[zTX' }

        it 'is valid' do
          expect(subject.errors).to be_empty
        end
      end
    end

    describe 'validate_updating_email' do
      context 'when provider is email' do
        let(:user) { create(:user) }

        it 'is valid' do
          expect(user.update(email: '<EMAIL>')).to be_truthy
        end
      end

      context 'when provider is google_oauth2' do
        let(:user) { create(:user, provider: 'google_oauth2', uid: '134687651385764654') }

        it 'is invalid' do
          expect(user.update(email: '<EMAIL>')).to be_falsy

          expect(user.errors.full_messages).to eq([I18n.t('updating_email', scope: 'devise.oauth')])
        end
      end
    end

    describe 'validate_email_domain_valid' do
      let(:auth_domain) { ['coyo.com.br'] }

      before { Company.current.update!(auth_domain: auth_domain, enable_google_oauth: true) }

      context 'when user.provider is email' do
        context 'and auth_domain contains email domain' do
          subject { build(:user, email: '<EMAIL>') }

          it { is_expected.to be_invalid }

          it 'has messages' do
            subject.valid?

            expect(subject.errors.full_messages).to eq(['O domínio coyo.com.br está cadastrado como domínio de login via Google Oauth e não pode ser utilizado para login com email e senha'])
          end
        end

        context 'and auth_domain does not contain email domain' do
          subject { build(:user, email: '<EMAIL>') }

          it { is_expected.to be_valid }
        end
      end

      context 'when user.provider is google_oauth2' do
        context 'and auth_domain contains email domain' do
          subject { build(:user, email: '<EMAIL>', provider: 'google_oauth2', uid: '123546876345354') }

          it { is_expected.to be_valid }
        end

        context 'and auth_domain does not contain email domain' do
          subject { build(:user, email: '<EMAIL>', provider: 'google_oauth2', uid: '123546876345354') }

          it { is_expected.to be_valid }
        end
      end
    end

    describe 'validate_block_menus' do
      context 'when menu name is valid' do
        it 'is valid' do
          expect(build(:user, block_menus: %w[dashboard favorites])).to be_valid
        end
      end

      context 'when menu name is not valid' do
        it 'is invalid' do
          user = build(:user, block_menus: %w[dashboard favorites foo])

          expect(user).to be_invalid
          expect(user.errors[:base]).to include('Menu informado não é permitido')
        end
      end
    end
  end

  describe 'need_change_password?' do
    let(:company_expire_password_after) { 20 }
    let(:user_password_changed_at) { nil }
    let(:user) { create(:user) }

    before do
      Company.current.update!(expire_password_after_in_days: company_expire_password_after)

      user.update_attribute(:password_changed_at, user_password_changed_at)
    end

    subject { user.need_change_password? }

    context 'when user.password_changed_at is nil' do
      it { is_expected.to be true }
    end

    context 'when user.password_changed_at is equal to company.expire_password_after days ago' do
      let(:user_password_changed_at) { 20.days.ago }

      it { is_expected.to be true }
    end

    context 'when user.password_changed_at is before to company.expire_password_after days ago' do
      let(:user_password_changed_at) { 21.days.ago }

      it { is_expected.to be true }
    end

    context 'when user.password_changed_at is after to company.expire_password_after days ago' do
      let(:user_password_changed_at) { 19.days.ago }

      it { is_expected.to be false }
    end

    context 'when user.provider eq google_oauth2' do
      let(:user) { create(:user, provider: 'google_oauth2', uid: 'asdf64s646') }

      context 'when user.password_changed_at is equal to company.expire_password_after days ago' do
        let(:user_password_changed_at) { 20.days.ago }

        it { is_expected.to be false }
      end
    end
  end

  describe 'associations' do
    it { is_expected.to have_many(:permissions).class_name('StepPermission').dependent(:destroy) }
    it { is_expected.to have_and_belong_to_many(:departments) }
    it { is_expected.to have_many(:answers) }
  end

  describe '#active_for_authentication?' do
    let(:user) { create(:user) }

    before { user.skip_confirmation! }

    subject { user.active_for_authentication? }

    context 'when user is active' do
      it { is_expected.to be true }
    end

    context 'when user is deleted' do
      let(:user) { create(:user, deleted_at: Time.zone.now) }

      it { is_expected.to be false }
    end

    context 'when user.password_changed_at is one year ago' do
      let(:user) { create(:user, password_changed_at: 1.year.ago) }
      it { is_expected.to be false }
    end
  end

  describe '#can_access?' do
    let(:business_group) { create(:business_group) }
    let(:business) { create(:business, business_group: business_group) }
    let(:step) { create(:step, business: business) }

    subject { create(:user) }

    before do
      @answer = create(:answer, step: step, position: 0, content: create(:content, business: business, status: :pending, created_at: 10.days.ago))
    end

    context 'when the user has permission to access the answer step' do
      before do
        create(:step_permission, step: step, user: subject)
      end

      it 'returns true' do
        expect(subject.can_access?(@answer.step.id)).to be_truthy
      end
    end

    context 'when the user does not have permission to access the answer step' do
      it 'returns false' do
        expect(subject.can_access?(@answer.step.id)).to be_falsy
      end

      context 'but the user deparment has' do
        before do
          create(:step_permission, step: step, department: create(:department, users: [subject]))
        end

        it 'returns true' do
          expect(subject.can_access?(@answer.step.id)).to be_truthy
        end
      end
    end

    context 'when the user has authorization to access the answer step' do
      before do
        create(:step_permission, step: step, user: subject)
      end

      it 'returns true' do
        expect(subject.can_access?(@answer.step.id)).to be_truthy
      end
    end

    context 'when the user does not have authorization to access the answer step' do
      it 'returns false' do
        expect(subject.can_access?(@answer.step.id)).to be_falsy
      end

      context 'but the user deparment has' do
        before do
          create(:step_permission, step: step, department: create(:department, users: [subject]))
        end

        it 'returns true' do
          expect(subject.can_access?(@answer.step.id)).to be_truthy
        end
      end
    end
  end

  describe '#send_email_requesting_approvement' do
    context 'on create' do
      context 'when approved is false' do
        it 'sends email' do
          expect(UserApprovementMailer).to receive(:request_approvement_for_user).and_call_original

          create(:user, approved: false)
        end
      end

      context 'when approved is true' do
        it 'does not send email' do
          expect(UserApprovementMailer).to_not receive(:request_approvement_for_user)

          create(:user, approved: true)
        end
      end
    end
  end

  describe 'auto aproving user' do
    context 'when the company\'s bypass approval flag is false' do
      before { Company.current.update!(bypass_approval: false) }

      it 'does not auto approve the user' do
        new_user = create(:user, approved: false)

        expect(new_user).not_to be_approved
      end

      it 'does not set the default department' do
        new_user = create(:user, approved: false)

        expect(new_user.departments).to be_empty
      end
    end

    context 'when the company\'s bypass approval flag is true' do
      let(:department) { create(:department) }

      before { Company.current.update!(default_department_id: department.id, bypass_approval: true) }

      it 'does not send email' do
        expect(UserApprovementMailer).not_to receive(:request_approvement_for_user)

        create(:user, approved: false)
      end

      it 'auto approves the user' do
        new_user = create(:user, approved: false)

        expect(new_user).to be_approved
      end

      context 'when the company has a default department' do
        it 'set the default department' do
          new_user = create(:user, approved: false)

          expect(new_user.departments).to eq([department])
        end

        context 'when the user already has a department set' do
          let(:department2) { create(:department) }

          it 'does not set the default department' do
            new_user = create(:user, approved: false, departments: [department2])

            expect(new_user.department_ids).to eq([department2.id])
          end
        end
      end

      context 'when the company does not have the a default department' do
        before { Company.current.update_column(:default_department_id, nil) }

        it 'does not set the default department' do
          new_user = create(:user, approved: false)

          expect(new_user.departments).to be_empty
        end
      end
    end
  end

  context '#send_email_when_user_is_approved' do
    context 'on update' do
      context 'when approved was true' do
        before { @user = create(:user, approved: true) }

        context 'and approved is false' do
          it 'sends email' do
            expect(UserApprovementMailer).to_not receive(:user_approved)

            @user.update(approved: false)
          end
        end

        context 'and approved is true' do
          it 'does not send email' do
            expect(UserApprovementMailer).to_not receive(:user_approved)

            @user.update(approved: true)
          end
        end
      end

      context 'when approved was false' do
        before { @user = create(:user, approved: false) }

        context 'and approved is false' do
          it 'sends email' do
            expect(UserApprovementMailer).to_not receive(:user_approved)

            @user.update(approved: false)
          end
        end

        context 'and approved is true' do
          it 'does not send email' do
            expect(UserApprovementMailer).to receive(:user_approved).and_call_original

            @user.update(approved: true)
          end
        end
      end
    end
  end

  describe '#chat_enabled?' do
    subject { create(:user) }

    before { subject.update_column(:chat_enabled, true) }

    context 'when the company does not have the chat enabled' do
      before { Company.current.update!(chat_enabled: false) }

      it 'returns false' do
        expect(subject.chat_enabled?).to be_falsy
      end
    end

    context 'when the company has the chat enabled' do
      before { Company.current.update!(chat_enabled: true) }

      it 'returns the chat enabled attribute' do
        expect(subject.chat_enabled?).to eq(subject.read_attribute(:chat_enabled))
      end
    end
  end

  describe 'after saving' do
    context 'when the chat is disabled' do
      let(:chat_enabled) { false }

      it 'does not initialize the twilio user service' do
        expect(Twilio::UserService).not_to receive(:new)

        create(:user, chat_enabled: chat_enabled)
      end

      it 'does not save the user' do
        expect_any_instance_of(Twilio::UserService).not_to receive(:save)

        create(:user, chat_enabled: chat_enabled)
      end
    end

    context 'when the chat is enabled' do
      let(:chat_enabled) { true }
      let(:twilio_service) { double(:twilio_service, save: true) }
      let(:user) { create(:user, chat_enabled: false) }

      before do
        Company.current.update!(chat_enabled: true)

        allow(Twilio::UserService).to receive(:new).and_return(twilio_service)
      end

      it 'initializes the twilio user service' do
        expect(Twilio::UserService).to receive(:new).and_return(twilio_service)

        user.update(chat_enabled: chat_enabled)
      end

      it 'tries to save the user' do
        expect(twilio_service).to receive(:save).with(user)

        user.update(chat_enabled: chat_enabled)
      end
    end
  end

  describe 'departments' do
    let(:department) { create(:department) }

    context 'when will_save_change_to_departments is truthy' do
      subject { build(:user, department_ids: [department.id], will_save_change_to_departments: true) }

      it 'updates the current departments attribute' do
        expect{ subject.save }.to change{ subject.current_departments }.from([]).to([department.name])
      end
    end

    context 'when will_save_change_to_departments is falsey' do
      subject { build(:user, department_ids: [department.id], will_save_change_to_departments: false) }

      it 'does not update the current departments attribute' do
        expect{ subject.save }.not_to change{ subject.current_departments }
      end
    end
  end

  describe 'generate_token_v2' do
    let(:user) { create(:user) }

    it 'generates a token' do
      expect(user.authorization_token).to be_present
    end
  end

  describe 'normalizes' do
    it 'normalizes record' do
      user = create(:user, 
        name: '  User Test  ',
        ddi: ' 55 ',
        ddd: '  11 ',
        phone: '  999999999 ',
        email: '  <EMAIL> ',
      )

      expect(user.name).to eq('User Test')
      expect(user.ddi).to eq('55')
      expect(user.ddd).to eq('11')
      expect(user.phone).to eq('999999999')
      expect(user.email).to eq('<EMAIL>')
    end
  end
end
