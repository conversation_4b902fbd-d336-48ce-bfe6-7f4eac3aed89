require 'rails_helper'

RSpec.describe AnswerProcessing, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:bulk_saving_answer) }
  end
  describe 'enums' do
    it do
      is_expected.to define_enum_for(:status).with_values(
        created: 0,
        processing: 1,
        done: 2,
        failed: 3
      )
    end
  end
  describe 'sanitize_data' do
    let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies) }
    let(:answer_processing) { create(:answer_processing, bulk_saving_answer: bulk_saving_answer, data: data) }
    subject { answer_processing.data }

    context 'for upload field' do
      let(:data) { { field.id => ['http://imageurl1.com.br','http://imageurl2.com.br'] } }
      let(:field) { create(:field, :with_dependencies, type: :upload) }

      before do
        create(:step_template, step: bulk_saving_answer.step, template: field.template)
      end

      it { is_expected.to eq(field.id => ['http://imageurl1.com.br', 'http://imageurl2.com.br']) }
      context 'when field value is empty' do
        let(:data) { { field.id => '' } }
        it { is_expected.to eq({}) }
      end
      context 'when field value is undefined' do
        let(:data) { {} }
        it { is_expected.to eq({}) }
      end
    end

    context 'for multiple field' do
      let(:data) { { field.id => ['value1','value2'] } }
      let(:field) { create(:field, :with_dependencies, type: :multiple) }

      before do
        create(:step_template, step: bulk_saving_answer.step, template: field.template)
      end

      it { is_expected.to eq(field.id => %w[value1 value2]) }
      context 'when field value is empty' do
        let(:data) { { field.id => '' } }
        it { is_expected.to eq({}) }
      end
      context 'when field value is array' do
        let(:data) { { field.id => %w[value1 value2] } }
        it { is_expected.to eq(field.id => %w[value1 value2]) }
      end
      context 'when field value is undefined' do
        let(:data) { {} }
        it { is_expected.to eq({}) }
      end
      context 'when field does not exist in step' do
        let(:data) { { 'non-existent-field-id' => 'some value' } }
        it { is_expected.to eq({}) }
      end
    end

    context 'edge cases' do
      let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies) }
      let(:answer_processing) { create(:answer_processing, bulk_saving_answer: bulk_saving_answer, data: data) }

      context 'when EMPTY_VALUE_KEY is used for non-existent field' do
        let(:data) { { 'non-existent-field-id' => '*empty*' } }
        it 'removes the field without error' do
          expect { answer_processing.data }.not_to raise_error
          expect(answer_processing.data).to eq({})
        end
      end
    end
  end

  describe '.sanitize_payload' do
    let(:step) { create(:step, :with_dependencies) }
    let(:template) { create(:template) }
    let(:text_field) { create(:field, :with_dependencies, template: template, type: :text) }
    before { create(:step_template, step: step, template: template) }
    let(:fields_by_id) { AnswerProcessing.fields_map_for(step) }

    it 'maps EMPTY_VALUE_KEY to field.empty_value for text fields' do
      payload = { text_field.id => AnswerProcessing::EMPTY_VALUE_KEY }
      sanitized = AnswerProcessing.sanitize_payload(payload, fields_by_id)
      expect(sanitized[text_field.id]).to eq(text_field.empty_value)
    end

    it 'parses array-like JSON strings for multiple fields and drops invalid JSON' do
      multiple_field = create(:field, :with_dependencies, type: :multiple)
      create(:step_template, step: step, template: multiple_field.template)

      payload = {
        "#{multiple_field.id}": '["a","b"]',
        unknown: '["x"]',
        "#{text_field.id}": ''
      }

      sanitized = AnswerProcessing.sanitize_payload(payload, AnswerProcessing.fields_map_for(step))

      expect(sanitized[multiple_field.id]).to eq(%w[a b])
      expect(sanitized).not_to have_key('unknown')
      expect(sanitized).not_to have_key(text_field.id)
    end

    it 'preserves blank values equal to field.empty_value when preserve_blank_empty is true' do
      payload = { text_field.id => '' }
      sanitized = AnswerProcessing.sanitize_payload(payload, fields_by_id, preserve_blank_empty: true)
      expect(sanitized[text_field.id]).to eq(text_field.empty_value)
    end
  end

  describe 'editing_data' do
    let(:step) { create(:step, :with_dependencies) }
    let(:template) { create(:template) }
    let(:field) { create(:field, :with_dependencies, template: template, type: :text) }
    before { create(:step_template, step: step, template: template) }
    let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies, step: step) }
    let(:answer_processing) { create(:answer_processing, bulk_saving_answer: bulk_saving_answer, data: values) }
    let(:values) { { field.id => 'test_value' } }

    subject { answer_processing.editing_data }
    context 'when answer_processing.data has string value' do
      let(:values) { { field.id => 'farofa' } }
      it { is_expected.to eq({ field.id => 'farofa' }) }
    end
    context 'when answer_processing.data has array value' do
      let(:values) { { field.id => %w[farofa frango] } }
      it { is_expected.to eq({ field.id => ['farofa','frango'].to_s }) }
    end
  end
end
