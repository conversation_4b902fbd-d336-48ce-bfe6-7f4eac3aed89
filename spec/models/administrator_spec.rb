require 'rails_helper'

RSpec.describe Administrator, type: :model do
  describe 'token_validation_response' do
    subject { create(:administrator).token_validation_response }

    it 'returns subdomain' do
      is_expected.to include(tenant: Apartment::Tenant.current)
    end
  end

  describe 'validations' do
    describe 'validate_updating_email' do
      context 'when provider is email' do
        let(:administrator) { create(:administrator) }

        it 'is valid' do
          expect(administrator.update(email: '<EMAIL>')).to be_truthy
        end
      end

      context 'when provider is google_oauth2' do
        let(:administrator) { create(:administrator, provider: 'google_oauth2', uid: '134687651385764654') }

        it 'is invalid' do
          expect(administrator.update(email: '<EMAIL>')).to be_falsy
          expect(administrator.errors.full_messages).to eq([I18n.t('updating_email', scope: 'devise.oauth')])
        end
      end
    end

    describe 'validate_email_domain_valid' do
      let(:auth_domain) { ['coyo.com.br'] }

      before { Company.current.update!(auth_domain: auth_domain, enable_google_oauth: true) }

      context 'when user.provider is email' do
        context 'and auth_domain contains email domain' do
          subject { build(:administrator, email: '<EMAIL>') }

          it { is_expected.to be_invalid }

          it 'has messages' do
            subject.valid?

            expect(subject.errors.full_messages).to eq(['O domínio coyo.com.br está cadastrado como domínio de login via Google Oauth e não pode ser utilizado para login com email e senha'])
          end
        end

        context 'and auth_domain does not contain email domain' do
          subject { build(:administrator, email: '<EMAIL>') }

          it { is_expected.to be_valid }
        end
      end

      context 'when user.provider is google_oauth2' do
        context 'and auth_domain contains email domain' do
          subject { build(:administrator, email: '<EMAIL>', provider: 'google_oauth2', uid: '123546876345354') }

          it { is_expected.to be_valid }
        end

        context 'and auth_domain does not contain email domain' do
          subject { build(:administrator, email: '<EMAIL>', provider: 'google_oauth2', uid: '123546876345354') }

          it { is_expected.to be_valid }
        end
      end
    end
  end

  describe '#active_for_authentication?' do
    let(:administrator) { create(:administrator) }

    subject { administrator.active_for_authentication? }

    context 'when administrator is active' do
      it { is_expected.to be true }
    end

    context 'when administrator is deleted' do
      let(:administrator) { create(:administrator, deleted_at: Time.zone.now) }

      it { is_expected.to be false }
    end
  end

  describe '.send_email_requesting_approvement' do
    context 'on create' do
      context 'when approved is false' do
        let(:mailer) { double(:mailer, deliver_later: true) }
        it 'sends email' do
          expect(UserApprovementMailer).to receive(:request_approvement_for_administrator).and_return(mailer)

          create(:administrator, approved: false)
        end
      end

      context 'when approved is true' do
        it 'does not send email' do
          expect(UserApprovementMailer).to_not receive(:request_approvement_for_administrator)

          create(:administrator, approved: true)
        end
      end
    end
  end

  context '.send_email_when_user_is_approved' do
    context 'on update' do
      context 'when approved was true' do
        before { @user = create(:administrator, approved: true) }

        context 'and approved is false' do
          it 'sends email' do
            expect(UserApprovementMailer).to_not receive(:administrator_approved)

            @user.update(approved: false)
          end
        end

        context 'and approved is true' do
          it 'does not send email' do
            expect(UserApprovementMailer).to_not receive(:administrator_approved)

            @user.update(approved: true)
          end
        end
      end

      context 'when approved was false' do
        before { @user = create(:administrator, approved: false) }

        context 'and approved is false' do
          it 'sends email' do
            expect(UserApprovementMailer).to_not receive(:administrator_approved)
            @user.update(approved: false)
          end
        end

        context 'and approved is true' do
          let(:mailer) { double(:mailer, deliver_later: true) }
          it 'does not send email' do
            expect(UserApprovementMailer).to receive(:administrator_approved).and_return(mailer)

            @user.update(approved: true)
          end
        end
      end
    end
  end

  context 'normalize' do
    it 'normalize record' do
      administrator = create(:administrator, name: '  Adm Test  ', email: '  <EMAIL> ')
      expect(administrator.name).to eq('Adm Test')
      expect(administrator.email).to eq('<EMAIL>')
    end
  end
end
