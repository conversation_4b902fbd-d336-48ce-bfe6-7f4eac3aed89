require 'rails_helper'

RSpec.describe Business, type: :model do
  let(:business_group) { create(:business_group) }

  subject { create(:business, business_group: business_group) }

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to be_valid }

    describe 'normalize' do
      it 'normalize record' do
        business = create(:business, name: "   Test Name   ", business_group: business_group)
        expect(business.name).to eq('Test Name')
      end
    end

    context 'updating sub_business ' do
      it { expect { subject.update!(sub_business: true) }.to raise_error 'A validação falhou: Sub negócio não pode ser alterado' }
    end

    it 'fails if there is more than 5 key fields' do
      field = create(:field, :with_dependencies)

      key_fields = create_list(:field, 6, template: field.template)

      expect(build(:business, :with_dependencies, key_fields: key_fields)).to be_invalid
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:business_group) }
    it { is_expected.to have_many(:contents) }
    it { is_expected.to have_many(:steps).order(Arel.sql('deleted_at NULLS FIRST, "steps"."order" ASC')) }
    it { is_expected.to have_many(:active_steps).conditions(deleted_at: nil).order(order: :asc).class_name('Step') }
    it { is_expected.to have_many(:answers).through(:contents) }
    it { is_expected.to have_many(:answer_versions).through(:answers).source(:versions) }
  end

  describe '.with_permission_for_user' do
    let(:user1) { create(:user) }
    let(:user2) { create(:user) }
    let(:step1) { create(:step, :with_dependencies) }
    let(:step2) { create(:step, :with_dependencies) }

    subject { Business.with_permission_for_user(user1).pluck(:id) }

    context 'when user has step_permission' do
      before do
        create(:step_permission, user: user1, step: step1)
        create(:step_permission, user: user2, step: step2)
      end

      it { is_expected.to eq([step1.business_id]) }
    end
  end

  context 'after add key field' do
    let!(:field) { create(:field, :with_dependencies, order: 0, required: true, type: :text) }
    let!(:step) { Step.find_by(business_id: business.id) }
    let!(:business) { create(:business, :with_dependencies, :with_step, deleted_at: Time.zone.now) }
    let(:index_name) { "answers_#{business.id[0..7]}_#{business.id[24..]}_#{Digest::MD5.hexdigest(index_fields.join('_'))}" }
    let(:first_step_id) { business.active_steps&.first&.id }
    let(:key_fields_ids) { business.key_fields.pluck(:id) }
    let(:index_fields) { key_fields_ids.map { |field_id| "(data->'values'->>'#{field_id}')" } }
    let(:index_definition) { business.get_index_definition(index_name, index_fields, first_step_id) }

    before do
      business.key_fields << field
    end

    it 'enqueues the index creation' do
      expect(BusinessKeyFieldIndexWorker).to receive(:perform_async).with(Apartment::Tenant.current, index_name, index_definition)

      business.save!
    end
  end

  describe '#parent' do
    context 'when there is a field configured for subbusiness' do
      let(:business) { create(:business, :with_dependencies, :with_step) }

      let!(:field_subbusiness) { create(:field, :with_dependencies, order: 1, required: true, type: :sub_business, reference_sub_business_id: sub_business_step.business.id) }
      let(:sub_business) { create(:business, :with_dependencies, sub_business: true) }
      let(:sub_business_step) { create(:step, :with_field, business: sub_business) }
      let(:sub_business_field) { sub_business_step.templates.first.fields.first }
      let(:sub_business_params) { { sub_business_field.id => 'subvalue1' } }
      let(:user) { create(:user) }

      subject { sub_business.parent }

      before do
        StepTemplate.create(step_id: business.steps.first.id, template_id: field_subbusiness.template_id)
      end

      it { is_expected.to eq business }

      context 'but field is deleted' do
        before { field_subbusiness.discard }

        it { is_expected.to be_nil }
      end
    end
  end

  describe 'after_discard' do
    context 'when there is a field configured for subbusiness' do
      let(:business) { create(:business, :with_dependencies, :with_step) }

      let!(:field_subbusiness) { create(:field, :with_dependencies, order: 1, required: true, type: :sub_business, reference_sub_business_id: sub_business_step.business.id) }
      let(:sub_business) { create(:business, :with_dependencies, sub_business: true) }
      let(:sub_business_step) { create(:step, :with_field, business: sub_business) }
      let(:sub_business_field) { sub_business_step.templates.first.fields.first }
      let(:sub_business_params) { { sub_business_field.id => 'subvalue1' } }
      let(:user) { create(:user) }

      subject { sub_business.parent }

      before do
        StepTemplate.create(step_id: business.steps.first.id, template_id: field_subbusiness.template_id)
      end

      it 'discards the sub business' do
        subject.discard

        expect(sub_business.reload).to be_discarded
      end
    end
  end

  describe 'after_undiscard' do
    context 'when there is a field configured for subbusiness' do
      let(:business) { create(:business, :with_dependencies, :with_step, deleted_at: Time.zone.now) }

      let!(:field_subbusiness) { create(:field, :with_dependencies, order: 1, required: true, type: :sub_business, reference_sub_business_id: sub_business_step.business.id) }
      let(:sub_business) { create(:business, :with_dependencies, sub_business: true, deleted_at: Time.zone.now) }
      let(:sub_business_step) { create(:step, :with_field, business: sub_business) }
      let(:sub_business_field) { sub_business_step.templates.first.fields.first }
      let(:sub_business_params) { { sub_business_field.id => 'subvalue1' } }
      let(:user) { create(:user) }

      subject { sub_business.parent }

      before do
        StepTemplate.create(step_id: business.steps.first.id, template_id: field_subbusiness.template_id)
      end

      it 'undiscards the sub business' do
        subject.undiscard

        expect(sub_business.reload).to be_undiscarded
      end
    end
  end

  describe 'after_update' do
    context 'when there is a field configured for subbusiness' do
      context 'when undiscards the sub_business' do
        let(:business) { create(:business, :with_dependencies, :with_step, deleted_at: Time.zone.now) }

        let!(:field_subbusiness) { create(:field, :with_dependencies, order: 1, required: true, type: :sub_business, reference_sub_business_id: sub_business_step.business.id) }
        let(:sub_business) { create(:business, :with_dependencies, sub_business: true, deleted_at: Time.zone.now) }
        let(:sub_business_step) { create(:step, :with_field, business: sub_business) }
        let(:sub_business_field) { sub_business_step.templates.first.fields.first }
        let(:sub_business_params) { { sub_business_field.id => 'subvalue1' } }
        let(:user) { create(:user) }

        subject { sub_business.parent }

        before do
          StepTemplate.create(step_id: business.steps.first.id, template_id: field_subbusiness.template_id)
        end

        it 'undiscards the parent' do
          subject.discard
          data = subject.reload.deleted_at

          expect { sub_business.undiscard }.to change { subject.reload.deleted_at }.from(data).to(nil)
        end
      end
    end
  end

  describe 'translated_attribute' do
    let(:business) { create(:business, business_group: business_group) }
    let(:attribute) { %w[description help_url name].sample }
    let!(:en) { create(:translation, language: :english, attribute_name: attribute, actable: business) }
    let!(:es) { create(:translation, language: :spanish, attribute_name: attribute, actable: business) }
    let!(:pt) { create(:translation, language: :portuguese, attribute_name: attribute, actable: business) }

    before { Company.current.update! enable_internationalization: true }

    context 'when I18n locale is set to english' do
      before { I18n.locale = 'en' }

      it 'returns the english attribute translation' do
        expect(business.translated_attribute(attribute)).to eq(en.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(business.translated_attribute(attribute, true)).to eq(business[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(business.translated_attribute(attribute)).to eq(business[attribute])
        end
      end
    end

    context 'when I18n locale is set to spanish' do
      before { I18n.locale = 'es' }

      it 'returns the spanish attribute translation' do
        expect(business.translated_attribute(attribute)).to eq(es.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(business.translated_attribute(attribute, true)).to eq(business[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(business.translated_attribute(attribute)).to eq(business[attribute])
        end
      end
    end

    context 'when I18n locale is set to portuguese' do
      before { I18n.locale = 'pt-BR' }

      it 'returns the brazilian portuguese attribute translation' do
        expect(business.translated_attribute(attribute)).to eq(pt.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(business.translated_attribute(attribute, true)).to eq(business[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(business.translated_attribute(attribute)).to eq(business[attribute])
        end
      end
    end
  end
end
