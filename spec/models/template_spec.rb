require 'rails_helper'

RSpec.describe Template, type: :model do
  subject { create(:template) }
  let(:business) { create(:business, business_group: create(:business_group)) }
  let(:step) { create(:step, business: business) }

  describe 'associations' do
    it { is_expected.to have_many(:fields).order(Arel.sql('fields.deleted_at NULLS FIRST, "fields"."order" ASC, "fields"."updated_at" DESC')) }
    it { is_expected.to have_many(:active_fields).conditions(deleted_at: nil).order(order: :asc, updated_at: :desc).class_name('Field') }
    it { is_expected.to have_many(:deleted_fields).conditions('deleted_at not null').order(order: :asc, updated_at: :desc).class_name('Field') }
    it { is_expected.to have_many(:step_templates).dependent(:destroy) }
    it { is_expected.to have_many(:steps).through(:step_templates) }
    it { is_expected.to have_many(:businesses).through(:steps) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to be_valid }
  end

  describe 'normalize' do
    it 'normalize record' do
      template = create(:template, name: "Test Name", description: "   Test Description   ", variable: "   Test Variable   ")
      
      expect(template.name).to eq("Test Name")
      expect(template.description).to eq("Test Description")
      expect(template.variable).to eq("Test Variable")
    end
  end

  describe '#has_fields?' do
    before do
      @template = create(:template)
    end

    it 'return false if the template does not have any field' do
      expect(@template.has_fields?).to be_falsy
    end

    it 'return false if the template does not have any active field' do
      create(:field, template: @template, deleted_at: Time.zone.now)

      expect(@template.has_fields?).to be_falsy
    end

    it 'returns true if the template has at least one active field' do
      create(:field, template: @template)

      expect(@template.has_fields?).to be_truthy
    end
  end

  describe 'before destroying' do
    context 'when the template is not in use' do
      it 'allows the deletion' do
        expect do
          subject.discard
        end.to change(Template.discarded, :count).by(1)
      end
    end

    context 'when the template is in use' do
      before do
        create(:step_template, step: step, template: subject)
      end

      it 'blocks the deletion' do
        expect do
          subject.discard
        end.to_not change(Template.discarded, :count)
      end

      it 'blocks the deletion' do
        expect do
          subject.destroy
        end.to_not change(Template, :count)
      end
    end
  end

  describe 'translated_attribute' do
    let(:template) { create(:template) }
    let(:attribute) { ['description','name','variable'].sample }
    let!(:en) { create(:translation, language: :english, attribute_name: attribute, actable: template)}
    let!(:es) { create(:translation, language: :spanish, attribute_name: attribute, actable: template)}
    let!(:pt) { create(:translation, language: :portuguese, attribute_name: attribute, actable: template)}

    before { Company.current.update! enable_internationalization: true }

    context 'when I18n locale is set to english' do
      before { I18n.locale = 'en' }

      it 'returns the english attribute translation' do
        expect(template.translated_attribute(attribute)).to eq(en.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(template.translated_attribute(attribute, true)).to eq(template[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(template.translated_attribute(attribute)).to eq(template[attribute])
        end
      end
    end

    context 'when I18n locale is set to spanish' do
      before { I18n.locale = 'es' }

      it 'returns the spanish attribute translation' do
        expect(template.translated_attribute(attribute)).to eq(es.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(template.translated_attribute(attribute, true)).to eq(template[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(template.translated_attribute(attribute)).to eq(template[attribute])
        end
      end
    end

    context 'when I18n locale is set to portuguese' do
      before { I18n.locale = 'pt-BR' }

      it 'returns the brazilian portuguese attribute translation' do
        expect(template.translated_attribute(attribute)).to eq(pt.translated_text)
      end

      context 'but it was asked to skipt translation' do
        it 'returns the original attribute value' do
          expect(template.translated_attribute(attribute, true)).to eq(template[attribute])
        end
      end

      context 'but enable_internationalization is set to false' do
        before { Company.current.update! enable_internationalization: false }

        it 'returns the original attribute value' do
          expect(template.translated_attribute(attribute)).to eq(template[attribute])
        end
      end
    end
  end
end
