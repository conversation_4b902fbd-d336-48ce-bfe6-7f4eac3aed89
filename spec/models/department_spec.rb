require 'rails_helper'

RSpec.describe Department, type: :model do
  subject { build(:department) }

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }

    it { is_expected.to be_valid }
  end

  describe 'normalize' do
    it 'normalizes record' do
      department = Department.new(name: '  Engineering  ')
      expect(department.name).to eq('Engineering')
    end
  end

  describe 'associations' do
    it { is_expected.to have_and_belong_to_many(:users) }
    it { is_expected.to have_many(:permissions).dependent(:destroy).class_name('StepPermission') }
  end
end
