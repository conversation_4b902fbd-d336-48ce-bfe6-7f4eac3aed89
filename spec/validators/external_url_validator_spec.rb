# == Schema Information
#
# Table name: answers
#
#  id          :uuid             not null, primary key
#  data        :jsonb
#  content_id  :uuid
#  step_id     :uuid
#  user_id     :uuid
#  deleted_at  :datetime
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  order       :integer          not null
#  status      :integer          default("pending"), not null
#  name        :string           not null
#  description :text
#

require 'rails_helper'

RSpec.describe ExternalUrlValidator, type: :validator do
  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group: business_group, enable_validation_web_service: true) }
  let(:content) { create(:content, business: business) }
  let(:step) { create(:step, business: business, validation_url: validation_url) }
  let(:user) { create(:user) }
  let(:validation_url) { 'http://foo.bar.com/api/validate' }
  let(:answer_xml) { '<?xml version="1.0" encoding="UTF-8" ?><fourmdg tenant="demo"></fourmdg>' }
  let(:response_xml) { '<?xml version="1.0" encoding="UTF-8" ?><fourmdg tenant="demo"><validation success="true"></validation></fourmdg>' }
  let(:xml_builder) { double(:xml_builder, generate: answer_xml) }

  before do
    allow(ValidationXmlBuilder).to receive(:new).and_return(xml_builder)
    stub_request(:post, /.+/).to_return(status: 200, body: response_xml.to_s)
  end

  describe '#validate' do
    let(:field_id) { SecureRandom.uuid }
    let(:subject) { build(:answer, :completed, content: content, step: step, user: user, values: values) }
    let(:values) { { field_id => 'a' * 39 } }

    it 'initializes the xml builder' do
      expect(ValidationXmlBuilder).to receive(:new).with(subject, subject.user)

      subject.valid?
    end

    it 'generates the xml for validation' do
      expect(xml_builder).to receive(:generate)

      subject.valid?
    end

    it 'issues a POST to the validation url' do
      internet_instance = instance_double(Async::HTTP::Internet)
      response_mock = instance_double('Response', status: 200, read: '<validation success="true"></validation>', close: nil)

      allow(Async::HTTP::Internet).to receive(:new).and_return(internet_instance)
      allow(internet_instance).to receive(:close)

      expected_headers = [
        ['env', Rails.env],
        ['tenant', Apartment::Tenant.current],
        ['category', 'validation'],
        ['Content-Type', 'application/xml'],
        ['Accept', 'application/xml']
      ]

      expect(internet_instance).to receive(:post).with(
        validation_url,
        expected_headers,
        kind_of(String)
      ).and_return(response_mock)

      subject.valid?
    end

    context 'enable_validation_web_service is false' do
      it 'does not initializes the xml builder' do
        business.enable_validation_web_service = false

        expect(ValidationXmlBuilder).not_to receive(:new).with(subject, subject.user)

        subject.valid?
      end
    end

    context 'with success' do
      it { is_expected.to be_valid }
    end

    context 'with error' do
      let(:response_xml) { '<?xml version="1.0" encoding="UTF-8" ?><fourmdg tenant="demo"><validation success="false"><error>Foo</error><error>Bar</error></validation></fourmdg>' }

      it { is_expected.to be_invalid }

      it 'adds the error messages' do
        subject.valid?

        expect(subject.errors[:base]).to match_array(['Foo, Bar'])
      end

      context 'when processing a bulk saving answer with enable_validation_url = false' do
        let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies, enable_validation_url: false) }

        subject { create(:answer, content: content, step: step, user: user) }

        before do
          create(:answer_processing, bulk_saving_answer: bulk_saving_answer, answer_id: subject.id, status: :processing)

          subject.assign_attributes(status: :done, values: values, filled_at: Time.zone.now, first_fill_at: Time.zone.now, concluded_at: Time.zone.now)
        end

        it { is_expected.to be_valid }
      end

      context 'when done a bulk saving answer with enable_validation_url = false' do
        let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies, enable_validation_url: false) }

        subject { create(:answer, content: content, step: step, user: user) }

        before do
          create(:answer_processing, bulk_saving_answer: bulk_saving_answer, answer_id: subject.id, status: :done)

          subject.assign_attributes(status: :done, values: values, filled_at: Time.zone.now, concluded_at: Time.zone.now)
        end

        it { is_expected.to be_invalid }
      end

      context 'with Net::ReadTimeout, Net::NetTimeout' do
        before do
          stub_request(:post, /.+/).to_timeout
        end

        it { is_expected.to be_invalid }

        it 'adds the error messages' do
          subject.valid?

          expect(subject.errors[:base]).to match_array(["Timeout na comunicação com a URL de validação #{validation_url}"])
        end
      end

      context 'with 400 error' do
        before do
          stub_request(:post, /.+/).to_return(status: 400, body: 'error', headers: { content_type: 'application/xml' })
        end

        it { is_expected.to be_invalid }

        it 'adds the error messages' do
          subject.valid?
          expect(subject.errors[:base]).to match_array(["Não foi possível comunicar com a URL de validação #{validation_url} - Código: 400"])
        end
      end

      context 'with StandardError' do
        before do
          stub_request(:post, /.+/).to_raise(StandardError.new('error'))
        end

        it { is_expected.to be_invalid }

        it 'adds the error messages' do
          subject.valid?

          expect(subject.errors[:base]).to match_array(["Não foi possível comunicar com a URL de validação #{validation_url} - Mensagem: error"])
        end
      end

      context 'when response code is 400' do
        before do
          stub_request(:post, /.+/).to_return(status: 400, body: response_xml, headers: { content_type: 'application/xml' })
        end
        let(:response_xml) { '<?xml version="1.0" encoding="UTF-8" ?><fourmdg tenant="demo"><validation success="false"></validation></fourmdg>' }

        it { is_expected.to be_invalid }

        it 'adds the error messages' do
          subject.valid?

          expect(subject.errors[:base]).to match_array(["Não foi possível comunicar com a URL de validação #{validation_url} - Código: 400"])
        end
      end
    end
  end
end
