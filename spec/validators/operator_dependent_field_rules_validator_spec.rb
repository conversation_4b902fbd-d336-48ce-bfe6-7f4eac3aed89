require 'rails_helper'

RSpec.describe OperatorDependentFieldRulesValidator, type: :validator do
  describe '#valid?' do
    context 'when rules are present' do
      it 'returns true if all rules have an operator' do
        hash = { 'rules' => { 'rules' => [{ 'operator' => 'AND' }, { 'operator' => 'OR' }] } }
        validator = OperatorDependentFieldRulesValidator.new(hash)
        expect(validator.valid?).to be true
      end

      it 'returns false if any rule is missing an operator' do
        hash = { 'rules' => { 'rules' => [{ 'operator' => 'AND' }, { 'field' => 'name' }] } }
        validator = OperatorDependentFieldRulesValidator.new(hash)
        expect(validator.valid?).to be false
      end
    end

    context 'when rules are not present' do
      it 'returns false' do
        hash = {}
        validator = OperatorDependentFieldRulesValidator.new(hash)
        expect(validator.valid?).to be false
      end
    end

    context 'greater and lower with array' do
      it 'returns false if the operator is greater and the value is an array' do
        hash = { 'rules' => { 'rules' => [{ 'operator' => 'greater', 'value' => ['1', '2'] }] } }
        validator = OperatorDependentFieldRulesValidator.new(hash)
        expect(validator.valid?).to be false
      end

      it 'returns false if the operator is lower and the value is an array' do
        hash = { 'rules' => { 'rules' => [{ 'operator' => 'lower', 'value' => ['1', '2'] }] } }
        validator = OperatorDependentFieldRulesValidator.new(hash)
        expect(validator.valid?).to be false
      end
    end
  end
end
