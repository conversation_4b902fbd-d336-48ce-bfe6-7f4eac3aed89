require 'rails_helper'

RSpec.describe BusinessValidationRulesRunner, type: :service do
  describe 'initialize' do
    let(:business) { create(:business, :with_dependencies, apply_field_validation_rule: true) }
    let(:content) { create(:content, business: business) }
    let(:step) { create(:step, name: 'step', business: business) }
    let(:template) { create(:template) }

    let(:rule_field) { create(:field, template: template, type: :text, label: 'Altura em cm') }
    let(:validation_field) { create(:field, template: template, type: :text, label: 'Valor') }

    let(:rules) do
      {
        condition: 'and',
        rules: [{ field: "#{step.id}:#{rule_field.id}", operator: 'equals', value: '172' }]
      }
    end
    let(:rule_actions) do
      [
        { field: "#{step.id}:#{validation_field.id}", value: '172', operator: 'equals', error_message: 'Error' }
      ]
    end

    let(:values) { { "#{step.id}:#{rule_field.id}" => '172', "#{step.id}:#{validation_field.id}" => '172' } }
    let(:answer) { create(:answer, user: create(:user), position: 1, content: content, step: step, values: values) }
    let(:service) { described_class.new(answer) }

    before do
      create(:step_template, step: step, template: template, order: 0)
      create(:dependent_field_rule, business_id: business.id, rule_type: :validation, rules: rules, rule_actions: rule_actions)
    end

    describe 'returns true if the answer is valid' do
      it 'validates the answer correctly' do
        expect(service.errors).to eq([])
      end
    end

    describe 'returns false if the answer is invalid' do
      let(:values) { { "#{step.id}:#{rule_field.id}" => '172' } }

      it 'does not validate the answer correctly' do
        expect(service.errors).to eq(['Error'])
      end
    end

    describe "validate conditions with array values" do
      let(:field) { create(:field, template: template, type: :text, label: 'Teste') }
      describe "lower" do
        it 'with lower value' do
          condition = {
            "field" => "#{step.id}:#{field.id}",
            "operator" => 'lower', "value" => 500
          }
          values = { "#{step.id}:#{field.id}" => [200, 300, 400] }
          answer = create(:answer, user: create(:user), position: 1, content: content, step: step, values: values)
          
          business_validation_rules_runner = BusinessValidationRulesRunner.new(
            answer
          )
          expect(business_validation_rules_runner.condition_match?(condition)).to eq(true)
        end

        it 'with greater value' do
          condition = {
            "field" => "#{step.id}:#{field.id}",
            "operator" => 'lower', "value" => 500
          }
          values = { "#{step.id}:#{field.id}" => [600, 700, 800] }
          answer = create(:answer, user: create(:user), position: 1, content: content, step: step, values: values)
          
          business_validation_rules_runner = BusinessValidationRulesRunner.new(
            answer
          )
          expect(business_validation_rules_runner.condition_match?(condition)).to eq(false)
        end
      end

      describe "greater" do
        it 'with greater value' do
          condition = {
            "field" => "#{step.id}:#{field.id}",
            "operator" => 'greater', "value" => 500
          }
          values = { "#{step.id}:#{field.id}" => [600, 700, 800] }
          answer = create(:answer, user: create(:user), position: 1, content: content, step: step, values: values)
          
          business_validation_rules_runner = BusinessValidationRulesRunner.new(
            answer
          )
          expect(business_validation_rules_runner.condition_match?(condition)).to eq(true)
        end

        it 'with lower value' do
          condition = {
            "field" => "#{step.id}:#{field.id}",
            "operator" => 'greater', "value" => 500
          }
          values = { "#{step.id}:#{field.id}" => [300, 400, 200] }
          answer = create(:answer, user: create(:user), position: 1, content: content, step: step, values: values)
          
          business_validation_rules_runner = BusinessValidationRulesRunner.new(
            answer
          )
          expect(business_validation_rules_runner.condition_match?(condition)).to eq(false)
        end
      end
    end
  end
end
