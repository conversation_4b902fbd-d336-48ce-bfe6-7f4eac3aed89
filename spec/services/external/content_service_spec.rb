require 'rails_helper'

RSpec.describe External::ContentService, type: :service do
  before do
    @business_group = create(:business_group, id: 'c88ec160-544d-4c8f-948f-817c7396ee05')
    @business = create(:business, id: 'f166e6f0-b856-470f-8b02-33993eb474d9', business_group: @business_group)

    @step1 = create(:step, id: '9e00d360-2206-4470-af09-fc2ded47b30c', business: @business, name: 'general')
    @template1 = create(:template, id: '3af2c527-c817-4a1d-a2c0-5309cda9c123')
    @step1.templates << @template1
    @field1 = create(:field, id: '584a2d29-d348-45e9-939e-6ed4043feaa7', template: @template1)
    @field2 = create(:field, id: 'd2b50438-46a3-44e0-8ac3-c7619c812d0d', template: @template1)

    @step2 = create(:step, id: '0a9735f8-dbd5-48fa-8fc4-05f110a69699', business: @business, name: 'detail')
    @template2 = create(:template, id: '6dc0c5da-777b-4605-a2bf-d67f6196a456')
    @step2.templates << @template2
    @field3 = create(:field, id: 'ca034779-04b1-42ca-94d4-ee8d9c233f5d', template: @template2)
    @field4 = create(:field, id: '9f568f27-9361-4591-9896-d96b7681ed46', template: @template2)
  end

  describe '#save' do
    let(:service) { described_class.new(parameters, remote_ip) }
    let(:remote_ip) { '127.0.0.1' }

    context 'with invalid file' do
      context 'error on save' do
        let(:parameters) { file_fixture('invalid_file.txt').read }

        it 'returns false' do
          service.save

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.save

          expect(service.errors).to include('O documento XML enviado é inválido')
        end
      end

      context 'when business is not present' do
        let(:parameters) { file_fixture('xml_for_creation.xml').read }
        let(:business_nil) { double(:business_nil, find_by: nil, index_by: {}) }
        let(:business_not_sub_business_scope) { double(where: business_nil) }

        before do
          allow(Business).to receive(:not_sub_business).and_return(business_not_sub_business_scope)
          allow(Business).to receive(:find_by).and_return(nil)
          allow(Business).to receive(:where).and_return(business_nil)
        end

        it 'returns false' do
          service.save

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.save

          expect(service.errors).to include('Não foi possível encontrar o négocio f166e6f0-b856-470f-8b02-33993eb474d9')
        end
      end

      context 'any general error' do
        let(:parameters) { file_fixture('xml_for_creation.xml').read }

        before do
          allow(Business).to receive(:where).and_raise('error message')
        end

        it 'returns false' do
          service.save

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.save

          expect(service.errors).to include('error message')
        end
      end
    end

    context 'with valid file' do
      context 'when the xml does not contain the content id' do
        let(:parameters) { file_fixture('xml_for_creation.xml').read }
        let(:content) { create(:content, business: @business, draft: true, status: :pending, id: 'bcc82074-eec3-427e-9fcf-0abc0328491d') }
        let(:content_service) { double(:content_service, create: true, record: content, success: true) }

        before do
          @answer1 = create(:answer, step: @step1, position: 0, content: content, status: :pending, values: nil, available_at: Time.zone.now)
          @answer2 = create(:answer, step: @step2, position: 1, content: content, status: :pending, values: nil, available_at: Time.zone.now)

          allow(ContentService).to receive(:new).and_return(content_service)
        end

        it 'initializes the content service' do
          expect(ContentService).to receive(:new).with(business_id: @business.id.to_s, parent_id: nil, created_by_id: 'ae73effb-a312-4b2f-9853-5d05bd05e6aa', remote_ip: remote_ip).and_return(content_service)

          service.save
        end

        context 'when content has created_by_id attribute' do
          let(:parameters) do
            xml = file_fixture('xml_for_creation.xml').read
            xml.gsub('<content>', '<content created_by_id="specific-user-id">')
          end

          it 'uses the created_by_id from content tag instead of step user' do
            expect(ContentService).to receive(:new).with(business_id: @business.id.to_s, parent_id: nil, created_by_id: 'specific-user-id', remote_ip: remote_ip).and_return(content_service)

            service.save
          end
        end

        context 'when content has created_by attribute' do
          let(:parameters) do
            xml = file_fixture('xml_for_creation.xml').read
            xml.gsub('<content>', '<content created_by="another-user-id">')
          end

          it 'uses the created_by from content tag instead of step user' do
            expect(ContentService).to receive(:new).with(business_id: @business.id.to_s, parent_id: nil, created_by_id: 'another-user-id', remote_ip: remote_ip).and_return(content_service)

            service.save
          end
        end

        it 'creates the content' do
          expect(content_service).to receive(:create).and_return(true)

          service.save
        end

        context 'when content_service has errors' do
          let(:content_service) { double(:content_service, create: false, record: content, success: false, errors: ['one error']) }
          it 'returns errors' do
            service.save
            expect(service.errors).to_not be_empty
          end
        end

        context 'with success creating the content' do
          let(:values_step1) do
            {
              '584a2d29-d348-45e9-939e-6ed4043feaa7' => '3', 'd2b50438-46a3-44e0-8ac3-c7619c812d0d' => 'FOO',
              'content_id' => content.id, 'user_id' => 'ae73effb-a312-4b2f-9853-5d05bd05e6aa', 'remote_ip' => remote_ip, 'skip_verification_url' => true,
              'skip_mandatory_fields' => false, 'skip_business_rules' => false, 'skip_validation_rules' => false, 'skip_field_validations' => false,
              'skip_webhook' => false, 'origin' => Answer.origins[:api], 'steps_to_change_ids' => nil
            }
          end
          let(:values_step2) do
            {
              'ca034779-04b1-42ca-94d4-ee8d9c233f5d' => '4', '9f568f27-9361-4591-9896-d96b7681ed46' => 'BAR',
              'content_id' => content.id, 'user_id' => 'ae73effb-a312-4b2f-9853-5d05bd05e6aa', 'remote_ip' => remote_ip, 'skip_verification_url' => true,
              'skip_mandatory_fields' => false, 'skip_business_rules' => false, 'skip_validation_rules' => false, 'skip_field_validations' => false,
              'skip_webhook' => false, 'origin' => Answer.origins[:api], 'steps_to_change_ids' => nil
            }
          end
          let(:answer_service) { double(:answer_service, update: true, success: true) }

          before do
            allow(answer_service).to receive(:record).and_return(@answer1, @answer2)
            allow(AnswerService).to receive(:new).with(values_step1).and_return(answer_service)
            allow(AnswerService).to receive(:new).with(values_step2).and_return(answer_service)
          end

          it 'initializes the answer service with the xml content' do
            expect(AnswerService).to receive(:new).with(values_step1).and_return(answer_service)
            expect(AnswerService).to receive(:new).with(values_step2).and_return(answer_service)

            service.save
          end

          it 'update the answers' do
            expect(answer_service).to receive(:update).with(@answer1.id).and_return(true)
            expect(answer_service).to receive(:update).with(@answer2.id).and_return(true)

            service.save
          end

          context 'when step has verification_url' do
            let!(:user) { User.new(id: 'ae73effb-a312-4b2f-9853-5d05bd05e6aa') }
            let(:field3) { create(:field, template: @template1) }
            let(:answer_decorated) { double :answer_decorated, values: { field3.id => 'value_from_verification_url_service' } }
            let(:values_step1) do
              {
                '584a2d29-d348-45e9-939e-6ed4043feaa7' => '3',
                'd2b50438-46a3-44e0-8ac3-c7619c812d0d' => 'FOO',
                field3.id => 'value_from_verification_url_service',
                'content_id' => content.id,
                'user_id' => 'ae73effb-a312-4b2f-9853-5d05bd05e6aa',
                'remote_ip' => remote_ip,
                'skip_verification_url' => true,
                'skip_mandatory_fields' => false,
                'skip_field_validations' => false,
                'skip_business_rules' => false,
                'skip_validation_rules' => false,
                'steps_to_change_ids' => nil
              }
            end

            before do
              allow(AnswerDecorator).to receive(:decorate).and_return(answer_decorated, double(values: {}))
              allow(User).to receive(:new).and_return(user)
            end

            it 'sends the user_id to AnswerDecorator' do
              expect(AnswerDecorator).to receive(:decorate) do |_answer, options|
                expect(options[:context][:skip_verification_url]).to be_truthy
                expect(options[:current_user]).to eq(user)
              end

              service.save
            end
          end

          context 'when a field has default value' do
            let(:default_value) { 'this is my default value' }

            before do
              @field4.update! default_value: default_value
            end

            context 'when a value for the field is given' do
              it 'does not overwrite the value' do
                expect(AnswerService).to receive(:new).with(values_step2).and_return(answer_service)

                service.save
              end
            end

            context 'when a value for the field is not given' do
              let(:parameters) { file_fixture('xml_for_creation_without_field_with_default_value.xml').read }
              let(:values_step2) do
                {
                  'ca034779-04b1-42ca-94d4-ee8d9c233f5d' => '4', '9f568f27-9361-4591-9896-d96b7681ed46' => default_value,
                  'content_id' => content.id, 'user_id' => 'ae73effb-a312-4b2f-9853-5d05bd05e6aa', 'remote_ip' => remote_ip, 'skip_verification_url' => true,
                  'skip_mandatory_fields' => false, 'skip_business_rules' => false, 'skip_validation_rules' => false, 'skip_field_validations' => false,
                  'skip_webhook' => false, 'origin' => Answer.origins[:api], 'steps_to_change_ids' => nil
                }
              end

              it 'adds the default value to answer params' do
                expect(AnswerService).to receive(:new).with(values_step2).and_return(answer_service)

                service.save
              end
            end
          end
        end
      end

      context 'when the xml contains the content id' do
        let(:parameters) { file_fixture('xml_for_update.xml').read }
        let(:values_step1) do
          {
            '584a2d29-d348-45e9-939e-6ed4043feaa7' => '3', 'd2b50438-46a3-44e0-8ac3-c7619c812d0d' => 'FOO',
            'content_id' => @content.id, 'user_id' => 'ae73effb-a312-4b2f-9853-5d05bd05e6aa', 'remote_ip' => remote_ip, 'skip_verification_url' => false,
            'skip_mandatory_fields' => false, 'skip_business_rules' => false, 'skip_validation_rules' => false, 'skip_field_validations' => false,
            'skip_webhook' => false, 'origin' => Answer.origins[:api], 'steps_to_change_ids' => nil
          }
        end
        let(:values_step2) do
          {
            'ca034779-04b1-42ca-94d4-ee8d9c233f5d' => '4', '9f568f27-9361-4591-9896-d96b7681ed46' => 'BAR',
            'content_id' => @content.id, 'user_id' => 'ae73effb-a312-4b2f-9853-5d05bd05e6aa', 'remote_ip' => remote_ip, 'skip_verification_url' => false,
            'skip_mandatory_fields' => false, 'skip_business_rules' => false, 'skip_validation_rules' => false, 'skip_field_validations' => false,
            'skip_webhook' => false, 'origin' => Answer.origins[:api], 'steps_to_change_ids' => nil
          }
        end
        let(:answer_service) { double(:answer_service, update: true, success: true) }

        before do
          @content = create(:content, business: @business, draft: true, status: :pending, id: 'bcc82074-eec3-427e-9fcf-0abc0328491d')

          @answer1 = create(:answer, step: @step1, position: 0, content: @content, status: :pending, values: nil, available_at: Time.zone.now)
          @answer2 = create(:answer, step: @step2, position: 1, content: @content, status: :pending, values: nil, available_at: Time.zone.now)

          allow(answer_service).to receive(:record).and_return(@answer1, @answer2)
          allow(AnswerService).to receive(:new).with(values_step1).and_return(answer_service)
          allow(AnswerService).to receive(:new).with(values_step2).and_return(answer_service)
        end

        context 'when xml does not have one step' do
          before do
            @step3 = create(:step, business: @business, name: 'step3')

            @answer3 = create(:answer, step: @step3, position: 3, content: @content, status: :pending, available_at: Time.zone.now)

            allow(answer_service).to receive(:record).and_return(@answer1, @answer2, @answer3)
          end

          it 'returns error' do
            service.save

            expect(service.errors).to eq(['A etapa step3 foi ignorada pois não foi encontrado uma tag <step> correspondente no XML enviado'])
          end
        end

        context 'when step is not available' do
          before do
            @answer2.update_attribute(:available_at, nil)
          end

          it 'returns error' do
            service.save
            expect(service.errors).to eq(['A etapa detail foi ignorada pois ela não está disponível para preenchimento'])
          end
        end

        it 'does not initialize the content service' do
          expect(ContentService).to_not receive(:new).with(business_id: @business.id.to_s)

          service.save
        end

        it 'does not create a new content' do
          expect_any_instance_of(ContentService).to_not receive(:create)

          service.save
        end

        it 'initializes the answer service with the xml content' do
          expect(AnswerService).to receive(:new).with(values_step1).and_return(answer_service)
          expect(AnswerService).to receive(:new).with(values_step2).and_return(answer_service)

          service.save
        end

        it 'update the answers' do
          expect(answer_service).to receive(:update).with(@answer1.id).and_return(true)
          expect(answer_service).to receive(:update).with(@answer2.id).and_return(true)

          service.save
        end

        context 'when update_created_by is true and no created_by_id in content tag' do
          let(:parameters) do
            xml = file_fixture('xml_for_update.xml').read
            xml.gsub('<content guid="bcc82074-eec3-427e-9fcf-0abc0328491d">', '<content guid="bcc82074-eec3-427e-9fcf-0abc0328491d" update_created_by="true">')
          end

          before do
            create(:user, id: 'ae73effb-a312-4b2f-9853-5d05bd05e6aa')
          end

          it 'updates created_by_id with user from step tag' do
            service.save

            @content.reload
            expect(@content.created_by_id).to eq('ae73effb-a312-4b2f-9853-5d05bd05e6aa')
          end
        end
      end
    end
  end
end
