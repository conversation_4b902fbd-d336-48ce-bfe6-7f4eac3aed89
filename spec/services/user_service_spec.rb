require 'rails_helper'

RSpec.describe UserService, type: :service do
  describe 'creation' do
    let(:service) { described_class.new(parameters) }

    before { Company.current.update(chat_enabled: true) }

    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '' } }

      it 'does not create a new user' do
        expect { service.create }.to_not change(User, :count)
      end

      it 'returns false' do
        service.create

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.create

        expect(service.errors).to_not be_empty
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Baz', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>', 'chat_enabled' => 'true' } }

      it 'creates a new user' do
        expect { service.create }.to change(User, :count).by(1)
      end

      it 'returns the created user' do
        service.create

        expect(service.record).to eq(User.last)
      end
      it 'creates authenticable user' do
        service.create

        expect(service.record.active_for_authentication?).to be true
      end

      it 'returns true' do
        service.create

        expect(service.success).to be_truthy
      end

      it 'populates the data' do
        service.create

        user = service.record

        expect(user.name).to eq('Baz')
        expect(user.chat_enabled?).to be_truthy
      end
    end
  end

  describe '#create_unconfirmed' do
    let(:service) { described_class.new(parameters) }
    context 'when the department_ids are not given' do
      let(:parameters) { { 'name' => 'Baz', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>' } }
      let(:link) { 'https://foobar.com.br' }

      it 'creates a new user' do
        expect { service.create_unconfirmed(link) }.to change(User, :count).by(1)
      end

      it 'returns the created user' do
        service.create_unconfirmed(link)

        expect(service.record).to eq(User.last)
      end

      it 'returns true' do
        service.create_unconfirmed(link)

        expect(service.success).to be_truthy
      end

      it 'populates the data' do
        service.create_unconfirmed(link)

        user = service.record

        expect(user.name).to eq('Baz')
      end
    end

    context 'when the department_ids are given' do
      context 'when the department_ids does not exist' do
        let(:parameters) { { 'name' => 'Baz', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>', 'department_ids' => %w[foo bar] } }
        let(:link) { 'https://foobar.com.br' }

        it 'returns the error' do
          service.create_unconfirmed(link)

          expect(service.errors).to eq("Couldn't find all Departments with 'id': (, ) (found 0 results, but was looking for 2). Couldn't find Departments with ids , .")
        end

        it 'not to be successful' do
          service.create_unconfirmed(link)

          expect(service.success).to be_falsy
        end

        it 'does not create a new user' do
          expect { service.create_unconfirmed(link) }.not_to change(User, :count)
        end
      end

      context 'when the department_ids exists' do
        let(:department) { create(:department) }
        let(:department2) { create(:department) }
        let(:parameters) { { 'name' => 'Baz', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>', 'department_ids' => [department.id, department2.id] } }
        let(:link) { 'https://foobar.com.br' }

        it 'creates a new user' do
          expect { service.create_unconfirmed(link) }.to change(User, :count).by(1)
        end

        it 'returns the created user' do
          service.create_unconfirmed(link)

          expect(service.record).to eq(User.last)
        end

        it 'returns true' do
          service.create_unconfirmed(link)

          expect(service.success).to be_truthy
        end

        it 'populates the data' do
          service.create_unconfirmed(link)

          user = service.record

          expect(user.name).to eq('Baz')
        end
      end
    end
  end

  describe 'update' do
    let(:service) { described_class.new(parameters) }

    before do
      @user = create(:user)

      Company.current.update(chat_enabled: true)
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'email' => '' } }

      it 'does not create a new user' do
        expect { service.update(@user.id) }.to_not change(User, :count)
      end

      it 'returns false' do
        service.update(@user.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.update(@user.id)

        expect(service.errors).to_not be_empty
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Bazinga', 'chat_enabled' => 'true' } }

      it 'updates the user' do
        service.update(@user.id)

        user = service.record

        expect(user.name).to eq('Bazinga')
        expect(user.chat_enabled?).to be_truthy
      end

      it 'sets the will_save_change_to_departments as falsey' do
        service.update(@user.id)

        expect(service.record.will_save_change_to_departments).to be_falsey
      end

      context 'when the departments will be updated' do
        let(:parameters) { { 'name' => 'Bazinga', 'chat_enabled' => 'true', department_ids: create_list(:department, 2).pluck(:id) } }

        it 'sets the will_save_change_to_departments as truthy' do
          service.update(@user.id)

          expect(service.record.will_save_change_to_departments).to be_truthy
        end
      end
    end
  end

  describe 'destroy' do
    let(:service) { described_class.new }

    before(:each) do
      @user = create(:user)
    end

    context 'with invalid parameters' do
      let(:user) { mock_model(User, discard: false, errors: double(:errors, full_messages: ['foo'])) }

      before(:each) do
        allow(User).to receive(:find).and_return(user)
      end

      it 'does not destroy the user' do
        expect { service.destroy(@user.id) }.to_not change(User.discarded, :count)
      end

      it 'returns false' do
        service.destroy(@user.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.destroy(@user.id)

        expect(service.errors).to_not be_empty
      end
    end

    context 'with valid parameters' do
      it 'soft deletes the user' do
        expect { service.destroy(@user.id) }.to change(User.discarded, :count).by(1)
      end

      it 'does not erase the user' do
        expect { service.destroy(@user.id) }.to_not change(User, :count)
      end

      it 'returns true' do
        service.destroy(@user.id)

        expect(service.success).to be_truthy
      end
    end
  end

  describe 'restore' do
    let(:service) { described_class.new }

    before do
      @user = create(:user, deleted_at: Time.zone.now)
    end

    context 'with invalid parameters' do
      let(:user) { mock_model(User, undiscard: false, errors: double(:errors, full_messages: ['foo'])) }

      before do
        allow(User).to receive(:with_discarded).and_return(user)
        allow(user).to receive(:find).and_return(user)
      end

      it 'does not restore the user' do
        expect { service.restore(@user.id) }.to_not change(User.kept, :count)
      end

      it 'returns false' do
        service.restore(@user.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.restore(@user.id)

        expect(service.errors).to_not be_empty
      end
    end

    context 'with valid parameters' do
      it 'restores the user' do
        expect { service.restore(@user.id) }.to change(User.kept, :count).by(1)
      end

      it 'restores the user' do
        expect { service.restore(@user.id) }.to change(User.discarded, :count).by(-1)
      end

      it 'returns true' do
        service.restore(@user.id)

        expect(service.success).to be_truthy
      end
    end
  end

  describe 'lock' do
    let(:service) { described_class.new }

    before do
      @user = create(:user)
    end

    it 'locks the user' do
      service.lock(@user.id)

      expect(@user.reload.locked_at).to be_within(1.second).of(Time.zone.now)
    end

    it 'returns success' do
      service.lock(@user.id)

      expect(service).to be_success
    end

    it 'returns no errors' do
      service.lock(@user.id)

      expect(service.errors).to eq([])
    end
  end

  describe 'unlock' do
    let(:service) { described_class.new }

    before do
      @user = create(:user, locked_at: Time.zone.now, unlock_token: 'foo bar', failed_attempts: 3)
    end

    it 'unlocks the user' do
      service.unlock(@user.id)

      expect(@user.reload.locked_at).to be_nil
      expect(@user.reload.unlock_token).to be_nil
      expect(@user.reload.failed_attempts).to be_zero
    end

    it 'returns success' do
      service.lock(@user.id)

      expect(service).to be_success
    end

    it 'returns no errors' do
      service.lock(@user.id)

      expect(service.errors).to eq([])
    end
  end

  describe 'token_to_confirm_step' do

    context 'when the user is an admin' do
      let!(:answer) { create(:answer, :with_dependencies, step: step)}
      let!(:business_group) { create(:business_group) }
      let!(:business) { create(:business, business_group: business_group) }
      let!(:step) { create(:step, business: business) }
      let(:admin) { create(:administrator) }
      let(:parameters) { {'step_id' => step.id, 'email' => admin.email, 'answer_id' => answer.id, 'administrator' => true} }
      let!(:service) { described_class.new(parameters) }
      let(:mailer) { double(:mailer, deliver_later: true) }

      context 'and the e-mail exists' do

        before {allow(UserStepTokenMailer).to receive(:send_token).and_return(mailer)}

        it 'updates the answer authorizer_token' do
          service.token_to_confirm_step

          expect(answer.reload.authorizer_token).not_to be_nil
        end

        it 'sends the email with token' do
          service.token_to_confirm_step

          expect(UserStepTokenMailer).to have_received(:send_token).with(admin, step, answer.reload.authorizer_token)
        end

        it 'sets success as true' do
          service.token_to_confirm_step

          expect(service.success).to be_truthy
        end

        it 'returns no error' do
          service.token_to_confirm_step

          expect(service.errors).to be_empty
        end

      end

      context 'and the e-mail does not exist' do
        let(:parameters) { {'step_id' => step.id, 'email' => 'foobar', 'answer_id' => answer.id, 'administrator' => true} }

        it 'sets sucess as false' do
          service.token_to_confirm_step

          expect(service.success).to be_falsey
        end

        it 'returns an error messsage' do
          service.token_to_confirm_step

          expect(service.errors).to eq(["O usuário informado não tem acesso a esta etapa"])
        end
      end
    end

    context 'when the user is not an admin' do
      let!(:answer) { create(:answer, :with_dependencies, step: step)}
      let!(:business_group) { create(:business_group) }
      let!(:business) { create(:business, business_group: business_group) }
      let!(:step) { create(:step, business: business) }
      let!(:user) { create(:user) }
      let!(:step_permission) { create(:step_permission, step: step, user: user)}
      let!(:service) { described_class.new(parameters) }
      let(:mailer) { double(:mailer, deliver_later: true) }
      let(:parameters) { {'step_id' => step.id, 'email' => user.email, 'answer_id' => answer.id, 'administrator' => false} }

      context 'and the e-mail exists' do

        before {allow(UserStepTokenMailer).to receive(:send_token).and_return(mailer)}

        it 'updates the answer authorizer_token' do
          service.token_to_confirm_step

          expect(answer.reload.authorizer_token).not_to be_nil
        end

        it 'sends the email with token' do
          service.token_to_confirm_step

          expect(UserStepTokenMailer).to have_received(:send_token).with(user, step, answer.reload.authorizer_token)
        end

        it 'sets success as true' do
          service.token_to_confirm_step

          expect(service.success).to be_truthy
        end

        it 'returns no error' do
          service.token_to_confirm_step

          expect(service.errors).to be_empty
        end

      end

      context 'and the e-mail does not exist' do
        let(:parameters) { {'step_id' => step.id, 'email' => 'foobar', 'answer_id' => answer.id, 'administrator' => false} }

        it 'sets sucess as false' do
          service.token_to_confirm_step

          expect(service.success).to be_falsey
        end

        it 'returns an error messsage' do
          service.token_to_confirm_step

          expect(service.errors).to eq(["O usuário informado não tem acesso a esta etapa"])
        end
      end

      context 'and the user can\'t access the step' do
        let(:user2) { create(:user) }
        let(:parameters) { {'step_id' => step.id, 'email' => user2.email, 'answer_id' => answer.id, 'administrator' => false} }

        it 'sets sucess as false' do
          service.token_to_confirm_step

          expect(service.success).to be_falsey
        end

        it 'returns an error messsage' do
          service.token_to_confirm_step

          expect(service.errors).to eq(["O usuário informado não tem acesso a esta etapa"])
        end
      end

    end
  end
end
