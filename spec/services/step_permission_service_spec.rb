require 'rails_helper'

RSpec.describe StepPermissionService, type: :service do
  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group: business_group) }

  describe 'creation' do
    let(:service) { described_class.new(parameters) }

    before do
      @step = create(:step, business: business)
      @user = create(:user)
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'step_id' => '', 'user_id' => '' } }

      it 'does not create a new step permission' do
        expect { service.create }.to_not change(StepPermission, :count)
      end

      it 'returns false' do
        service.create

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.create

        expect(service.errors).to_not be_empty
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'step_id' => @step.id.to_s, 'user_id' => @user.id.to_s } }

      it 'creates a new step permission' do
        expect { service.create }.to change(StepPermission, :count).by(1)
      end

      it 'returns the created step permission' do
        service.create

        expect(service.record).to eq(StepPermission.last)
      end

      it 'returns true' do
        service.create

        expect(service.success).to be_truthy
      end

      it 'populates the data' do
        service.create

        step_permission = service.record

        expect(step_permission.step).to eq(@step)
        expect(step_permission.user).to eq(@user)
      end

      context 'with already existing permission' do
        before do
          @step_permission = create(:step_permission, step_id: @step.id, user_id: @user.id)
        end

        it 'does not create a new step permission' do
          expect do
            service.create
          end.to_not change(StepPermission, :count)
        end

        it 'returns the existing step permission' do
          service.create

          expect(service.record).to eq(@step_permission)
        end

        it 'returns true' do
          service.create

          expect(service.success).to be_truthy
        end
      end
    end

    describe 'creation in batch (new contract)' do
      let(:business_group) { create(:business_group) }
      let(:business) { create(:business, business_group: business_group) }

      before do
        @step_a = create(:step, business: business)
        @step_b = create(:step, business: business)
        @user_a = create(:user)
        @user_b = create(:user)
        @department_a = create(:department)
        @department_b = create(:department)
      end

      it 'creates records for combinations of steps and users' do
        service = described_class.new({ steps_id: [@step_a.id, @step_b.id], users_id: [@user_a.id, @user_b.id], scope: 'edit' })
        expect { service.create }.to change(StepPermission, :count).by(4)
        expect(service.success).to be_truthy
      end

      it 'creates records for combinations of steps and departments' do
        service = described_class.new({ steps_id: [@step_a.id], departments_id: [@department_a.id, @department_b.id], scope: 'read' })
        expect { service.create }.to change(StepPermission, :count).by(2)
        expect(service.success).to be_truthy
      end

      it 'is idempotent when called repeatedly' do
        params = { steps_id: [@step_a.id, @step_b.id], users_id: [@user_a.id], departments_id: [@department_a.id], scope: 'edit' }
        service = described_class.new(params)
        service.create
        expect { described_class.new(params).create }.to_not change(StepPermission, :count)
      end

      it 'returns error with invalid parameters' do
        service = described_class.new({ steps_id: [], users_id: [], departments_id: [] })
        service.create
        expect(service.success).to be_falsy
        expect(service.errors).to_not be_empty
      end

      it 'accepts fallback to legacy contract' do
        service = described_class.new({ step_id: @step_a.id, user_id: @user_a.id, scope: 'edit' })
        expect { service.create }.to change(StepPermission, :count).by(1)
        expect(service.success).to be_truthy
      end
    end

    describe 'create/update unified' do
      let(:business_group) { create(:business_group) }
      let(:business) { create(:business, business_group: business_group) }

      it 'updates scope for batch users set' do
        step_a = create(:step, business: business)
        step_b = create(:step, business: business)
        user_a = create(:user)
        user_b = create(:user)
        create(:step_permission, step: step_a, user: user_a, scope: 'read')
        create(:step_permission, step: step_b, user: user_b, scope: 'read')
        service = described_class.new({ steps_id: [step_a.id, step_b.id], users_id: [user_a.id, user_b.id], scope: 'edit' })
        service.create
        expect(service.success).to be_truthy
        expect(StepPermission.where(step_id: [step_a.id, step_b.id], user_id: [user_a.id, user_b.id]).pluck(:scope).uniq).to eq(['edit'])
      end

      it 'updates scope for batch departments set' do
        step_a = create(:step, business: business)
        step_b = create(:step, business: business)
        dep_a = create(:department)
        dep_b = create(:department)
        create(:step_permission, step: step_a, department: dep_a, scope: 'read')
        create(:step_permission, step: step_b, department: dep_b, scope: 'read')
        service = described_class.new({ steps_id: [step_a.id, step_b.id], departments_id: [dep_a.id, dep_b.id], scope: 'approvement' })
        service.create
        expect(service.success).to be_truthy
        expect(StepPermission.where(step_id: [step_a.id, step_b.id], department_id: [dep_a.id, dep_b.id]).pluck(:scope).uniq).to eq(['approvement'])
      end

      it 'returns error for invalid params' do
        service = described_class.new({ steps_id: [], users_id: [], departments_id: [] })
        service.create
        expect(service.success).to be_falsy
        expect(service.errors).to_not be_empty
      end
    end


    describe 'destroy' do
      let(:service) { described_class.new }

      before do
        @step = create(:step, business: business)
        @user = create(:user)
        @step_permission = create(:step_permission, step: @step, user: @user)
      end

      context 'with invalid parameters' do
        let(:step_permission) { mock_model(StepPermission, errors: double(:errors, full_messages: ['foo'])) }

        before do
          allow(StepPermission).to receive(:find).and_return(step_permission)
        end

        it 'does not remove the step_permission' do
          expect { service.destroy(step_permission.id) }.to_not change(StepPermission, :count)
        end

        it 'returns false' do
          service.destroy(step_permission.id)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.destroy(step_permission.id)

          expect(service.errors).to_not be_empty
        end
      end

      context 'with valid parameters' do
        it 'destroys the step' do
          expect { service.destroy(@step_permission.id) }.to change(StepPermission, :count).by(-1)
        end

        it 'returns true' do
          service.destroy(@step_permission.id)

          expect(service.success).to be_truthy
        end
      end
    end
  end
end
