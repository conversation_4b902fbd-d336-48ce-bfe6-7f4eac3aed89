require 'rails_helper'

RSpec.describe BulkAnswerService, type: :service do
  let(:step) { create(:step, :with_dependencies) }
  let(:user) { create(:user) }

  describe 'sanitization via fetch_answer_param' do
    let(:step) { create(:step, :with_dependencies, :with_field) }
    let(:field) { Field.for_step(step.id).first }

    it 'trims leading and trailing spaces for simple strings' do
      incoming = { field.id => '   valor com espacos   ' }
      service = described_class.new({ answers: [incoming] }.with_indifferent_access)
      result = service.fetch_answer_param
      expect(result.first[field.id]).to eq('valor com espacos')
    end

    it 'parses JSON first and trims items, returning an Array' do
      incoming = { field.id => '  ["  A  ", "B "]  ' }
      service = described_class.new({ answers: [incoming] }.with_indifferent_access)
      result = service.fetch_answer_param
      expect(result.first[field.id]).to eq(["A", "B"])
    end

    it 'trims items when value is already an Array' do
      incoming = { field.id => ["  A  ", "B ", 10, { nested: ' x ' }] }
      service = described_class.new({ answers: [incoming] }.with_indifferent_access)
      result = service.fetch_answer_param
      expect(result.first[field.id]).to eq(["A", "B", 10, { "nested" => 'x' }])
    end

    it 'trims string values inside nested hashes within arrays' do
      incoming = { field.id => [
        { "key" => "  foo  ", "value" => "  bar  " },
        { "meta" => { "inner" => "  baz  " } },
        "  Z  "
      ] }
      service = described_class.new({ answers: [incoming] }.with_indifferent_access)
      result = service.fetch_answer_param
      expect(result.first[field.id]).to eq([
        { "key" => "foo", "value" => "bar" },
        { "meta" => { "inner" => "baz" } },
        "Z"
      ])
    end

    it 'parses JSON array of objects and trims nested string values' do
      incoming = { field.id => ' [ {"key":"  a  ","value":"  b  "}, {"meta":{"inner":"  z  "}}, "  X  " ] ' }
      service = described_class.new({ answers: [incoming] }.with_indifferent_access)
      result = service.fetch_answer_param
      expect(result.first[field.id]).to eq([
        { "key" => "a", "value" => "b" },
        { "meta" => { "inner" => "z" } },
        "X"
      ])
    end

  end
  describe "exception handling" do
    it "returns [] when sanitize_value raises inside strip_answer_texts" do
      incoming = { 1 => "x" }
      service = described_class.new({ answers: [incoming] }.with_indifferent_access)
      allow(service).to receive(:sanitize_value).and_raise("boom")
      expect(service.fetch_answer_param).to eq([])
    end

    it "sanitize_value returns original when unexpected error occurs" do
      obj = Class.new { def is_a?(_); raise "boom"; end }.new
      service = described_class.new({})
      expect(service.send(:sanitize_value, obj)).to equal(obj)
    end
  end


  describe 'strip integration on save/update processing' do
    let(:user) { create(:user) }
    let(:step) { create(:step, :with_dependencies, :with_field) }
    let(:field) { Field.for_step(step.id).first }
    let(:service) { described_class.new(parameters.with_indifferent_access) }

    context 'create flow' do
      let(:parameters) { { 'step_id' => step.id, 'answers' => [{ field.id => '  valor  ' }], user_id: user.id } }

      it 'stores trimmed value into answer_processing.data' do
        service.create
        ap = service.record.answer_processings.first
        expect(ap.data[field.id]).to eq('valor')
      end
    end

    context 'update flow' do
      let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies, step: step) }
      let!(:ap) { create(:answer_processing, status: :failed, bulk_saving_answer: bulk_saving_answer, data: { field.id => 'x' }) }
      let(:parameters) { { 'step_id' => step.id, 'answers' => [{ id: ap.id, field.id => '  novo valor  ' }], user_id: user.id } }

      it 'stores trimmed value into answer_processing.data' do
        service.update(bulk_saving_answer.id)
        expect(ap.reload.data[field.id]).to eq('novo valor')
      end
    end
  end

  describe 'pre_filled' do
    let(:service) { described_class.new(parameters.with_indifferent_access) }
    let!(:step1) { create(:step, :with_dependencies, :with_field) }
    let!(:field1) { step1.fields.first }

    subject { service.pre_filled }

    before do
      field1.update(default_value: '20')
    end

    context 'first step (without saved content)' do
      let(:parameters) { { 'step_id' => step1.id, 'answers' => [{}], user_id: user.id, lines_to_fill: 1 } }

      it 'fills default value' do
        is_expected.to eq([{ field1.id => '20' }])
      end

      context 'when lines_to_fill is 3' do
        let(:parameters) { { 'step_id' => step1.id, 'answers' => [{}, {}, {}, {}], user_id: user.id, lines_to_fill: 3 } }

        it 'fills default value' do
          is_expected.to eq([{ field1.id => '20' }, { field1.id => '20' }, { field1.id => '20' }])
        end
      end
    end

    context 'second step (with saved content)' do
      let!(:step2) { create(:step, business: step1.business) }
      let!(:step2_template) { create(:step_template, step: step2, template: create(:template)) }
      let!(:pk_field) { create(:field, label: 'pk1', template: step1.step_templates.first.template) }
      let!(:field_step2) { create(:field, template: step2_template.template, default_value: nil) }
      let(:parameters) { { 'step_id' => step2.id, 'answers' => [{}], user_id: user.id, lines_to_fill: 1 } }

      before do
        step1.business.update(key_field_ids: [pk_field.id])
      end

      context 'with field_default_value' do
        let!(:field_step2) { create(:field, template: step2_template.template, default_value: 'Pizza') }

        it 'fills default value' do
          is_expected.to eq([{ field_step2.id => field_step2.default_value }])
        end
      end

      # context 'with dependent fields' do
      #   let(:rules) { { "condition": "and", "rules": [{ "field": "#{step1.id}:#{field1.id}", "operator": "equals", "value": "20"}] } }
      #   let(:rule_actions) { [{ "field": "#{step2.id}:#{field_step2}", "operator": "equals", "value": "vinte" }] }
      #   let(:dependent_field_rule) { create(:dependent_field_rule, rules: rules, rule_actions: rule_actions, rule_type: :dependent) }

      #   let(:parameters) { { 'step_id' => step1.id, 'answers' => [{ pk_field.id => '001', field1.id => '20' }], user_id: user.id, lines_to_fill: 1 } }
      #   let(:content) { create(:content, business: step1.business) }

      #   before do
      #     create(:answer, user: create(:user), position: 1, content: content, step: step1, values: { pk_field.id => '001', field1.id => '20' })
      #     create(:answer, user: create(:user), position: 2, content: content, step: step2)
      #   end

      #   it 'fills default value' do
      #     is_expected.to eq([{ pk_field.id => '001', field_step2.id => 'vinte' }])
      #   end
      # end
    end
  end

  describe 'creation' do
    let(:service) { described_class.new(parameters.with_indifferent_access) }

    context 'with errors' do
      let(:answer_params) { [{ 'field_id' => 'value' }] }
      let(:parameters) { { 'step_id' => step.id, 'answers' => answer_params, user_id: user.id } }

      before do
        allow(SaveAnswerHighPriorityWorker).to receive(:push_bulk).and_raise 'error'
      end

      it 'returns false' do
        service.create

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.create

        expect(service.errors).to_not be_empty
      end
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'step_id' => '', answers: nil } }

      it 'does not create a new bulk_saving_answer' do
        expect { service.create }.to_not change(BulkSavingAnswer, :count)
      end

      it 'returns false' do
        service.create

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.create

        expect(service.errors).to_not be_empty
      end
    end

    context 'with valid parameters' do
      let(:answer_params) { [{ 'field_id' => 'value' }] }
      let(:parameters) { { 'step_id' => step.id, 'answers' => answer_params, user_id: user.id } }

      it 'creates a new bulk_saving_answer' do
        expect { service.create }.to change(BulkSavingAnswer, :count).by(1)
      end

      it 'returns the created bulk_saving_answer' do
        service.create

        expect(service.record).to eq(BulkSavingAnswer.last)
      end

      it 'returns true' do
        service.create

        expect(service.success).to be_truthy
      end

      it 'populates the data' do
        service.create

        bulk_saving_answer = service.record

        expect(bulk_saving_answer.step_id).to eq(step.id)
        expect(bulk_saving_answer.answer_processings.count).to eq(1)
      end

      context 'maps EMPTY_VALUE_KEY using field.empty_value' do
        let(:step_with_field) { create(:step, :with_dependencies, :with_field) }
        let(:field) { Field.for_step(step_with_field.id).first }
        let(:parameters) { { 'step_id' => step_with_field.id, 'answers' => [{ field.id => AnswerProcessing::EMPTY_VALUE_KEY }], user_id: user.id } }

        it 'stores field.empty_value in data' do
          service.create
          ap = service.record.answer_processings.first
          expect(ap.data[field.id]).to eq(field.empty_value)
        end
      end

      context 'when the user is not at 4mdg domain' do
        context "and there is less than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_USER} jobs to process" do
          it 'schedules worker' do
            expect(SaveAnswerHighPriorityWorker).to receive(:push_bulk).with(anything)

            service.create
          end
        end

        context "and there is more than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_USER} jobs to process" do
          let(:answer_params) { [{ 'field_id' => 'value' }] }
          let(:parameters) { { 'step_id' => step.id, 'answers' => answer_params, user_id: user.id } }

          before do
            BulkAnswerService::MAXIMUM_RECORDS_FOR_USER.times do
              answer_params << { 'field_id' => 'value' }
            end
          end

          it 'schedules worker' do
            expect(SaveAnswerLowPriorityWorker).to receive(:push_bulk).with(anything)

            service.create
          end
        end
      end

      context 'when the user is at 4mdg domain' do
        let(:user) { create(:user, email: '<EMAIL>') }

        context "and there is less than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER} jobs to process" do
          it 'schedules worker' do
            expect(SaveAnswerStaffHighPriorityWorker).to receive(:push_bulk).with(anything)

            service.create
          end
        end

        context "and there is more than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER} jobs to process" do
          let(:answer_params) { [{ 'field_id' => 'value' }] }
          let(:parameters) { { 'step_id' => step.id, 'answers' => answer_params, user_id: user.id } }

          before do
            BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER.times do
              answer_params << { 'field_id' => 'value' }
            end
          end

          it 'schedules worker' do
            expect(SaveAnswerStaffLowPriorityWorker).to receive(:push_bulk).with(anything)

            service.create
          end
        end
      end

      context 'when all values of an answer_processing are empty' do
        let(:answer_params) { [{ 'field_id' => 'value' }, { 'field_id' => '', 'field_2' => '' }] }

        it 'creates a new bulk_saving_answer' do
          expect { service.create }.to change(BulkSavingAnswer, :count).by(1)
        end

        it 'returns true' do
          service.create

          expect(service.success).to be_truthy
        end

        it 'populates the data' do
          service.create

          bulk_saving_answer = service.record

          expect(bulk_saving_answer.step_id).to eq(step.id)
          expect(bulk_saving_answer.answer_processings.count).to eq(1)
        end
      end

      context 'large batch (sanity check)' do
        let(:step_with_field) { create(:step, :with_dependencies, :with_field) }
        let(:field) { Field.for_step(step_with_field.id).first }
        let(:parameters) do
          { 'step_id' => step_with_field.id, 'answers' => Array.new(15_000) { |i| { field.id => "v#{i}" } }, user_id: user.id }
        end

        it 'creates many answer_processings' do
          expect { service.create }.to change(AnswerProcessing, :count).by(15_000)
        end
      end
    end

    context 'when second answer_processing is invalid' do
      let(:step) { create(:step, :with_dependencies, :with_field) }
      let(:answer_params) { [{ @pk_field.id => '1' }, { @pk_field.id => '1' }] }
      let(:parameters) { { 'step_id' => step.id, 'answers' => answer_params, user_id: user.id } }

      before do
        @pk_field = create(:field, label: 'pk1', template: step.step_templates.first.template)
        step.business.update(key_field_ids: [@pk_field.id])
      end

      it 'does not create a new bulk_saving_answer' do
        expect { service.create }.to_not change(BulkSavingAnswer, :count)
      end

      it 'does not create a new answer_processing' do
        expect { service.create }.to_not change(AnswerProcessing, :count)
      end

      it 'does not enqueue' do
        service.create

        expect(SaveAnswerHighPriorityWorker).to_not receive(:perform_async)
      end

      it 'returns false' do
        service.create

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.create

        expect(service.errors).to_not be_empty
      end
    end
  end

  describe 'update' do
    let(:service) { described_class.new(parameters.with_indifferent_access) }

    before do
      @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies)
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'step_id' => '', 'answers' => nil } }

      it 'does not create a new bulk_saving_answer' do
        expect { service.update(@bulk_saving_answer.id) }.to_not change(BulkSavingAnswer, :count)
      end

      it 'returns false' do
        service.update(@bulk_saving_answer.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.update(@bulk_saving_answer.id)

        expect(service.errors).to_not be_empty
      end

      context 'with invalid answer_processing id' do
        let(:parameters) { { 'step_id' => '', 'answers' => [{ id: '3242' }] } }

        it 'returns false' do
          service.update(@bulk_saving_answer.id)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.update(@bulk_saving_answer.id)

          expect(service.errors).to_not be_empty
        end
      end
    end

    context 'with valid parameters' do
      let(:step_with_field) { create(:step, :with_dependencies, :with_field) }
      let(:field) { Field.for_step(step_with_field.id).first }
      let(:parameters) { { 'step_id' => step_with_field.id, 'answers' => [{ id: @answer_processing.id, field.id => 'last_value' }] } }

      before do
        @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies, step: step_with_field)
        @answer_processing = create(:answer_processing, status: :failed, bulk_saving_answer: @bulk_saving_answer, data: { field.id => 'first_value' })
      end

      it 'updates the answer_processing' do
        service.update(@bulk_saving_answer.id)

        @answer_processing.reload

        expect(@answer_processing.data[field.id]).to eq 'last_value'
        expect(@answer_processing.created?).to eq true
      end
    end

    context 'when second answer_processing is invalid' do
      let(:step) { create(:step, :with_dependencies, :with_field) }
      let(:answer_params) { [{ id: @answer_processing1.id, @pk_field.id => '1' }, { id: @answer_processing2.id, @pk_field.id => '1' }] }
      let(:parameters) { { 'step_id' => step.id, 'answers' => answer_params, user_id: user.id } }

      before do
        @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies, step: step)
        @pk_field = create(:field, label: 'pk1', template: step.step_templates.first.template)
        step.business.update(key_field_ids: [@pk_field.id])
        @answer_processing1 = create(:answer_processing, status: :failed, bulk_saving_answer: @bulk_saving_answer, data:  { @pk_field.id => '1' })
        @answer_processing2 = create(:answer_processing, status: :failed, bulk_saving_answer: @bulk_saving_answer, data:  { @pk_field.id => '2' })
      end

      it 'does not create a new bulk_saving_answer' do
        expect { service.update(@bulk_saving_answer.id) }.to_not change(BulkSavingAnswer.processing, :count)
      end

      it 'does not create a new answer_processing' do
        expect { service.update(@bulk_saving_answer.id) }.to_not change(AnswerProcessing.processing, :count)
      end

      it 'does not enqueue' do
        service.update(@bulk_saving_answer.id)

        expect(SaveAnswerHighPriorityWorker).to_not receive(:perform_async)
      end

      it 'returns false' do
        service.update(@bulk_saving_answer.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.update(@bulk_saving_answer.id)

        expect(service.errors).to eq(['Violação de campo(s) chave(s) "pk1: 1", não é possível inserir valor duplicado'])
      end
    end
  end

  describe 'process_orphans' do
    let(:service) { described_class.new(user_id: user.id) }

    before do
      @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies)
      @answer_processing = create(:answer_processing, status: :created, bulk_saving_answer: @bulk_saving_answer, data: { 'field1' => 'first_value' })
    end

    context 'when the user is not at 4mdg domain' do
      context "and there is less than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_USER} jobs to process" do
        it 'calls SaveAnswerHighPriorityWorker' do
          expect(SaveAnswerHighPriorityWorker).to receive(:perform_async).with(Apartment::Tenant.current, @answer_processing.id)

          service.process_orphans(@bulk_saving_answer.id)
        end
      end

      context "and there is #{BulkAnswerService::MAXIMUM_RECORDS_FOR_USER} or more jobs to process" do
        before do
          @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies)
          BulkAnswerService::MAXIMUM_RECORDS_FOR_USER.times do
            create(:answer_processing, status: :created, bulk_saving_answer: @bulk_saving_answer, data: { 'field1' => 'first_value' })
          end
        end

        it 'calls SaveAnswerLowPriorityWorker' do
          expect(SaveAnswerLowPriorityWorker).to receive(:perform_async).exactly(BulkAnswerService::MAXIMUM_RECORDS_FOR_USER).with(Apartment::Tenant.current, anything)

          service.process_orphans(@bulk_saving_answer.id)
        end
      end
    end

    context 'when the user is at 4mdg domain' do
      let(:user) { create(:user, email: '<EMAIL>') }

      context "and there is less than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER} jobs to process" do
        it 'calls SaveAnswerStaffHighPriorityWorker' do
          expect(SaveAnswerStaffHighPriorityWorker).to receive(:perform_async).with(Apartment::Tenant.current, @answer_processing.id)

          service.process_orphans(@bulk_saving_answer.id)
        end
      end

      context "and there is #{BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER} or more jobs to process" do
        before do
          @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies)
          BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER.times do
            create(:answer_processing, status: :created, bulk_saving_answer: @bulk_saving_answer, data: { 'field1' => 'first_value' })
          end
        end

        it 'calls SaveAnswerStaffLowPriorityWorker' do
          expect(SaveAnswerStaffLowPriorityWorker).to receive(:perform_async).exactly(BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER).with(Apartment::Tenant.current, anything)

          service.process_orphans(@bulk_saving_answer.id)
        end
      end

    end
  end

  describe 'bulk_alteration' do
    let(:service) { described_class.new(parameters.with_indifferent_access) }

    let(:business) { create(:business, :with_dependencies) }
    let(:step) { create(:step, business: business) }
    let(:step_template) { create(:step_template, template: template, step: step, order: 0) }
    let(:template) { create(:template) }
    let(:field) { create(:field, label: 'field', template: template, type: :text) }

    let(:criterions) do
      [{ step_id: step.id, field_id: field.id, value: 'bar', operator: 'equals' }]
    end

    before do
      @content = create(:content, business: business).tap do |content|
        create(:answer, :completed, step: step, position: 1, content: content, values: { field.id => 'bar' })
      end
    end

    context 'with errors' do
      let(:parameters) { { business_id: business.id, bulk_action: 'inactivate', criterions: criterions, alterations: [], user_id: user.id } }

      before do
        allow(BulkAlterationHighPriorityWorker).to receive(:push_bulk).and_raise 'error'
      end

      it 'returns false' do
        service.bulk_alteration

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.bulk_alteration

        expect(service.errors).to_not be_empty
      end
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'business_id' => '', 'bulk_action' => '', 'alterations' => [], 'criterions' => [], 'approvals' => [], 'user_id' => nil } }

      it 'does not create a new bulk_saving_answer' do
        expect { service.bulk_alteration }.to_not change(BulkSavingAnswer, :count)
      end

      it 'returns false' do
        service.bulk_alteration

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.bulk_alteration

        expect(service.errors).to_not be_empty
      end
    end

    context 'with valid parameters' do
      let(:validation_url) { Faker::Internet.url }
      let(:verification_url) { Faker::Internet.url }
      let(:parameters) { { business_id: business.id, bulk_action: 'inactivate', verification_url: verification_url, validation_url: validation_url, criterions: criterions, alterations: [], user_id: user.id } }
      let(:expected_params) { { bulk_action: 'inactivate', verification_url: verification_url, validation_url: validation_url, criterions: criterions, alterations: [] }.with_indifferent_access }

      it 'creates a new bulk_saving_answer' do
        expect { service.bulk_alteration }.to change(BulkSavingAnswer, :count).by(1)
      end

      it 'returns the created bulk_saving_answer' do
        service.bulk_alteration

        expect(service.record).to eq(BulkSavingAnswer.last)
      end

      it 'returns true' do
        service.bulk_alteration

        expect(service.success).to be_truthy
      end
      it 'checks if the instance of AlterationProcessing is valid with the expected parameters' do
        allow(AlterationProcessing).to receive(:new).and_call_original
        allow_any_instance_of(AlterationProcessing).to receive(:save).and_return(true)
        service.bulk_alteration
        expect(AlterationProcessing).to have_received(:new).with(hash_including(expected_params))
      end

      it 'populates the data' do
        service.bulk_alteration

        bulk_saving_answer = service.record

        expect(bulk_saving_answer.business_id).to eq(business.id)
        expect(bulk_saving_answer.alteration_processing).to_not be_nil
      end

      context 'when the user is not at 4mdg domain' do
        context "and there is less than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_USER} jobs to process" do
          it 'schedules worker' do
            expect(BulkAlterationHighPriorityWorker).to receive(:push_bulk).with(anything)

            service.bulk_alteration
          end
        end

        context "and there is more than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_USER} jobs to process" do

          before do
            BulkAnswerService::MAXIMUM_RECORDS_FOR_USER.times do
              create(:content, business: business).tap do |content|
                create(:answer, :completed, step: step, position: 1, content: content, values: { field.id => 'bar' })
              end
            end
          end

          it 'schedules worker' do
            expect(BulkAlterationLowPriorityWorker).to receive(:push_bulk).with(anything)

            service.bulk_alteration
          end
        end
      end

      context 'when the user is at 4mdg domain' do
        let(:user) { create(:user, email: '<EMAIL>') }

        context "and there is less than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER} jobs to process" do
          it 'schedules worker' do
            expect(BulkAlterationStaffHighPriorityWorker).to receive(:push_bulk).with(anything)

            service.bulk_alteration
          end
        end

        context "and there is more than #{BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER} jobs to process" do

          before do
            BulkAnswerService::MAXIMUM_RECORDS_FOR_STAFF_USER.times do
              create(:content, business: business).tap do |content|
                create(:answer, :completed, step: step, position: 1, content: content, values: { field.id => 'bar' })
              end
            end
          end

          it 'schedules worker' do
            expect(BulkAlterationStaffLowPriorityWorker).to receive(:push_bulk).with(anything)

            service.bulk_alteration
          end
        end
      end
    end
  end

  describe 'bulk_alteration_preview' do
    let(:service) { described_class.new(parameters.with_indifferent_access) }

    let(:business) { create(:business, :with_dependencies) }
    let(:step) { create(:step, business: business) }
    let(:step_template) { create(:step_template, template: template, step: step, order: 0) }
    let(:template) { create(:template) }
    let(:field) { create(:field, label: 'field', template: template, type: :text) }
    let(:field2) { create(:field, label: 'field2', template: template, type: :text) }

    let(:criterions) do
      [{ step_id: step.id, field_id: field.id, value: 'bar', operator: 'equals' }]
    end

    let(:parameters) { { business_id: business.id, criterions: criterions } }

    before do
      create(:content, business: business).tap do |content|
        values = { field.id => 'bar', field2.id => 'op1' }
        create(:answer, :completed, step: step, position: 0, content: content, values: values)
      end

      create(:content, business: business).tap do |content|
        values = { field.id => 'foo', field2.id => '123' }
        create(:answer, :completed, step: step, position: 0, content: content, values: values)
      end

      create(:content, business: business).tap do |content|
        values = { field.id => 'bar', field2.id => 'abc' }
        create(:answer, :completed, step: step, position: 0, content: content, values: values)
      end
    end

    it 'returns the results count number' do
      service.bulk_alteration_preview

      expect(service.bulk_alteration_preview).to eq(2)
    end
  end

  describe 'filter_by_criterions' do
    let(:service) { described_class.new }

    let(:business) { create(:business, :with_dependencies) }
    let(:step) { create(:step, business: business) }
    let(:step_template) { create(:step_template, template: template, step: step, order: 0) }
    let(:template) { create(:template) }
    let(:field) { create(:field, label: 'field', template: template, type: :text) }
    let(:field2) { create(:field, label: 'field2', template: template, type: :text) }

    context 'filter empty value' do
      BulkAnswerService::FILTERS.reject{ |filter| ['equals', 'notEqual'].include?(filter) }.each do |filter|
        context "when filtering by '#{filter}' filter" do
          context 'when the field type is \'Integer\'' do
            let(:field) { create(:field, label: 'field', template: template, type: :integer) }
            let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
            let(:criterions) do
              [{ step_id: step.id, field_id: field.id, value: '', operator: filter }]
            end

            before do
              @content1 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => 10 }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content2 = create(:content, business: business).tap do |content|
                values = { field.id => 25, field2.id => '' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content3 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => 15 }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end
            end

            it 'does not try to cast the field type' do
              expect(service).not_to receive(:cast_operator_for_field)

              service.filter_by_criterions(business.id, criterions)
            end

            it 'returns nothing' do
              contents = service.filter_by_criterions(business.id, criterions)

              expect(contents.count).to eq(0)
              expect(contents).to match_array([])
            end
          end

          context 'when the field type is \'Decimal\'' do
            let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
            let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
            let(:criterions) do
              [{ step_id: step.id, field_id: field.id, value: '', operator: filter }]
            end

            before do
              @content1 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => 10.31 }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content2 = create(:content, business: business).tap do |content|
                values = { field.id => 25.35, field2.id => '' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content3 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => 15.56 }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end
            end

            it 'does not try to cast the field type' do
              expect(service).not_to receive(:cast_operator_for_field)

              service.filter_by_criterions(business.id, criterions)
            end

            it 'returns nothing' do
              contents = service.filter_by_criterions(business.id, criterions)

              expect(contents.count).to eq(0)
              expect(contents).to match_array([])
            end
          end

          context 'when the field type is \'Date\'' do
            let(:field) { create(:field, label: 'field', template: template, type: :date) }
            let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
            let(:criterions) do
              [{ step_id: step.id, field_id: field.id, value: '', operator: filter }]
            end

            before do
              @content1 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => '10-10-2020' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content2 = create(:content, business: business).tap do |content|
                values = { field.id => '09-10-2020', field2.id => '' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content3 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => '10-11-2020' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end
            end

            it 'does not try to cast the field type' do
              expect(service).not_to receive(:cast_operator_for_field)

              service.filter_by_criterions(business.id, criterions)
            end

            it 'returns nothing' do
              contents = service.filter_by_criterions(business.id, criterions)

              expect(contents.count).to eq(0)
              expect(contents).to match_array([])
            end
          end

          context 'when the field type is \'Text\'' do
            let(:field) { create(:field, label: 'field', template: template, type: :text) }
            let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
            let(:criterions) do
              [{ step_id: step.id, field_id: field.id, value: '', operator: filter }]
            end

            before do
              @content1 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => 'foobar' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content2 = create(:content, business: business).tap do |content|
                values = { field.id => 'foo bar', field2.id => '' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end

              @content3 = create(:content, business: business).tap do |content|
                values = { field.id => '', field2.id => 'foo obar' }
                create(:answer, :completed, step: step, position: 0, content: content, values: values)
              end
            end

            it 'does not try to cast the field type' do
              expect(service).not_to receive(:cast_operator_for_field)

              service.filter_by_criterions(business.id, criterions)
            end

            it 'returns nothing' do
              contents = service.filter_by_criterions(business.id, criterions)

              expect(contents.count).to eq(0)
              expect(contents).to match_array([])
            end
          end
        end
      end

      context 'when filtering by \'equals\' filter' do
        context 'when the field type is \'Integer\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :integer) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'equals' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 10 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => 25, field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 15 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(2)
            expect(contents).to match_array([@content1, @content3])
          end
        end

        context 'when the field type is \'Decimal\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'equals' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 10.31 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => 25.35, field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 15.56 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(2)
            expect(contents).to match_array([@content1, @content3])
          end
        end

        context 'when the field type is \'Date\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :date) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'equals' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => '10-10-2020' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => '09-10-2020', field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => '10-11-2020' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(2)
            expect(contents).to match_array([@content1, @content3])
          end
        end

        context 'when the field type is \'Text\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :text) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'equals' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 'foobar' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => 'foo bar', field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 'foo obar' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(2)
            expect(contents).to match_array([@content1, @content3])
          end
        end
      end

      context 'when filtering by \'notEqual\' filter' do
        context 'when the field type is \'Integer\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :integer) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'notEqual' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 10 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => 25, field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 15 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(1)
            expect(contents).to match_array([@content2])
          end
        end

        context 'when the field type is \'Decimal\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'notEqual' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 10.31 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => 25.35, field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 15.56 }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(1)
            expect(contents).to match_array([@content2])
          end
        end

        context 'when the field type is \'Date\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :date) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'notEqual' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => '10-10-2020' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => '09-10-2020', field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => '10-11-2020' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(1)
            expect(contents).to match_array([@content2])
          end
        end

        context 'when the field type is \'Text\'' do
          let(:field) { create(:field, label: 'field', template: template, type: :text) }
          let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
          let(:criterions) do
            [{ step_id: step.id, field_id: field.id, value: '', operator: 'notEqual' }]
          end

          before do
            @content1 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 'foobar' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content2 = create(:content, business: business).tap do |content|
              values = { field.id => 'foo bar', field2.id => '' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end

            @content3 = create(:content, business: business).tap do |content|
              values = { field.id => '', field2.id => 'foo obar' }
              create(:answer, :completed, step: step, position: 0, content: content, values: values)
            end
          end

          it 'does not try to cast the field type' do
            expect(service).not_to receive(:cast_operator_for_field)

            service.filter_by_criterions(business.id, criterions)
          end

          it 'returns nothing' do
            contents = service.filter_by_criterions(business.id, criterions)

            expect(contents.count).to eq(1)
            expect(contents).to match_array([@content2])
          end
        end
      end
    end

    context 'filter integer field' do
      context 'when filtering by \'contains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '2', operator: 'contains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 102, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end

      context 'when filtering by \'notContains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '2', operator: 'notContains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 102, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content3])
        end
      end

      context 'when filtering by \'startsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '5', operator: 'startsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 502, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1,@content3])
        end
      end

      context 'when filtering by \'endsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '5', operator: 'endsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 502, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content2,@content3])
        end
      end

      context 'when filtering by \'equals\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '55', operator: 'equals' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1,@content3])
        end
      end

      context 'when filtering by \'notEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '55', operator: 'notEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content2])
        end
      end

      context 'when filtering by \'greaterThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '25', operator: 'greaterThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content3])
        end
      end

      context 'when filtering by \'greaterThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '25', operator: 'greaterThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(3)
          expect(contents).to match_array([@content1, @content2, @content3])
        end
      end

      context 'when filtering by \'lessThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '55', operator: 'lessThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content2])
        end
      end

      context 'when filtering by \'lessThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '55', operator: 'lessThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 25, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(3)
          expect(contents).to match_array([@content1, @content2, @content3])
        end
      end

      context 'when filtering by \'regex\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :integer) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '.*15*', operator: 'regex' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 915534, field2.id => 10 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 659155, field2.id => 111 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55, field2.id => 15 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end
    end

    context 'filter date field' do
      context 'when filtering by \'contains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '2023', operator: 'contains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content3])
        end
      end

      context 'when filtering by \'notContains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '3', operator: 'notContains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content2])
        end
      end

      context 'when filtering by \'startsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '1', operator: 'startsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '11-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '05-01-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '19-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1,@content3])
        end
      end

      context 'when filtering by \'endsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '3', operator: 'endsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2033', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2021', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1,@content2])
        end
      end

      context 'when filtering by \'equals\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '01-01-2022', operator: 'equals' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content2])
        end
      end

      context 'when filtering by \'notEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '01-01-2022', operator: 'notEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content3])
        end
      end

      context 'when filtering by \'greaterThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '01-01-2022', operator: 'greaterThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2026', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2024', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2021', field2.id => '01-01-2021' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end

      context 'when filtering by \'greaterThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '03-02-2022', operator: 'greaterThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2026', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '02-03-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2021', field2.id => '01-01-2021' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end

      context 'when filtering by \'lessThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '31-12-2021', operator: 'lessThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2026', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '02-03-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2021', field2.id => '01-01-2021' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content3])
        end
      end

      context 'when filtering by \'lessThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '31-12-2022', operator: 'lessThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2026', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '12-31-2022', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-01-2021', field2.id => '01-01-2021' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content2, @content3])
        end
      end

      context 'when filtering by \'regex\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :date) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '.*31*', operator: 'regex' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => '01-12-2316', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => '01-31-2122', field2.id => '01-01-2026' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => '01-18-2021', field2.id => '01-01-2021' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end
    end

    context 'filter decimal field' do
      context 'when filtering by \'contains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 3, operator: 'contains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.00, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end

      context 'when filtering by \'notContains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 3, operator: 'notContains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.00, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content3])
        end
      end

      context 'when filtering by \'startsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 9, operator: 'startsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.00, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 9.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1,@content3])
        end
      end

      context 'when filtering by \'endsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 3, operator: 'endsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.83, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1,@content3])
        end
      end

      context 'when filtering by \'equals\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 55.89, operator: 'equals' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.00, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content3])
        end
      end

      context 'when filtering by \'notEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 55.89, operator: 'notEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.00, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end

      context 'when filtering by \'greaterThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 55.89, operator: 'greaterThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.00, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end

      context 'when filtering by \'greaterThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 55.89, operator: 'greaterThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.00, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(3)
          expect(contents).to match_array([@content1, @content2, @content3])
        end
      end

      context 'when filtering by \'lessThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 93.53, operator: 'lessThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.53, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content2, @content3])
        end
      end

      context 'when filtering by \'lessThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 93.53, operator: 'lessThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 93.53, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.38, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.89, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(3)
          expect(contents).to match_array([@content1, @content2, @content3])
        end
      end

      context 'when filtering by \'regex\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '.*91*', operator: 'regex' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 91.53, field2.id => 54.55 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 89.91, field2.id => 698.87 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 55.87, field2.id => 85.7899 }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1, @content2])
        end
      end
    end

    context 'filter text field' do
      context 'when filtering by \'contains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'se', operator: 'contains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content2, @content3])
        end
      end

      context 'when filtering by \'notContains\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'r', operator: 'notContains' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content2])
        end
      end

      context 'when filtering by \'startsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'r', operator: 'startsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content1])
        end
      end

      context 'when filtering by \'endsWith\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'on', operator: 'endsWith' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content2,@content3])
        end
      end

      context 'when filtering by \'equals\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'redundancy', operator: 'equals' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content1,@content2])
        end
      end

      context 'when filtering by \'notEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'redundancy', operator: 'notEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(1)
          expect(contents).to match_array([@content3])
        end
      end

      context 'when filtering by \'greaterThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'redundancy', operator: 'greaterThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns nothing' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(0)
          expect(contents).to match_array([])
        end
      end

      context 'when filtering by \'greaterThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'redundancy', operator: 'greaterThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns nothing' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(0)
          expect(contents).to match_array([])
        end
      end

      context 'when filtering by \'lessThan\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'redundancy', operator: 'lessThan' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns nothing' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(0)
          expect(contents).to match_array([])
        end
      end

      context 'when filtering by \'lessThanOrEqual\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: 'redundancy', operator: 'lessThanOrEqual' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns nothing' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(0)
          expect(contents).to match_array([])
        end
      end

      context 'when filtering by \'regex\' filter' do
        let(:field) { create(:field, label: 'field', template: template, type: :text) }
        let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
        let(:criterions) do
          [{ step_id: step.id, field_id: field.id, value: '.*on', operator: 'regex' }]
        end

        before do
          @content1 = create(:content, business: business).tap do |content|
            values = { field.id => 'redundancy', field2.id => 'generation' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content2 = create(:content, business: business).tap do |content|
            values = { field.id => 'possession' , field2.id => 'exhibition' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end

          @content3 = create(:content, business: business).tap do |content|
            values = { field.id => 'separation', field2.id => 'conscience' }
            create(:answer, :completed, step: step, position: 0, content: content, values: values)
          end
        end

        it 'returns the results count number' do
          contents = service.filter_by_criterions(business.id, criterions)

          expect(contents.count).to eq(2)
          expect(contents).to match_array([@content2, @content3])
        end
      end
    end

    context 'when filtering by a non existent filter' do
      let(:field) { create(:field, label: 'field', template: template, type: :date) }
      let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
      let(:criterions) do
        [{ step_id: step.id, field_id: field.id, value: '2023', operator: 'foobar' }]
      end

      before do
        @content1 = create(:content, business: business).tap do |content|
          values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end

        @content2 = create(:content, business: business).tap do |content|
          values = { field.id => '01-01-2022', field2.id => '01-01-2026' }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end

        @content3 = create(:content, business: business).tap do |content|
          values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end
      end

      it 'returns nothing' do
        contents = service.filter_by_criterions(business.id, criterions)

        expect(contents.count).to eq(0)
        expect(contents).to match_array([])
      end
    end

    context 'when filtering with a invalid regex' do
      let(:field) { create(:field, label: 'field', template: template, type: :date) }
      let(:field2) { create(:field, label: 'field2', template: template, type: :date) }
      let(:criterions) do
        [{ step_id: step.id, field_id: field.id, value: '*9', operator: 'regex' }]
      end

      before do
        @content1 = create(:content, business: business).tap do |content|
          values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end

        @content2 = create(:content, business: business).tap do |content|
          values = { field.id => '01-01-2022', field2.id => '01-01-2026' }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end

        @content3 = create(:content, business: business).tap do |content|
          values = { field.id => '01-01-2023', field2.id => '01-01-2026' }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end
      end

      it 'returns nothing' do
        contents = service.filter_by_criterions(business.id, criterions)

        expect(contents.count).to eq(0)
        expect(contents).to match_array([])
      end
    end

    context 'filter content id' do
      let(:field) { create(:field, label: 'field', template: template, type: :decimal) }
      let(:field2) { create(:field, label: 'field2', template: template, type: :decimal) }
      let(:criterions) do
        [{ step_id: nil, field_id: BulkAnswerService::CONTENT_ID_FIELD, value: [@content1.id, @content3.id], operator: 'equals' }]
      end

      before do
        @content1 = create(:content, business: business).tap do |content|
          values = { field.id => 123.45, field2.id => 12.34 }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end

        @content2 = create(:content, business: business).tap do |content|
          values = { field.id => 123.44, field2.id => 123.46 }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end

        @content3 = create(:content, business: business).tap do |content|
          values = { field.id => 123.46, field2.id => 23.45 }
          create(:answer, :completed, step: step, position: 0, content: content, values: values)
        end
      end

      it 'returns the results count number' do
        contents = service.filter_by_criterions(business.id, criterions)

        expect(contents.count).to eq(2)
        expect(contents).to match_array([@content1, @content3])
      end
    end

    context 'filter parent content id' do
      let(:sub_business) { create(:business, :with_dependencies, sub_business: true) }
      let(:parent_content) { create(:content, :with_dependencies) }
      let(:sub_business_field) { create(:field, :with_dependencies, type: :sub_business, reference_sub_business_id: sub_business.id) }
      let(:sub_content) { create(:content, business_id: sub_business.id, parent_id: parent_content.id) }

      let(:criterions) do
        [{ step_id: nil, field_id: BulkAnswerService::PARENT_CONTENT_ID_FIELD, value: [parent_content.id], operator: 'equals' }]
      end

      before do
        step = create(:step, business: parent_content.business)
        create(:step_template, step: step, template: sub_business_field.template)
        create(:answer, :completed, step: step, position: 0, content: sub_content, values: { 'field0' => '1' })
      end

      it 'returns the results count number' do
        contents = service.filter_by_criterions(sub_business.id, criterions)

        expect(contents.count).to eq(1)
        expect(contents.include?(sub_content)).to be_truthy
      end
    end
  end
end
