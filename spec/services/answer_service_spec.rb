require 'rails_helper'

RSpec.describe AnswerService, type: :service do
  include ActiveSupport::Testing::TimeHelpers
  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group:) }
  let(:step) { create(:step, business:) }
  let!(:step_template) { create(:step_template, step:, template:, order: 0) }
  let(:template) { create(:template) }
  let!(:field) { create(:field, :random_type, template:) }

  let(:option1) { { 'label' => 'Option1', 'value' => 'op1', 'order' => 1 } }
  let(:option2) { { 'label' => 'Option2', 'value' => 'op2', 'order' => 2 } }
  let(:field2) { create(:field, template:, type: :dropdown, options: [option1, option2]) }

  let(:business2) { create(:business, business_group:) }
  let(:step2) { create(:step, business: business2) }
  let(:template2) { create(:template) }
  let(:field3) { create(:field, :random_type, template: template2) }

  let(:field4) { create(:field, template:, type: :reference, reference_business: business2, reference_field: field3, reference_value_field_id: field3.id) }

  before do
    @user = create(:user)

    StepTemplate.create(step_id: step.id, template_id: template.id)
    StepTemplate.create(step_id: step2.id, template_id: template2.id)

    create(:content, business: business2, draft: true).tap do |content|
      values = { field3.id => 'Field3-Value0', 'bar' => 'batz' }
      create(:answer, :completed, step: step2, position: 0, content:, values:)
    end

    @content = create(:content, business:, draft: true)
    @answer = create(:answer, step:, position: 0, content: @content, status: :pending, values: nil)
  end

  describe '#update' do
    let(:service) { described_class.new(parameters) }

    context 'with invalid parameters' do
      let(:parameters) { { field.id => '', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

      it 'does not create any new answer' do
        expect { service.update(@answer.id) }.to_not change(Answer, :count)
      end

      it 'returns false' do
        service.update(@answer.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.update(@answer.id)

        expect(service.errors).to_not be_empty
      end
    end

    context 'with error' do
      let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

      before do
        create(:step_permission, step_id: @answer.step_id, user_id: @user.id)

        allow(Answer).to receive(:find_by).and_call_original
        allow(Answer).to receive(:find_by).with(id: @answer.id, content_id: @answer.content_id).and_return(@answer)
        allow(@answer).to receive(:send).and_return(false)
      end

      it 'does not create any new answer' do
        expect { service.update(@answer.id) }.to_not change(Answer, :count)
      end

      it 'returns true' do
        service.update(@answer.id)

        expect(service.success).to be_truthy
      end
    end

    context 'with exception' do
      let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

      before do
        create(:step_permission, step_id: @answer.step_id, user_id: @user.id)

        allow(Answer).to receive(:find).and_raise 'Not found'
      end

      it 'does not create any new answer' do
        expect { service.update(@answer.id) }.to_not change(Answer, :count)
      end

      it 'returns false' do
        service.update(@answer.id)

        expect(service.success).to be_falsy
      end

      it 'has errors' do
        service.update(@answer.id)

        expect(service.errors).to eq(['Not found'])
      end
    end

    context 'with valid parameters' do
      let(:parameters) do
        {
          field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id,
          'for_review' => for_review, 'remote_ip' => '127.0.0.1'
        }
      end
      let(:for_review) { 'false' }

      context 'with permission for step' do
        before do
          create(:step_permission, step_id: @answer.step_id, user_id: @user.id)

          allow(Answer).to receive(:find_by).and_call_original
          allow(Answer).to receive(:find_by).with(id: @answer.id, content_id: @answer.content_id).and_return(@answer)
        end

        it 'does not create any new answer' do
          expect { service.update(@answer.id) }.to_not change(Answer, :count)
        end

        it 'updates the content progress' do
          expect(@answer.content.status).to eq('pending')
          service.update(@answer.id)
          expect(service.record.content.status).to eq('done')
        end

        it 'returns the updated answer' do
          service.update(@answer.id)

          expect(service.record).to eq(@answer.reload)
        end

        it 'returns true' do
          service.update(@answer.id)

          expect(service.success).to be_truthy
        end

        context 'when the answer does not require authorization' do
          context 'when its not for review' do
            it 'finishes the answer' do
              service.update(@answer.id)

              answer = service.record

              expect(answer.done?).to be_truthy
            end
          end

          context 'when its for review' do
            let(:for_review) { 'true' }

            it 'does not change the answer status' do
              expect { service.update(@answer.id) }.not_to change(@answer, :status)
            end
          end

          context 'and the answer was under review' do
            let(:parameters) do
              {
                field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id,
                'review_notes' => ''
              }
            end

            before do
              @answer.update_attribute(:status, :under_review)
              @answer.update_attribute(:review_requested_by_id, @user.id)
            end

            context 'when there are no notes included' do
              it 'sends the reviewed answer mail to the user who requested the review with the message' do
                freeze_time do
                  note = "#{I18n.l(Time.zone.now, format: :default)} - Etapa #{@answer.step.name} revisada pelo usuário #{@user.name}"
                  expect(StepRevisionMailer).to receive(:step_reviewed).with(@answer, note).and_call_original

                  service.update(@answer.id)
                end
              end

              it 'updates the content note' do
                freeze_time do
                  note = "#{I18n.l(Time.zone.now, format: :default)} - Etapa #{@answer.step.name} revisada pelo usuário #{@user.name}"
                  service.update(@answer.id)

                  content = @answer.content.reload

                  expect(content.note).to include(note)
                end
              end
            end

            context 'when there are notes included' do
              let(:parameters) do
                {
                  field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id,
                  'review_notes' => 'Foo bar bat'
                }
              end

              let(:note) { "#{I18n.l(Time.zone.now, format: :default)} - Etapa #{@answer.step.name} revisada pelo usuário #{@user.name}\r\nObservação: #{parameters['review_notes']}" }

              it 'sends the reviewed answer mail to the user who requested the review with the message including the notes' do
                service.update(@answer.id)
              end

              it 'updates the content note' do
                service.update(@answer.id)

                content = @answer.content.reload

                expect(content.note).to include(note)
                expect(content.note).to include("Observação: #{parameters['review_notes']}")
              end
            end

            it 'changes the answer status to done' do
              expect { service.update(@answer.id) }.to(change { @answer.reload.status }.to('done'))
            end
          end

          it 'does not send email' do
            expect(AuthorizationMailer).to_not receive(:notification_mail)

            service.update(@answer.id)
          end

          context 'but was waiting authorization' do
            before do
              @answer.update_attribute(:status, :waiting_authorization)
              @answer.update_attribute(:data, { values: {} })
            end

            context 'when its not for review' do
              it 'finishes the answer' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.done?).to be_truthy
              end
            end

            context 'when its for review' do
              let(:for_review) { 'true' }

              it 'does not change the answer status' do
                expect { service.update(@answer.id) }.not_to change(@answer, :status)
              end
            end

            it 'does not send email' do
              expect(AuthorizationMailer).to_not receive(:notification_mail)

              service.update(@answer.id)
            end
          end
        end

        context 'when the answer requires authorization' do
          before do
            allow(@answer).to receive(:requires_authorization?).and_return(true)
          end

          context 'and the answer was pending' do
            context 'when its not for review' do
              it 'marks the answer as waiting authorization' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.waiting_authorization?).to be_falsy
                expect(answer.done?).to be_truthy
              end

              it 'sends email' do
                service.update(@answer.id)
              end
            end

            context 'when its for review' do
              let(:for_review) { 'true' }

              it 'does not change the answer status' do
                expect { service.update(@answer.id) }.not_to change(@answer, :status)
              end

              it 'does not send email' do
                expect(AuthorizationMailer).to_not receive(:notification_mail)

                service.update(@answer.id)
              end
            end
          end

          context 'and the answer was waiting authorization' do
            before do
              @answer.update_attribute(:status, :waiting_authorization)
              @answer.update_attribute(:data, { values: {} })
            end

            it 'does not change the status' do
              expect(@answer).to_not receive(:wait_authorization!)
              expect(@answer).to_not receive(:reopen!)

              service.update(@answer.id)

              answer = service.record

              expect(answer.waiting_authorization?).to be_falsy
              expect(answer.done?).to be_truthy
            end

            it 'does not send email' do
              expect(AuthorizationMailer).to_not receive(:notification_mail).and_call_original

              service.update(@answer.id)
            end
          end

          context 'and the answer was done' do
            before do
              @answer.update_attribute(:status, :done)
              @answer.update_attribute(:data, { values: {} })
              @answer.update_attribute(:concluded_at, Time.now)
            end

            context 'when its not for review' do
              it 'reopens the answer' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.waiting_authorization?).to be_falsy
                expect(answer.done?).to be_truthy
              end
            end

            context 'when its for review' do
              let(:for_review) { 'true' }

              it 'does not change the answer status' do
                expect { service.update(@answer.id) }.not_to change(@answer, :status)
              end
            end

            it 'does not send email' do
              expect(AuthorizationMailer).to_not receive(:notification_mail)

              service.update(@answer.id)
            end
          end

          context 'and the answer was under review' do
            let(:parameters) do
              {
                field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id,
                'review_notes' => ''
              }
            end

            before do
              @answer.update_attribute(:status, :under_review)
              @answer.update_attribute(:review_requested_by_id, @user.id)
            end

            context 'when there are no notes included' do
              let(:note) { "#{I18n.l(Time.zone.now, format: :default)} - Etapa #{@answer.step.name} revisada pelo usuário #{@user.name}" }

              it 'sends the reviewed answer mail to the user who requested the review with the message' do
                expect(StepRevisionMailer).to receive(:step_reviewed).with(@answer, note).and_call_original

                service.update(@answer.id)
              end

              it 'updates the content note' do
                service.update(@answer.id)

                content = @answer.content.reload

                expect(content.note).to include(note)
              end
            end

            context 'when there are notes included' do
              let(:parameters) do
                {
                  field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id,
                  'review_notes' => 'Foo bar bat'
                }
              end

              let(:note) { "#{I18n.l(Time.zone.now, format: :default)} - Etapa #{@answer.step.name} revisada pelo usuário #{@user.name}\r\nObservação: #{parameters['review_notes']}" }

              it 'sends the reviewed answer mail to the user who requested the review with the message including the notes' do
                expect(StepRevisionMailer).to receive(:step_reviewed).with(@answer, note).and_call_original

                service.update(@answer.id)
              end

              it 'updates the content note' do
                service.update(@answer.id)

                content = @answer.content.reload

                expect(content.note).to include(note)
                expect(content.note).to include("Observação: #{parameters['review_notes']}")
              end
            end
          end
        end

        context 'when there are multiple type fields' do
          let(:validator) { double(:validator, valid?: true, errors: []) }
          let(:field5) { create(:field, template:, type: :multiple) }
          let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', field5.id => ' foo , bar,1234 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

          before do
            @answer.update!(values: nil)
          end

          it 'updates the answers with formatted data array' do
            service.update(@answer.id)

            answer = service.record

            expect(answer.values[field5.id]).to match_array(%w[foo bar 1234])
          end
        end

        context 'when there is a bussiness with pk field' do
          let(:validator) { double(:validator, valid?: true, errors: []) }
          let(:field5) { create(:field, :random_type, template:) }
          let(:content_url) { "http://test.lvh.me:4200/businesses/#{other_content.business_id}/contents/#{other_content.id}" }
          let(:other_content) { create(:content, business:, draft: true) }

          before do
            business.update(key_field_ids: [field5.id])
            @answer.update!(values: nil)

            values = { field5.id => '1234' }
            create(:answer, :completed, step:, position: 0, content: other_content, values:)
          end

          context 'with error for repeated value' do
            let(:parameters) { { field5.id => '1234', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

            it 'returns false' do
              service.update(@answer.id)

              expect(service.success).to be_falsy
            end

            it 'returns the errors' do
              service.update(@answer.id)

              expect(service.errors).to eq(["Violação de campo(s) chave(s) \"#{field5.label}\", já existe um registro com mesmas chaves: <a href=\"#{content_url}\" target=\"_blank\">Gostaria de verificar o registro?</a>"])
            end
          end

          context 'with success for no repeated value' do
            let(:parameters) { { field5.id => '12345', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

            it 'returns true' do
              service.update(@answer.id)

              expect(service.success).to be_truthy
            end
          end
        end

        context 'when there are upload fields' do
          let(:validator) { double(:validator, valid?: true, errors: []) }
          let(:field5) { create(:field, template:, type: :upload) }
          let(:file1) { ActionDispatch::Http::UploadedFile.new(tempfile: file_fixture('invalid_file.sh'), filename: 'invalid_file.sh') }
          let(:file2) { ActionDispatch::Http::UploadedFile.new(tempfile: file_fixture('invalid_file.sh'), filename: 'foo.txt') }
          let(:files) { [file1, file2] }
          let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', field5.id => files, 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

          before do
            @answer.update!(values: nil)

            allow(UploadFieldValidator).to receive(:new).and_return(validator)
          end

          it 'validates the files' do
            expect(UploadFieldValidator).to receive(:new).with(@answer, field5.id, files)
            expect(validator).to receive(:valid?).and_return(false)

            service.update(@answer.id)
          end

          context 'and there are invalid files' do
            let(:validator) { double(:validator, valid?: false, errors: ['foo']) }
            let(:storage_service_double) { double(StorageService) }

            before do
              allow(File).to receive(:size).with(file2.tempfile).and_return(11.megabytes)
              allow(StorageService).to receive(:new).and_return(storage_service_double)
              allow(storage_service_double).to receive(:perform_base64).and_return(true)
              allow(storage_service_double).to receive(:perform).and_return(true)
            end

            it 'returns false' do
              service.update(@answer.id)

              expect(service.success).to be_falsy
            end

            it 'returns the errors' do
              service.update(@answer.id)

              expect(service.errors).to match_array(%w[foo])
            end
          end
        end

        context 'when there are validation field rules' do
          let(:validator) { double(:validator, errors: ['foo bar']) }
          let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id, :step_authorizer_id => nil } }

          before do
            allow(BusinessValidationRulesRunner).to receive(:new).and_return(validator)
          end

          it 'initializes validation field rule applier' do
            expect(BusinessValidationRulesRunner).to receive(:new).with(instance_of(Answer), anything, anything).and_return(validator)
            expect(validator).to receive(:errors).and_return(['foo bar'])

            service.update(@answer.id)
          end

          context 'checks validation rules' do
            it 'returns false' do
              service.update(@answer.id)

              expect(service.success).to be_falsy
            end

            it 'returns the errors' do
              service.update(@answer.id)

              expect(service.errors).to eq(['foo bar'])
            end
          end

          context 'when a parameter is missing' do
            let(:parameters) { { field.id => 'foo', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

            it 'fills the missing parameter with the default value' do
              expect(BusinessValidationRulesRunner).to receive(:new).with(instance_of(Answer), anything, anything).and_return(validator)

              service.update(@answer.id)
            end

            context 'when the field does not have a default value' do
              before do
                field2.update! default_value: nil
                field4.update! default_value: nil
              end

              it 'fills the missing parameter with an empty string' do
                expect(BusinessValidationRulesRunner).to receive(:new).with(instance_of(Answer), anything, anything).and_return(validator)

                service.update(@answer.id)
              end
            end
          end

          context 'when the answer does not have fields' do
            let(:answer) { create(:answer, :with_dependencies) }
            let(:parameters) { { field.id => 'foo', 'content_id' => @content.id, 'id' => answer.id, 'user_id' => @user.id, :step_authorizer_id => nil } }

            it 'returns only the given values' do
              expect(BusinessValidationRulesRunner).to receive(:new).with(instance_of(Answer), anything, anything).and_return(validator)

              service.update(@answer.id)
            end
          end

          context 'checks for field validation rules' do
            it 'calls field validator' do
              expect(FieldValidator).to receive_message_chain(:new, :validate).with(@answer)

              service.update(@answer.id)
            end

            context 'when there is no error' do
              let(:validator) { double(:validator, errors: []) }
              let(:field) { create(:field, template:) }
              let!(:validation) { create(:field_validation, field_id: field.id, operator: 2, data: 0) }

              before { allow(validator).to receive(:skip_validation?) }

              it 'returns true as success' do
                service.update(@answer.id)

                expect(service.success).to be_truthy
              end

              it 'adds no error to service accessor' do
                service.update(@answer.id)
                expect(service.errors).to eq([])
              end
            end

            context 'when there is an error' do
              let(:field) { create(:field, template:) }
              let!(:validation) { create(:field_validation, field_id: field.id, operator: 0, data: 0) }

              it 'returns false as success' do
                service.update(@answer.id)

                expect(service.success).to be_falsey
              end

              it 'adds the error to service accessor' do
                service.update(@answer.id)
                expect(service.errors).to eq([validation.error_message])
              end
            end
          end
        end

        it 'updates the answer data trimming the values' do
          freeze_time do
            service.update(@answer.id)

            answer = service.record

            expect(answer.filled_at).to be_within(10.seconds).of(Time.zone.now)
            expect(answer.first_fill_at).to be_within(10.seconds).of(Time.zone.now)
            expect(answer.user).to eq(@user)
            expect(answer.values).to eq(
              field.id => 'foo',
              field2.id => 'op1',
              field4.id => 'Field3-Value0'
            )
          end
        end

        it 'saves the last update ip' do
          service.update(@answer.id)

          answer = service.record

          expect(answer.last_update_ip).to eq('127.0.0.1')
        end

        it 'does not updates the answer first fill at when it is already filled' do
          @answer.update_attribute(:first_fill_at, 2.days.ago)

          expect { service.update(@answer.id) }.to_not(change { @answer.reload.first_fill_at })
        end

        context 'when there are fields with text transformation' do
          context 'for a text field' do
            let(:field) { create(:field, template:, type: :text, text_transformation: transformation) }

            context 'when the transformation is uppercase' do
              let(:transformation) { :uppercase }
              let(:parameters) { { field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'uppercases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'FOO', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is lowercase' do
              let(:transformation) { :lowercase }
              let(:parameters) { { field.id => ' FOO ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'downcases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is capitalize' do
              let(:transformation) { :capitalize }
              let(:parameters) { { field.id => ' FOO ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'capitalizes the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'Foo', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end
          end

          context 'for a text area field' do
            let(:field) { create(:field, template:, type: :text_area, text_transformation: transformation) }

            context 'when the transformation is uppercase' do
              let(:transformation) { :uppercase }
              let(:parameters) { { field.id => " foo bar\nbatz ", field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'uppercases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => "FOO BAR\nBATZ", field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is lowercase' do
              let(:transformation) { :lowercase }
              let(:parameters) { { field.id => " FOO BAR\nBatz ", field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'downcases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => "foo bar\nbatz", field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is capitalize' do
              let(:transformation) { :capitalize }
              let(:parameters) { { field.id => " FOO BAR\nBAtz ", field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'capitalizes the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'Foo Bar Batz', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end
          end

          context 'for a multiple field' do
            let(:field) { create(:field, template:, type: :multiple, text_transformation: transformation) }

            context 'when the transformation is uppercase' do
              let(:transformation) { :uppercase }
              let(:parameters) { { field.id => ' foo , bar,batz ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'uppercases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values[field.id]).to match_array(%w[FOO BAR BATZ])
              end
            end

            context 'when the transformation is lowercase' do
              let(:transformation) { :lowercase }
              let(:parameters) { { field.id => ' FOO , BAR,BATZ ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'downcases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values[field.id]).to match_array(%w[foo bar batz])
              end
            end

            context 'when the transformation is capitalize' do
              let(:transformation) { :capitalize }
              let(:parameters) { { field.id => ' FOO , BAR,BAtz ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'capitalizes the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values[field.id]).to match_array(%w[Foo Bar Batz])
              end
            end
          end
        end

        context 'when there is data replacement configured' do
          let(:parameters) { { field.id => ' {foo] ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

          before { create(:data_replacement, text: '{foo]', replacement: '[bar}') }

          context 'for a text field' do
            let(:field) { create(:field, template:, type: :text) }

            it 'replaces the value' do
              service.update(@answer.id)

              answer = service.record

              expect(answer.values).to eq(field.id => '[bar}', field2.id => 'op1', field4.id => 'Field3-Value0')
            end
          end

          context 'for a text area field' do
            let(:field) { create(:field, template:, type: :text_area) }

            it 'replaces the value' do
              service.update(@answer.id)

              answer = service.record

              expect(answer.values).to eq(field.id => '[bar}', field2.id => 'op1', field4.id => 'Field3-Value0')
            end
          end

          context 'for a multiple field' do
            let(:parameters) { { field.id => ' {foo] , foobar,batz ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

            let(:field) { create(:field, template:, type: :multiple) }

            it 'replaces the value' do
              service.update(@answer.id)

              answer = service.record

              expect(answer.values[field.id]).to match_array(['[bar}', 'foobar', 'batz'])
            end
          end
        end

        context 'when there are fields with text transformation and data replacement is configured also' do
          before { create(:data_replacement, text: 'foo', replacement: 'bar') }

          context 'for a text field' do
            let(:field) { create(:field, template:, type: :text, text_transformation: transformation) }

            context 'when the transformation is uppercase' do
              let(:transformation) { :uppercase }
              let(:parameters) { { field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then uppercases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'BAR', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is lowercase' do
              let(:transformation) { :lowercase }
              let(:parameters) { { field.id => ' FOO ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then downcases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'bar', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is capitalize' do
              let(:transformation) { :capitalize }
              let(:parameters) { { field.id => ' FOO ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then capitalizes the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'Bar', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end
          end

          context 'for a text area field' do
            let(:field) { create(:field, template:, type: :text_area, text_transformation: transformation) }

            context 'when the transformation is uppercase' do
              let(:transformation) { :uppercase }
              let(:parameters) { { field.id => " foo bar\nbatz ", field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then uppercases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => "BAR BAR\nBATZ", field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is lowercase' do
              let(:transformation) { :lowercase }
              let(:parameters) { { field.id => " FOO BAR\nBatz ", field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then downcases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => "bar bar\nbatz", field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end

            context 'when the transformation is capitalize' do
              let(:transformation) { :capitalize }
              let(:parameters) { { field.id => " FOO BAR\nBAtz ", field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then capitalizes the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values).to eq(field.id => 'Bar Bar Batz', field2.id => 'op1', field4.id => 'Field3-Value0')
              end
            end
          end

          context 'for a multiple field' do
            let(:field) { create(:field, template:, type: :multiple, text_transformation: transformation) }

            context 'when the transformation is uppercase' do
              let(:transformation) { :uppercase }
              let(:parameters) { { field.id => ' foo , bar,batz ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then uppercases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values[field.id]).to match_array(%w[BAR BAR BATZ])
              end
            end

            context 'when the transformation is lowercase' do
              let(:transformation) { :lowercase }
              let(:parameters) { { field.id => ' FOO , BAR,BATZ ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then downcases the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values[field.id]).to match_array(%w[bar bar batz])
              end
            end

            context 'when the transformation is capitalize' do
              let(:transformation) { :capitalize }
              let(:parameters) { { field.id => ' FOO , BAR,BAtz ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

              it 'first replaces then capitalizes the value' do
                service.update(@answer.id)

                answer = service.record

                expect(answer.values[field.id]).to match_array(%w[Bar Bar Batz])
              end
            end
          end
        end
      end

      context 'without permission for step' do
        it 'returns false' do
          service.update(@answer.id)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.update(@answer.id)

          expect(service.errors).to_not be_empty
        end
      end

      context 'without user_id for step' do
        let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => '' } }

        it 'returns false' do
          service.update(@answer.id)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.update(@answer.id)
          expect(service.errors).to eq(["O id de usuário deve estar presente para a etapa de id #{@answer.step_id}"])
        end
      end

      context 'without answer' do
        let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => '' } }

        it 'returns false' do
          service.update(nil)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.update(nil)

          expect(service.errors).to eq(["Couldn't find Answer without an ID"])
        end
      end
    end
  end

  describe '#authorize' do
    let(:parameters) { { 'content_id' => @content.id, 'authorizer_id' => @user.id, 'remote_ip' => '127.0.0.1' } }

    let(:service) { described_class.new(parameters) }

    before do
      @answer.update_attribute(:filled_at, Time.zone.now)
      @answer.update_attribute(:first_fill_at, Time.zone.now)
      @answer.update_attribute(:user_id, @user.id)
      @answer.update_attribute(:values, {})
    end

    context 'when the answer is not waiting authorization' do
      before do
        @answer.update_attribute(:status, [Answer.statuses[:pending], Answer.statuses[:done]].sample)
      end

      it 'does not change the status of the answer' do
        expect { service.authorize(@answer.id) }.to_not change(@answer, :status)
      end

      it 'returns false' do
        service.authorize(@answer.id)

        expect(service.success).to be_falsy
      end

      it 'returns the error' do
        service.authorize(@answer.id)

        expect(service.errors).to eq(["A etapa '#{@answer.step.name}' não está aguardando aprovação"])
      end
    end

    context 'when the answer is waiting authorization' do
      before do
        @answer.update_attribute(:status, :waiting_authorization)

        allow(Answer).to receive_message_chain(:waiting_authorization, :find_by).and_return(@answer)
      end

      context 'but it does not have any authorizer available' do
        it 'does not change the status of the answer' do
          expect { service.authorize(@answer.id) }.to_not change(@answer, :status)
        end

        it 'returns false' do
          service.authorize(@answer.id)

          expect(service.success).to be_falsy
        end

        it 'returns the error' do
          service.authorize(@answer.id)

          expect(service.errors).to eq(['A etapa requer aprovação mas não existe nenhum usuário habilitado à autorizá-la'])
        end
      end

      context 'and there are available authorizers' do
        before do
          create(:step_permission, scope: :approvement, step: @answer.step, user: create(:user))
        end

        context 'but the given user cannot authorize it' do
          context 'and the user department also cannot' do
            it 'does not change the status of the answer' do
              expect { service.authorize(@answer.id) }.to_not change(@answer, :status)
            end

            it 'returns false' do
              service.authorize(@answer.id)

              expect(service.success).to be_falsy
            end

            it 'returns the error' do
              service.authorize(@answer.id)

              expect(service.errors).to eq(['Esse usuário não pode aprovar essa etapa'])
            end
          end

          context 'but the user department can' do
            before do
              create(:step_permission, scope: :approvement, step: @answer.step, department: create(:department, users: [@user]))
            end

            it 'authorizes the answer' do
              expect(@answer).to receive(:authorize!)

              service.authorize(@answer.id)
            end

            it 'returns true' do
              service.authorize(@answer.id)

              expect(service.success).to be_truthy
            end

            it 'updates the content progress' do
              expect { service.authorize(@answer.id) }.to change(@answer.content, :status).from('waiting_authorization').to('done')
            end

            it 'saves the last update ip' do
              service.authorize(@answer.id)

              answer = service.record

              expect(answer.last_update_ip).to eq('127.0.0.1')
            end

            it 'returns the record' do
              service.authorize(@answer.id)

              expect(service.record).to eq(@answer)
            end
          end
        end

        context 'and the user can authorize the answer' do
          before do
            create(:step_permission, scope: :approvement, step: @answer.step, user: @user)
          end

          it 'authorizes the answer' do
            expect(@answer).to receive(:authorize!)

            service.authorize(@answer.id)
          end

          it 'returns true' do
            service.authorize(@answer.id)

            expect(service.success).to be_truthy
          end

          it 'updates the content progress' do
            expect { service.authorize(@answer.id) }.to change(@answer.content, :status).from('waiting_authorization').to('done')
          end

          it 'saves the last update ip' do
            service.authorize(@answer.id)

            answer = service.record

            expect(answer.last_update_ip).to eq('127.0.0.1')
          end

          it 'returns the record' do
            service.authorize(@answer.id)

            expect(service.record).to eq(@answer)
          end
        end
      end
    end
  end

  describe '#reject' do
    let(:parameters) { { 'content_id' => @content.id, 'authorizer_id' => @user.id, 'remote_ip' => '127.0.0.1' } }

    let(:service) { described_class.new(parameters) }

    before do
      @answer.update_attribute(:filled_at, Time.zone.now)
      @answer.update_attribute(:first_fill_at, Time.zone.now)
      @answer.update_attribute(:user_id, @user.id)
      @answer.update_attribute(:values, {})
    end

    context 'when the answer is not waiting authorization' do
      before do
        @answer.update_attribute(:status, [Answer.statuses[:pending], Answer.statuses[:done]].sample)
      end

      it 'does not change the status of the answer' do
        expect { service.reject(@answer.id, @user) }.to_not change(@answer, :status)
      end

      it 'returns false' do
        service.reject(@answer.id, @user)

        expect(service.success).to be_falsy
      end

      it 'returns the error' do
        service.reject(@answer.id, @user)

        expect(service.errors).to eq(["A etapa '#{@answer.step.name}' não está aguardando aprovação"])
      end
    end

    context 'when the answer is waiting authorization' do
      before do
        @answer.update_attribute(:status, :waiting_authorization)

        allow(Answer).to receive_message_chain(:waiting_authorization, :find_by).and_return(@answer)
      end

      context 'but it does not have any authorizations available' do
        it 'does not change the status of the answer' do
          expect { service.reject(@answer.id, @user) }.to_not change(@answer, :status)
        end

        it 'returns false' do
          service.reject(@answer.id, @user)

          expect(service.success).to be_falsy
        end

        it 'returns the error' do
          service.reject(@answer.id, @user)

          expect(service.errors).to eq(['A etapa requer aprovação mas não existe nenhum usuário habilitado à autorizá-la'])
        end
      end

      context 'and there are available rejectrs' do
        before do
          create(:step_permission, scope: :approvement, step: @answer.step, user: create(:user))
        end

        context 'but the given user cannot reject it' do
          context 'and the user department also cannot' do
            it 'does not change the status of the answer' do
              expect { service.reject(@answer.id, @user) }.to_not change(@answer, :status)
            end

            it 'returns false' do
              service.reject(@answer.id, @user)

              expect(service.success).to be_falsy
            end

            it 'returns the error' do
              service.reject(@answer.id, @user)

              expect(service.errors).to eq(['Esse usuário não pode aprovar essa etapa'])
            end
          end

          context 'but the user department can' do
            before do
              create(:step_permission, scope: :approvement, step: @answer.step, department: create(:department, users: [@user]))
            end

            it 'rejects the answer' do
              expect(@answer).to receive(:reject!)

              service.reject(@answer.id, @user)
            end

            it 'returns true' do
              service.reject(@answer.id, @user)

              expect(service.success).to be_truthy
            end

            it 'updates the content progress' do
              expect { service.authorize(@answer.id) }.to change(@answer.content, :status).from('waiting_authorization').to('done')
            end

            it 'saves the last update ip' do
              service.reject(@answer.id, @user)

              answer = service.record

              expect(answer.last_update_ip).to eq('127.0.0.1')
            end

            it 'returns the record' do
              service.reject(@answer.id, @user)

              expect(service.record).to eq(@answer)
            end
          end
        end

        context 'and the user can authorize the answer' do
          before do
            create(:step_permission, scope: :approvement, step: @answer.step, user: @user)
          end

          it 'authorizes the answer' do
            expect(@answer).to receive(:authorize!)

            service.authorize(@answer.id)
          end

          it 'returns true' do
            service.authorize(@answer.id)

            expect(service.success).to be_truthy
          end

          it 'updates the content progress' do
            expect { service.authorize(@answer.id) }.to change(@answer.content, :status).from('waiting_authorization').to('done')
          end

          it 'saves the last update ip' do
            service.reject(@answer.id, @user)

            answer = service.record

            expect(answer.last_update_ip).to eq('127.0.0.1')
          end

          it 'returns the record' do
            service.authorize(@answer.id)

            expect(service.record).to eq(@answer)
          end
        end
      end
    end
  end

  describe '#revision' do
    before do
      step2.update_attribute(:step_for_revision_ids, [step.id])

      @answer.update_attribute(:status, :done)
      @answer2 = create(:answer, step: step2, position: 1, content: @content, status: :pending, values: nil)
    end

    let(:service) { described_class.new(parameters) }

    context 'with parameters error' do
      let(:parameters) { { 'step_id' => '', 'content_id' => @content.id, 'user_id' => @user.id } }

      it 'does not change the status of the answer' do
        expect { service.revision(@answer2.id) }.to_not change(@answer, :status)
      end

      it 'returns false' do
        service.revision(@answer2.id)

        expect(service.success).to be_falsy
      end

      it 'returns the error' do
        service.revision(@answer2.id)

        expect(service.errors).to eq(['Não é possível revisar à partir desta etapa'])
      end
    end

    context 'with error' do
      let(:parameters) { { 'step_id' => step.id, 'content_id' => @content.id, 'user_id' => @user.id } }

      before do
        allow(Answer).to receive(:find_by).with(step_id: step.id, content_id: @content.id).and_return(@answer)
        allow(@answer).to receive(:save).and_return(false)
        allow(@answer).to receive_message_chain(:errors, :full_messages).and_return(['foo'])
      end

      it 'returns false' do
        service.revision(@answer2.id)

        expect(service.success).to be_falsy
      end

      it 'returns the error' do
        service.revision(@answer2.id)

        expect(service.errors).to eq(['foo'])
      end
    end

    context 'with success' do
      let(:parameters) { { 'step_id' => step.id, 'content_id' => @content.id, 'user_id' => @user.id, 'remote_ip' => '127.0.0.1' } }
      let(:note) { "#{I18n.l(Time.zone.now, format: :default)} - Pedido de revisão etapa #{@answer.step.name} pelo usuário #{@user.name}" }

      it 'changes the status of the answer to under review' do
        expect { service.revision(@answer2.id) }.to(change { @answer.reload.status }.to('under_review'))
      end

      it 'saves the user who requested the review' do
        expect { service.revision(@answer2.id) }.to(change { @answer.reload.review_requested_by_id }.to(@user.id))
      end

      context 'when no notes are included' do
        let(:parameters) { { 'step_id' => step.id, 'content_id' => @content.id, 'notes' => '', 'user_id' => @user.id } }

        it 'adds only the review request to the content notes' do
          service.revision(@answer2.id)

          content = @answer.content.reload

          expect(content.note).to include(note)
        end
      end

      context 'when notes are included' do
        let(:parameters) { { 'step_id' => step.id, 'content_id' => @content.id, 'notes' => 'foobar', 'user_id' => @user.id } }

        it 'adds the review request with the notes to the content notes' do
          service.revision(@answer2.id)

          content = @answer.content.reload

          expect(content.note).to include(note)
          expect(content.note).to include("Observação: #{parameters['notes']}")
        end
      end

      context 'when the users with access must be informed' do
        let(:mailer) { double(:mailer, deliver_now: true) }

        before do
          step2.update_attribute(:send_email_to_all_with_access, true)

          allow(StepRevisionMailer).to receive(:step_to_review).and_return(mailer)
        end
      end

      it 'saves the last update ip' do
        service.revision(@answer2.id)

        answer = service.record

        expect(answer.last_update_ip).to eq('127.0.0.1')
      end

      it 'returns true' do
        service.revision(@answer2.id)

        expect(service.success).to be_truthy
      end
    end
  end

  describe '#validate' do
    let(:service) { described_class.new(parameters) }

    context 'with invalid parameters' do
      let(:parameters) { { field.id => '', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

      it 'returns false' do
        service.update(@answer.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.update(@answer.id)

        expect(service.errors).to_not be_empty
      end
    end

    context 'with error' do
      let(:parameters) { {} }

      before do
        create(:step_permission, step_id: @answer.step_id, user_id: @user.id)

        allow(Answer).to receive(:find_by).and_call_original
        allow(Answer).to receive(:find_by).with(id: @answer.id, content_id: @answer.content_id).and_return(@answer)
      end

      it 'returns false' do
        service.update(@answer.id)

        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        service.update(@answer.id)

        expect(service.errors).to_not be_empty
      end
    end

    context 'with exception' do
      let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

      before do
        create(:step_permission, step_id: @answer.step_id, user_id: @user.id)

        allow(Answer).to receive(:find).and_raise 'Not found'
      end

      it 'returns false' do
        service.update(@answer.id)

        expect(service.success).to be_falsy
      end

      it 'has errors' do
        service.update(@answer.id)

        expect(service.errors).to eq(['Not found'])
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { field.id => ' foo ', field2.id => ' op1', field4.id => 'Field3-Value0 ', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }

      context 'with permission for step' do
        before do
          create(:step_permission, step_id: @answer.step_id, user_id: @user.id)

          allow(Answer).to receive(:find_by).and_call_original
          allow(Answer).to receive(:find_by).with(id: @answer.id, content_id: @answer.content_id).and_return(@answer)
        end

        it 'returns true' do
          service.update(@answer.id)

          expect(service.success).to be_truthy
        end

        context 'when there are upload fields' do
          let(:validator) { double(:validator, valid?: true, errors: []) }
          let(:field5) { create(:field, template:, type: :upload) }
          let(:file1) { ActionDispatch::Http::UploadedFile.new(tempfile: file_fixture('invalid_file.sh'), filename: 'invalid_file.sh') }
          let(:file2) { ActionDispatch::Http::UploadedFile.new(tempfile: file_fixture('invalid_file.sh'), filename: 'foo.txt') }
          let(:files) { [file1, file2] }
          let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', field5.id => files, 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id } }
          let(:storage_service_double) { double(StorageService) }

          before do
            @answer.update!(values: nil)

            allow(UploadFieldValidator).to receive(:new).and_return(validator)
            allow(StorageService).to receive(:new).and_return(storage_service_double)
            allow(storage_service_double).to receive(:perform_base64).and_return(true)
            allow(storage_service_double).to receive(:perform).and_return(true)
          end

          it 'validates the files' do
            expect(UploadFieldValidator).to receive(:new).with(@answer, field5.id, files).and_return(validator)
            expect(validator).to receive(:valid?).and_return(false)

            service.update(@answer.id)
          end

          context 'and there are invalid files' do
            let(:validator) { double(:validator, valid?: false, errors: ['foo']) }

            before do
              allow(File).to receive(:size).with(file2.tempfile).and_return(11.megabytes)
            end

            it 'returns false' do
              service.update(@answer.id)

              expect(service.success).to be_falsy
            end

            it 'returns the errors' do
              service.update(@answer.id)

              expect(service.errors).to match_array(%w[foo])
            end
          end
        end

        context 'when there are validation field rules' do
          let(:validator) { double(:validator, validate: false, errors: ['foo bar']) }
          let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id, :step_authorizer_id => nil } }

          before do
            allow(BusinessValidationRulesRunner).to receive(:new).and_return(validator)
          end

          it 'initializes validation field rule applier' do
            expect(BusinessValidationRulesRunner).to receive(:new).with(instance_of(Answer), anything, anything).and_return(validator)
            expect(validator).to receive(:errors).and_return(['foo bar'])

            service.update(@answer.id)
          end

          context 'checks validation rules' do
            it 'returns false' do
              service.update(@answer.id)

              expect(service.success).to be_falsy
            end

            it 'returns the errors' do
              service.update(@answer.id)

              expect(service.errors).to eq(['foo bar'])
            end
          end

          context 'when a parameter is missing' do
            let(:parameters) { { field.id => 'foo', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => @user.id, :step_authorizer_id => nil } }

            it 'fills the missing parameter with empty string' do
              expect(BusinessValidationRulesRunner).to receive(:new).with(instance_of(Answer), anything, anything).and_return(validator)

              service.update(@answer.id)
            end
          end

          context 'when the answer does not have fields' do
            let(:answer) { create(:answer, :with_dependencies) }
            let(:parameters) { { field.id => 'foo', 'content_id' => @content.id, 'id' => answer.id, 'user_id' => @user.id, :step_authorizer_id => nil } }

            it 'returns only the given values' do
              expect(BusinessValidationRulesRunner).to receive(:new).with(instance_of(Answer), anything, anything).and_return(validator)

              service.update(@answer.id)
            end
          end
        end
      end

      context 'without permission for step' do
        it 'returns false' do
          service.update(@answer.id)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.update(@answer.id)

          expect(service.errors).to_not be_empty
        end
      end

      context 'without user_id for step' do
        let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => '' } }

        it 'returns false' do
          service.update(@answer.id)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.update(@answer.id)

          expect(service.errors).to eq(["O id de usuário deve estar presente para a etapa de id #{@answer.step_id}"])
        end
      end

      context 'without answer' do
        let(:parameters) { { field.id => 'foo', field2.id => 'op1', field4.id => 'Field3-Value0', 'content_id' => @content.id, 'id' => @answer.id, 'user_id' => '' } }

        it 'returns false' do
          service.update(nil)

          expect(service.success).to be_falsy
        end

        it 'returns the errors' do
          service.update(nil)

          expect(service.errors).to eq(["Couldn't find Answer without an ID"])
        end
      end
    end
  end

  describe '#in_changing' do
    let(:service) { described_class.new }
    let(:service_in_changing) { service.in_changing(content_id, step_id) }

    context 'when the answer does not exists' do
      let(:another_content) { create(:content, business:, draft: true) }
      let(:another_step) { create(:step, business:) }
      let(:content_id) { another_content.id }
      let(:step_id) { another_step.id }

      before do
        service_in_changing
      end

      it 'returns false' do
        expect(service.success).to be_falsy
      end

      it 'returns the errors' do
        expect(service.errors).to eq(['Resposta não encontrada.'])
      end
    end

    context 'when the answer exists but the content is discarded' do
      let(:content_id) { @answer.content_id }
      let(:step_id) { @answer.step_id }

      before do
        @content.discard!
        @answer.update_attribute(:status, :done)
        service_in_changing
      end

      it 'returns false' do
        expect(service.success).to be_falsey
      end

      it 'returns the errors' do
        expect(service.errors).to eq(['Não é possível definir o status para: em alteração. O conteúdo foi excluído.'])
      end
    end

    context 'when the answer exists but its not done and content is discarded' do
      let(:content_id) { @answer.content_id }
      let(:step_id) { @answer.step_id }

      before do
        @content.discard!
        @answer.update_attribute(:status, :pending)
        service_in_changing
      end

      it 'returns false' do
        expect(service.success).to be_falsey
      end

      it 'returns the errors' do
        expect(service.errors).to eq(['Não é possível definir o status para: em alteração. O conteúdo foi excluído.'])
      end
    end

    context 'when the answer exists but its not done' do
      let(:content_id) { @answer.content_id }
      let(:step_id) { @answer.step_id }

      before do
        @answer.update_attribute(:status, :pending)
        service_in_changing
      end

      it 'returns false' do
        expect(service.success).to be_falsey
      end

      it 'returns the errors' do
        expect(service.errors).to eq(['Não é possível definir o status para: em alteração. A etapa não foi concluída.'])
      end
    end

    context 'when the answer exists and its done' do
      let(:content_id) { @answer.content_id }
      let(:step_id) { @answer.step_id }

      before do
        @answer.update_attribute(:status, :done)
        service_in_changing
      end

      it 'returns success' do
        expect(service.success).to be_truthy
      end

      it 'changes the status for changing' do
        expect(service.record.status).to eq('changing')
      end
    end
  end
end

