# This file is copied to spec/ when you run 'rails generate rspec:install'
require 'spec_helper'
ENV['RAILS_ENV'] ||= 'test'
require File.expand_path('../config/environment', __dir__)
# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production? || Rails.env.staging? || Rails.env.sandbox? || Rails.env.academy? || Rails.env.uat?
require 'rspec/rails'
require 'should_not/rspec'
require 'pundit/rspec'

# Add additional requires below this line. Rails is not loaded until this point!

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
# Dir[Rails.root.join('spec/support/**/*.rb')].each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove this line.
ActiveRecord::Migration.maintain_test_schema!

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_paths = ["#{::Rails.root}/spec/fixtures"]

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true
  config.include Pundit::RSpec::Matchers, type: :policy

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, :type => :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://relishapp.com/rspec/rspec-rails/docs
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also be filtered via:
  # config.filter_gems_from_backtrace("gem name")

  config.include FactoryBot::Syntax::Methods
  config.include Devise::Test::ControllerHelpers, type: :controller

  config.before(:suite) do
    File.truncate(Rails.root.join('log', 'test.log'), 0)

    if Rails.env.test?
      if defined?(SidekiqUniqueJobs)
        SidekiqUniqueJobs.configure do |config|
          config.logger = Logger.new(nil)
        end
      end

      module Kernel
        def puts(*args); end
        def print(*args); end
        def p(*args); end
        def pp(*args); end
      end
    end

    # Create the default tenant for our tests
    unless Company.exists?(subdomain: 'test')
      begin
        Apartment::Tenant.drop('test')
      rescue StandardError
        true
      end
      Company.create!(name: 'Test Corp.', subdomain: 'test')
    end
  end

  config.before(:each) do
    Apartment::Tenant.switch! 'test'

    stub_request(:get, /api.twilio.com/)
    stub_request(:post, /conversations.twilio.com/)
    stub_request(:post, 'https://wafv2.us-foo-1.amazonaws.com/')
  end

  config.after(:suite) do
    File.truncate(Rails.root.join('log', 'test.log'), 0)
  end
end
