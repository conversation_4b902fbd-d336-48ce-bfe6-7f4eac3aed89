require 'rails_helper'

RSpec.describe CheckOutdatedContentsInElasticsearchWorker, type: :worker do
  describe '#perform' do
    subject { described_class.new.perform }

    context 'when no companies have elasticsearch enabled' do
      before do
        create(:company, use_elasticsearch: false)
        create(:company, use_elasticsearch: false)
      end

      it 'does not enqueue any tenant workers' do
        expect(CheckOutdatedContentsInElasticsearchForTenantWorker).not_to receive(:perform_bulk)
        subject
      end
    end

    context 'when companies have elasticsearch enabled' do
      let!(:company1) { create(:company, subdomain: 'tenant1', use_elasticsearch: true, last_checked_elasticsearch: nil) }
      let!(:company2) { create(:company, subdomain: 'tenant2', use_elasticsearch: true, last_checked_elasticsearch: 1.hour.ago) }
      let!(:company3) { create(:company, subdomain: 'tenant3', use_elasticsearch: false) }

      it 'enqueues tenant workers only for companies with elasticsearch enabled' do
        expected_args = [
          ['tenant1', nil],
          ['tenant2', company2.last_checked_elasticsearch.iso8601]
        ]

        expect(CheckOutdatedContentsInElasticsearchForTenantWorker).to receive(:perform_bulk).with(match_array(expected_args))
        subject
      end

      it 'passes the correct subdomain and last_checked_elasticsearch for each company' do
        allow(CheckOutdatedContentsInElasticsearchForTenantWorker).to receive(:perform_bulk) do |args|
          expect(args).to contain_exactly(
            ['tenant1', nil],
            ['tenant2', company2.last_checked_elasticsearch.iso8601]
          )
        end

        subject
      end

      it 'excludes companies with elasticsearch disabled' do
        allow(CheckOutdatedContentsInElasticsearchForTenantWorker).to receive(:perform_bulk) do |args|
          tenant_names = args.map(&:first)
          expect(tenant_names).not_to include('tenant3')
        end

        subject
      end
    end

    context 'when all companies have elasticsearch disabled' do
      before do
        create_list(:company, 3, use_elasticsearch: false)
      end

      it 'does not enqueue any workers' do
        expect(CheckOutdatedContentsInElasticsearchForTenantWorker).not_to receive(:perform_bulk)
        subject
      end
    end

    context 'when companies have mixed last_checked_elasticsearch values' do
      let!(:never_checked) { create(:company, subdomain: 'never', use_elasticsearch: true, last_checked_elasticsearch: nil) }
      let!(:recently_checked) { create(:company, subdomain: 'recent', use_elasticsearch: true, last_checked_elasticsearch: 30.minutes.ago) }
      let!(:old_check) { create(:company, subdomain: 'old', use_elasticsearch: true, last_checked_elasticsearch: 1.day.ago) }

      it 'preserves the last_checked_elasticsearch timestamp for each company' do
        allow(CheckOutdatedContentsInElasticsearchForTenantWorker).to receive(:perform_bulk) do |args|
          args_hash = args.to_h

          expect(args_hash['never']).to be_nil
          expect(args_hash['recent']).to eq(recently_checked.last_checked_elasticsearch.iso8601)
          expect(args_hash['old']).to eq(old_check.last_checked_elasticsearch.iso8601)
        end

        subject
      end
    end
  end
end
