require 'rails_helper'

RSpec.describe SaveAnswerHighPriorityWorker, type: :worker do
  describe 'perform' do
    subject { worker.perform(Apartment::Tenant.current, answer_processing.id) }

    let(:worker) { described_class.new }
    let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies) }
    let(:answer_processing) { create(:answer_processing, bulk_saving_answer: bulk_saving_answer, data: { field_pk.id => '22' }) }
    let(:field) { create(:field, :with_dependencies) }
    let(:field_pk) { create(:field, template: field.template) }
    let(:saved_answer) { create(:answer, :with_dependencies, step_id: bulk_saving_answer.step_id) }
    let(:answer_service) { double(:answer_service, record: saved_answer, update: true, success: true, errors: []) }
    let(:answer) { create(:answer, :completed, step: bulk_saving_answer.step, content: @content, values: { field_pk.id => '22' }) }

    before do
      @content = create(:content, :with_dependencies, business_id: bulk_saving_answer.step.business_id)
      @answer = answer

      create(:step_template, step: @answer.step, template: field.template)
      create(:step_permission, user_id: bulk_saving_answer.user_id, step_id: bulk_saving_answer.step_id)

      bulk_saving_answer.step.business.update(key_field_ids: [field_pk.id])

      allow(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return(answer_processing)
    end

    def expect_bulk_saving_answer_is_updated_to_done
      expect(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return(answer_processing)
      expect(answer_processing).to receive(:processing!).ordered
      expect(bulk_saving_answer).to receive(:processing!).ordered
      expect(AnswerProcessing).to receive(:exists?).with(bulk_saving_answer_id: bulk_saving_answer.id, status: %i[created processing]).and_return(false)
      expect(bulk_saving_answer).to receive(:update).with(status: :done, end_at: kind_of(Time)).ordered

      subject
    end

    def expect_bulk_saving_answer_is_not_updated_to_done
      expect(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return(answer_processing)
      expect(answer_processing).to receive(:processing!).ordered
      expect(bulk_saving_answer).to receive(:processing!).ordered
      expect(AnswerProcessing).to receive(:exists?).with(bulk_saving_answer_id: bulk_saving_answer.id, status: %i[created processing]).and_return(true)
      expect(bulk_saving_answer).to_not receive(:update)

      subject
    end

    it 'switch tenant' do
      expect(Apartment::Tenant).to receive(:switch!).with Apartment::Tenant.current

      subject
    end

    it 'updates answer_processing status to processing' do
      expect(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return(answer_processing)
      expect(answer_processing).to receive(:processing!)

      subject
    end

    it 'updates bulk_saving_answer status to processing' do
      expect(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return(answer_processing)
      expect(bulk_saving_answer).to receive(:processing!)

      subject
    end

    # context 'with decorated values' do
    #   let(:decorated_values) { { field_pk.id => 'MICROSOFT LTDA' } }
    #   let(:decorator) { double(:decorator, values: decorated_values) }

    #   before { allow_any_instance_of(Answer).to receive(:decorate).and_return(decorator) }

    #   it 'updates data_with_verification_url_response' do
    #     subject

    #     expect(answer_processing.reload.data_with_verification_url_response).to eq(decorated_values)
    #   end
    # end

    context 'when answers params has key_field_ids' do
      before { answer_processing.update(data: { field_pk.id => '33' }) }

      context 'and there is no content with value' do
        it 'returns a new content' do
          expect do
            subject
          end.to change(Content, :count)
        end
      end

      context 'and there is content with value' do
        before do
          @content = create(:content, business: bulk_saving_answer.step.business)
          @answer = create(:answer, :completed, content: @content, step: bulk_saving_answer.step, values: { field_pk.id => '33' })
        end

        it 'raises error' do
          expect { subject }.to_not raise_error
        end
      end
    end

    context 'when AnswerService returns ok' do
      before { allow(AnswerService).to receive(:new).and_return(answer_service) }

      it 'updates answer_processing status to done' do
        expect(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return(answer_processing)
        expect(answer_processing).to receive(:processing!).ordered
        expect(answer_processing).to receive(:update!).with(answer_id: answer.id).ordered.and_call_original
        expect(answer_processing).to receive(:update!).ordered.and_call_original
        expect(answer_processing).to receive(:done!).ordered

        subject
      end

      it { expect_bulk_saving_answer_is_updated_to_done }
    end

    context 'when AnswerService returns errors' do
      let(:answer_service) { double(:answer_service, record: saved_answer, update: false, success: false, errors: ['an error']) }

      before { allow(AnswerService).to receive(:new).and_return(answer_service) }

      it 'updates answer_processing status to done' do
        expect(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return(answer_processing)
        expect(answer_processing).to receive(:processing!).ordered
        expect(answer_processing).to receive(:update).with(processing_errors: answer_service.errors.first, status: :failed).ordered
        expect(Sidekiq.logger).to receive(:error).with '[SaveAnswerHighPriorityWorker] error: an error'

        subject
      end

      it 'updates bulk_saving_answer to done ' do
        expect_bulk_saving_answer_is_updated_to_done
      end
    end

    context 'when there is another answer_processing with status :processing' do
      before { create(:answer_processing, bulk_saving_answer: bulk_saving_answer, status: :processing) }

      it { expect_bulk_saving_answer_is_not_updated_to_done }
    end

    context 'when there is another answer_processing with status :created' do
      before { create(:answer_processing, bulk_saving_answer: bulk_saving_answer, status: :created) }

      it { expect_bulk_saving_answer_is_not_updated_to_done }
    end

    describe 'reorder answers' do
      before do
        scope = Content.where(id: @content.id)

        allow(Content).to receive(:for_pks).and_return(scope)
        allow(scope).to receive(:first).and_return(@content)
      end

      it 'calls for redorder answer on content' do
        expect(@content).to receive(:reorder_answers)

        subject
      end
    end
  end

  describe 'load_content' do
    let(:business) { create(:business, :with_dependencies) }
    let(:step) { create(:step, name: 'main', business: business) }
    let(:template) { create(:template) }
    let(:step_template) { create(:step_template, step: step, template: template) }
    let(:field_pk) { create(:field, template: template) }
    let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies, step: step) }
    let(:answer_processing) { create(:answer_processing, bulk_saving_answer: bulk_saving_answer, data: { field_pk.id => '22' }) }
    let(:field) { create(:field, :with_dependencies) }
    let(:saved_answer) { create(:answer, :with_dependencies) }
    let(:user_id) { create(:user).id }
    let(:content) { create(:content, :with_dependencies, business: business) }

    before do
      step_template # Ensure step_template is created
      @answer = create(:answer, :completed, step: step, content: content, values: { field_pk.id => '22' })
    end

    subject { described_class.new.load_content(step, user_id, answer_processing.data) }

    context 'key_field_ids' do
      context 'is a referenced key_field_id' do
        context 'and business has key_field_ids' do
          let(:key_field_ids) { [field_pk.id] }
          let(:content_url) { "http://test.lvh.me:4200/businesses/#{content.business_id}/contents/#{content.id}" }

          before { business.update(key_field_ids: key_field_ids) }

          it { is_expected.to eq content }

          context 'and business.bulk_insert_on_first_step_validates_pk is true' do
            before { business.update!(bulk_insert_on_first_step_validates_pk: true) }

            context 'and step is the first' do
              it 'raises error' do
                expect { subject }.to raise_error "Já existe um cadastro com as mesmas chaves: <a href=\"#{content_url}\" target=\"_blank\">Gostaria de verificar o registro?</a>."
              end
            end
          end

          context 'and content is subcontent' do
            let(:parent_business) { create(:business, :with_dependencies) }
            let(:parent_content) { create(:content, business: parent_business) }
            let(:contents_for_parent_pks) { double(first: double(id: parent_content.id)) }
            let(:content_service) { double(:content_service, success: true, record: double) }
            let(:parent_key_field_ids) { ['d5716988-87af-4ad6-9064-c8a8ecdb2be8'] }

            before do
              allow(AnswerProcessing).to receive(:find_by).with(id: answer_processing.id).and_return answer_processing
              allow(business).to receive(:sub_business?).and_return(true)
              allow(parent_business).to receive(:key_field_ids).and_return(parent_key_field_ids)
              allow(business).to receive(:parent).and_return(parent_business)
              allow(Content).to receive(:for_pks).and_call_original
              allow(Content).to receive(:for_pks).with(parent_business.id, answer_processing.data).and_return(contents_for_parent_pks)
            end

            it 'saves a new content' do
              expect(ContentService).to receive(:new).with({ business_id: step.business_id, created_by_id: user_id, parent_id: parent_content.id }).and_return(content_service)
              expect(content_service).to receive(:create)

              subject
            end

            context 'when parent has no pk' do
              let(:parent_key_field_ids) { [] }

              it 'raises error' do
                expect { subject }.to raise_error "A chave primária para o negócio #{parent_business.name} não foi configurada."
              end
            end
          end
        end

        context 'and business has no key_field_ids' do
          let(:key_field_ids) { [] }

          before { business.update(key_field_ids: key_field_ids) }

          it 'raises error' do
            expect { subject }.to raise_error "A chave primária para o negócio #{business.name} não foi configurada."
          end
        end
      end

      context 'with an invalid value' do
        let(:key_field_ids) { [field.id] }

        before { business.update(key_field_ids: key_field_ids) }

        it 'returns a new content' do
          expect do
            subject
          end.to change(Content, :count)
        end
      end
    end
  end
end
