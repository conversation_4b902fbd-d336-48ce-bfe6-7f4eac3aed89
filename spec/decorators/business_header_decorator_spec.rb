require 'rails_helper'

RSpec.describe BusinessHeaderDecorator do
  describe '#fields_with_values' do
    let(:business) { create(:business, :with_dependencies, integrate_elastic: false) }
    let(:step) { create(:step, business:) }
    let(:template) { create(:template) }
    let!(:step_template) { create(:step_template, template:, step:, order: 0) }

    let(:field1) { create(:field, :with_dependencies, label: 'field1', template:) }
    let(:field2) { create(:field, :with_dependencies, :dropdown_type, label: 'field2', template:) }

    let!(:header1) { create(:business_header, :with_dependencies, business:, field: field1, step:) }
    let!(:header2) { create(:business_header, :with_dependencies, business:, field: field2, step:) }

    let(:content) { create(:content, business:) }

    let(:params) { { business_id: business.id, content_id: content.id } }

    subject(:result) { described_class.decorate(params).fields_with_values }

    context 'when there are no content values for the given content' do
      it 'returns the fields with empty values' do
        expect(result).to match_array([
          { field: field1, value: '' },
          { field: field2, value: '' }
        ])
      end
    end

    context 'when there are content values for the given content' do
      let!(:answer) { create(:answer, step:, content:) }

      before do
        create(:content_value, field: field1, answer:, content:, value: 'abc123')
        create(:content_value, field: field2, answer:, content:, value: 'foo')
      end

      it 'returns the fields with their decorated values' do
        expect(result).to include(
          { field: field1, value: 'abc123' },
          { field: field2, value: 'Foo' }
        )
      end

      it 'does not leak values from other contents' do
        other_content = create(:content, business:)
        other_answer = create(:answer, step:, content: other_content)
        create(:content_value, field: field1, answer: other_answer, content: other_content, value: 'from-other')

        expect(result).to include({ field: field1, value: 'abc123' })
        expect(result).to include({ field: field2, value: 'Foo' })
      end
    end

    context 'when business has no headers' do
      it 'returns an empty array' do
        other_business = create(:business, :with_dependencies, integrate_elastic: false)
        other_content = create(:content, business: other_business)

        expect(
          BusinessHeaderDecorator.decorate({ business_id: other_business.id, content_id: other_content.id }).fields_with_values
        ).to eq([])
      end
    end

    context 'when a header field is of type reference' do
      let(:field_ref) { create(:field, :with_dependencies, :reference_type, label: 'ref', template: template) }
      let!(:field_option) { create(:field_option, :with_dependencies, field_id: field_ref.id) }
      let!(:header_ref) { create(:business_header, :with_dependencies, business:, field: field_ref, step:) }

      it 'returns the option label via ContentValueDecorator' do
        answer = create(:answer, step:, content: content)
        create(:content_value, field: field_ref, answer:, content:, value: field_option.value)

        expect(result).to include({ field: field_ref, value: field_option.label })
      end
    end

    context 'when a header field is of type upload' do
      let(:field_upload) { create(:field, :with_dependencies, :upload_type, label: 'upload', template: template) }
      let!(:header_upload) { create(:business_header, :with_dependencies, business:, field: field_upload, step:) }

      it 'always returns empty value' do
        answer = create(:answer, step:, content: content)
        create(:content_value, field: field_upload, answer:, content:, value: 'some-file-key')

        expect(result).to include({ field: field_upload, value: '' })
      end
    end
  end
end
