require 'rails_helper'

RSpec.describe StepSearcher, type: :searcher do
  subject(:search) { described_class.new(params).search }

  let(:business) { create(:business, :with_dependencies) }

  describe '#search' do
    context 'when filtering by business_id' do
      let(:params) { { business_id: business.id } }

      let!(:other_business) { create(:business, :with_dependencies) }
      let!(:_other_steps)   { create_list(:step, 2, business: other_business) }

      let!(:s1) { create(:step, business: business) }
      let!(:s2) { create(:step, business: business) }
      let!(:s3) { create(:step, business: business) }

      it 'returns only steps from the business ordered ascending by order' do
        expect(search.pluck(:id)).to eq([s1.id, s2.id, s3.id])
      end
    end

    context 'when filtering by ids' do
      let!(:s1) { create(:step, business: business) }
      let!(:s2) { create(:step, business: business) }
      let!(:s3) { create(:step, business: business) }
      let(:params) { { ids: [s1.id, s3.id] } }

      it 'returns only the requested steps ordered by order' do
        expect(search).to eq([s1, s3])
      end
    end

    context 'when kept parameter is present' do
      let(:params) { { kept: '1' } }

      let!(:kept_step)      { create(:step, business: business) }
      let!(:discarded_step) { create(:step, business: business).tap(&:discard) }

      it 'returns only kept steps' do
        expect(search).to include(kept_step)
        expect(search).not_to include(discarded_step)
      end
    end

    context 'when kept parameter is absent' do
      let(:params) { {} }
      let!(:kept_step)      { create(:step, business: business) }
      let!(:discarded_step) { create(:step, business: business).tap(&:discard) }

      it 'still returns only kept steps due to default scope' do
        expect(search).to include(kept_step)
        expect(search).not_to include(discarded_step)
      end
    end


    context 'when both business_id and ids are provided (intersection)' do
      let!(:other_business) { create(:business, :with_dependencies) }
      let!(:s1) { create(:step, business: business) }
      let!(:s2) { create(:step, business: business) }
      let!(:s3) { create(:step, business: other_business) }
      let(:params) { { business_id: business.id, ids: [s2.id, s3.id] } }

      it 'returns only the steps matching both filters' do
        expect(search).to eq([s2])
      end
    end

    context 'when ids is an empty array' do
      let!(:s1) { create(:step, business: business) }
      let!(:s2) { create(:step, business: business) }
      let(:params) { { business_id: business.id, ids: [] } }

      it 'ignores ids filter and applies other filters normally' do
        expect(search.pluck(:id)).to eq([s1.id, s2.id])
      end
    end

    context 'when kept is "false" as string (legacy semantics)' do
      let(:params) { { kept: 'false' } }

      let!(:kept_step)      { create(:step, business: business) }
      let!(:discarded_step) { create(:step, business: business).tap(&:discard) }

      it 'still returns only kept steps' do
        expect(search).to include(kept_step)
        expect(search).not_to include(discarded_step)
      end
    end

    context 'when no params are provided' do
      let(:params) { {} }

      let!(:kept_a1) { create(:step, business: business) }
      let!(:kept_a2) { create(:step, business: business) }
      let!(:other_business) { create(:business, :with_dependencies) }
      let!(:kept_b1) { create(:step, business: other_business) }
      let!(:discarded_b2) { create(:step, business: other_business).tap(&:discard) }

      it 'returns kept steps across businesses and excludes discarded' do
        expect(search).to include(kept_a1, kept_a2, kept_b1)
        expect(search).not_to include(discarded_b2)
      end
    end

    context 'when ids order differs from creation order' do
      let!(:s1) { create(:step, business: business) }
      let!(:s2) { create(:step, business: business) }
      let(:params) { { ids: [s2.id, s1.id] } }

      it 'still orders by order ascending' do
        expect(search).to eq([s1, s2])
      end
    end
  end
end
