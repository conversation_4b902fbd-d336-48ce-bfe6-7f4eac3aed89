
require 'rails_helper'

RSpec.describe TemplateSearcher, type: :searcher do
  describe '#search' do
    def service(params = {})
      described_class.new(params)
    end

    let!(:kept_a)      { create(:template, name: 'Alpha') }
    let!(:kept_b)      { create(:template, name: 'Beta') }
    let!(:discarded_z) { create(:template, name: 'Zeta', deleted_at: Time.zone.now) }
    let!(:discarded_m) { create(:template, name: 'Mu',   deleted_at: Time.zone.now) }

    context 'without parameters' do
      it 'returns all templates ordered by not deleted first (NULLS FIRST) and then by name asc' do
        result = service.search
        expect(result).to eq([kept_a, kept_b, discarded_m, discarded_z])
      end
    end

    context 'with deleted filter' do
      context 'deleted: true (boolean)' do
        it 'returns only discarded ordered by name asc' do
          result = service(deleted: true).search
          expect(result).to eq([discarded_m, discarded_z])
        end
      end

      context "deleted: 'true' (string)" do
        it 'returns only discarded ordered by name asc' do
          result = service(deleted: 'true').search
          expect(result).to eq([discarded_m, discarded_z])
        end
      end

      context 'deleted: false (boolean)' do
        it 'returns only kept ordered by name asc' do
          result = service(deleted: false).search
          expect(result).to eq([kept_a, kept_b])
        end
      end

      context "deleted: 'false' (string)" do
        it 'returns only kept ordered by name asc' do
          result = service(deleted: 'false').search
          expect(result).to eq([kept_a, kept_b])
        end
      end

      context 'deleted: nil (explicit nil)' do
        it 'behaves like no filter (all, ordered by NULLS FIRST then name)' do
          result = service(deleted: nil).search
          expect(result).to eq([kept_a, kept_b, discarded_m, discarded_z])
        end
      end

      context "deleted receives an unexpected value (string that is not 'true'/'false')" do
        it 'treats it as no filter (robust behavior)' do
          result = service(deleted: 'yes').search
          expect(result).to eq([kept_a, kept_b, discarded_m, discarded_z])
        end
      end
    end
  end
end
