# frozen_string_literal: true

require 'rails_helper'

def load_schemas_for_version(version)
  schemas = {}
  schema_path = Rails.root.join('support', 'schemas', version)

  return schemas unless Dir.exist?(schema_path)

  Dir.glob("#{schema_path}/*.erb.yml").each do |file|
    erb_content = ERB.new(File.read(file)).result
    file_schemas = YAML.load(erb_content)
    schemas.merge!(file_schemas) if file_schemas.is_a?(Hash)
  end

  schemas
end

RSpec.configure do |config|
  # Specify a root folder where Swagger JSON files are generated
  # NOTE: If you're using the rswag-api to serve API descriptions, you'll need
  # to ensure that it's configured to serve Swagger from the same folder
  config.openapi_root = Rails.root.join('swagger').to_s

  # Define one or more Swagger documents and provide global metadata for each one
  # When you run the 'rswag:specs:swaggerize' rake task, the complete Swagger will
  # be generated at the provided relative path under swagger_root
  # By default, the operations defined in spec files are added to the first
  # document below. You can override this behavior by adding a swagger_doc tag to the
  # the root example_group in your specs, e.g. describe '...', swagger_doc: 'v2/swagger.yaml'
  config.openapi_specs = {
    'v1/swagger.yaml' => {
      openapi: '3.0.1',
      info: {
        title: '4MDG API V1',
        version: 'v1',
        description: 'API V1 para integração com o sistema 4MDG'
      },
      paths: {},
      servers: [
        {
          url: 'https://{subdomain}.4mdg.com.br',
          description: 'Servidor de produção',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'https://{subdomain}.staging.4mdg.com.br',
          description: 'Servidor de staging',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'https://{subdomain}.uat.4mdg.com.br',
          description: 'Servidor de UAT',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'https://{subdomain}.sandbox.4mdg.com.br',
          description: 'Servidor de sandbox',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'http://{subdomain}.lvh.me:3000',
          description: 'Servidor local',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        }
      ],
      components: {
        securitySchemes: {
          basic_auth: {
            type: :http,
            scheme: :basic,
            description: 'Autenticação básica usando API Key da empresa'
          }
        },
        schemas: load_schemas_for_version('v1')
      }
    },
    'v2/swagger.yaml' => {
      openapi: '3.0.1',
      info: {
        title: '4MDG External API V2',
        version: 'v2',
        description: 'API V2 para integração externa com o sistema 4MDG'
      },
      paths: {},
      servers: [
        {
          url: 'https://{subdomain}.4mdg.com.br',
          description: 'Servidor de produção',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'https://{subdomain}.staging.4mdg.com.br',
          description: 'Servidor de staging',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'https://{subdomain}.uat.4mdg.com.br',
          description: 'Servidor de UAT',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'https://{subdomain}.sandbox.4mdg.com.br',
          description: 'Servidor de sandbox',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        },
        {
          url: 'http://{subdomain}.lvh.me:3000',
          description: 'Servidor local',
          variables: {
            subdomain: {
              default: 'testes',
              description: 'Subdomínio da empresa'
            }
          }
        }
      ],
      components: {
        securitySchemes: {
          admin_token: {
            type: :apiKey,
            name: 'ADMIN_TOKEN',
            in: :header,
            description: 'Token de autorização do administrador'
          },
          admin_email: {
            type: :apiKey,
            name: 'ADMIN_EMAIL',
            in: :header,
            description: 'Email do administrador'
          }
        },
        schemas: load_schemas_for_version('v2')
      }
    }
  }

  # Specify the format of the output Swagger file when running 'rswag:specs:swaggerize'.
  # The swagger_docs configuration option has the filename including format in
  # the key, this may want to be changed to avoid putting yaml in json files.
  # Defaults to json. Accepts ':json' and ':yaml'.
  config.openapi_format = :yaml
end
