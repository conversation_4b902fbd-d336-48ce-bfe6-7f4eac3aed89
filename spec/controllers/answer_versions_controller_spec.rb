require 'rails_helper'

RSpec.describe AnswerVersionsController, type: :controller do
  before do
    @user = create(:user)

    @auth_headers = @user.create_new_auth_token
  end

  describe 'GET index' do
    render_views
    let(:params) { {} }
    let!(:answer_version) { create(:answer_version, :with_dependencies, :filled) }

    before do
    end
    def do_get
      get :index, xhr: true, format: :datatable, params: parameters
    end

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(params).merge(datatable_columns) }
      let(:json_response) { JSON.parse(response.body) }

      it 'initializes the answerversion datatable' do
        expect(AnswerVersionDatatable).to receive(:new).and_return(datatable)

        do_get
      end

      it 'renders items' do
        do_get

        expect(json_response['data']).to include(hash_including({ 'id' => answer_version.id.to_s }))
      end

      context 'when content is discarded' do
        before { answer_version.answer.content.tap{ |content| content.current_user = create(:user) }.discard }

        it 'renders items' do
          do_get

          expect(json_response['data']).to include(hash_including({ 'id' => answer_version.id.to_s }))
        end
      end
    end
  end
end
