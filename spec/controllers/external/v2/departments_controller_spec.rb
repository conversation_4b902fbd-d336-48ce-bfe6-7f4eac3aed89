require 'rails_helper'

RSpec.describe External::V2::DepartmentsController, type: :controller do
  let(:administrator) { create(:administrator) }
  let(:department) { create(:department, name: 'Engineering', limited: false) }

  before do
    request.headers['ADMIN_TOKEN'] = administrator.authorization_token
    request.headers['ADMIN_EMAIL'] = administrator.email
  end

  describe 'GET #index' do
    context 'with valid authorization' do
      before do
        create_list(:department, 15)
      end

      it 'returns ok status' do
        get :index, format: :json
        expect(response).to have_http_status(:ok)
      end

      it 'returns departments with default pagination' do
        get :index, format: :json
        expect(assigns(:departments).count).to eq(10)
        expect(assigns(:total_departments)).to eq(15)
      end

      it 'respects custom offset and limit parameters' do
        get :index, format: :json, params: { offset: 5, limit: 3 }
        expect(assigns(:departments).count).to eq(3)
        expect(assigns(:departments).offset_value).to eq("5")
      end

      it 'sets pagination metadata correctly' do
        get :index, format: :json, params: { offset: 0, limit: 10 }
        expect(assigns(:total_departments)).to eq(15)
        expect(assigns(:total_next_departments)).to eq(5)
        expect(assigns(:has_more_departments)).to be true
      end

      it 'handles last page correctly' do
        get :index, format: :json, params: { offset: 10, limit: 10 }
        expect(assigns(:total_next_departments)).to eq(0)
        expect(assigns(:has_more_departments)).to be false
      end
    end

    context 'with limit validation' do
      it 'returns unprocessable entity when limit exceeds 500' do
        get :index, format: :json, params: { limit: 501 }
        expect(response).to have_http_status(:unprocessable_entity)

        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to eq(I18n.t("api.v2.departments.errors.max_limit"))
      end

      it 'allows limit of exactly 500' do
        get :index, format: :json, params: { limit: 500 }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'without authorization' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
        request.headers['ADMIN_EMAIL'] = nil
      end

      it 'returns unauthorized status' do
        get :index, format: :json
        expect(response).to have_http_status(:unauthorized)

        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq(I18n.t("api.v2.departments.errors.unauthorized"))
      end
    end

    context 'with invalid credentials' do
      before do
        request.headers['ADMIN_TOKEN'] = 'invalid_token'
        request.headers['ADMIN_EMAIL'] = '<EMAIL>'
      end

      it 'returns unauthorized status' do
        get :index, format: :json
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET #show' do
    context 'with valid authorization' do
      context 'when department exists' do
        it 'returns ok status' do
          get :show, format: :json, params: { id: department.id }
          expect(response).to have_http_status(:ok)
          expect(assigns(:department)).to eq(department)
        end
      end

      context 'when department does not exist' do
        it 'returns not found status' do
          get :show, format: :json, params: { id: 'non-existent-id' }
          expect(response).to have_http_status(:not_found)

          json_response = JSON.parse(response.body)
          expect(json_response['errors']).to eq(I18n.t("api.v2.departments.errors.department_not_found"))
        end
      end
    end

    context 'without authorization' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        get :show, format: :json, params: { id: department.id }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST #create' do
    context 'with valid authorization' do
      context 'with valid parameters' do
        let(:valid_params) do
          {
            name: 'Marketing',
            limited: true
          }
        end

        it 'returns created status' do
          post :create, format: :json, params: valid_params
          expect(response).to have_http_status(:created)
        end

        it 'creates a new department' do
          expect {
            post :create, format: :json, params: valid_params
          }.to change(Department, :count).by(1)
        end

        it 'returns the created department id' do
          post :create, format: :json, params: valid_params

          json_response = JSON.parse(response.body)
          expect(json_response['id']).to eq(Department.last.id)
        end

        it 'creates department with correct attributes' do
          post :create, format: :json, params: valid_params

          created_department = Department.last
          expect(created_department.name).to eq('Marketing')
          expect(created_department.limited).to be true
        end
      end

      context 'with invalid parameters' do
        let(:invalid_params) do
          {
            name: '',
            limited: false
          }
        end

        it 'returns unprocessable entity status' do
          post :create, format: :json, params: invalid_params
          expect(response).to have_http_status(:unprocessable_entity)
        end

        it 'does not create a department' do
          expect {
            post :create, format: :json, params: invalid_params
          }.not_to change(Department, :count)
        end

        it 'returns validation errors' do
          post :create, format: :json, params: invalid_params

          json_response = JSON.parse(response.body)
          expect(json_response['errors']).to be_present
        end
      end
    end

    context 'without authorization' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
        request.headers['ADMIN_EMAIL'] = nil
      end

      it 'returns unauthorized status' do
        post :create, format: :json, params: { name: 'Test Department' }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PUT #update' do
    context 'with valid authorization' do
      context 'when department exists' do
        context 'with valid parameters' do
          let(:valid_params) do
            {
              id: department.id,
              name: 'Updated Engineering',
              limited: true
            }
          end

          it 'returns ok status' do
            put :update, format: :json, params: valid_params
            expect(response).to have_http_status(:ok)
          end

          it 'updates the department' do
            put :update, format: :json, params: valid_params

            department.reload
            expect(department.name).to eq('Updated Engineering')
            expect(department.limited).to be true
          end

          it 'returns the updated department id' do
            put :update, format: :json, params: valid_params

            json_response = JSON.parse(response.body)
            expect(json_response['id']).to eq(department.id)
          end
        end

        context 'with invalid parameters' do
          let(:invalid_params) do
            {
              id: department.id,
              name: '',
              limited: false
            }
          end

          it 'returns unprocessable entity status' do
            put :update, format: :json, params: invalid_params
            expect(response).to have_http_status(:unprocessable_entity)
          end

          it 'does not update the department' do
            original_name = department.name
            put :update, format: :json, params: invalid_params

            department.reload
            expect(department.name).to eq(original_name)
          end

          it 'returns validation errors' do
            put :update, format: :json, params: invalid_params

            json_response = JSON.parse(response.body)
            expect(json_response['errors']).to be_present
          end
        end
      end

      context 'when department does not exist' do
        it 'raises ActiveRecord::RecordNotFound' do
          expect {
            put :update, format: :json, params: { id: 'non-existent-id', name: 'Test' }
          }.to raise_error(ActiveRecord::RecordNotFound)
        end
      end
    end

    context 'without authorization' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        put :update, format: :json, params: { id: department.id, name: 'Updated Name' }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'authorization edge cases' do
    context 'with invalid action parameter' do
      before do
        request.headers['ADMIN_TOKEN'] = administrator.authorization_token
        request.headers['ADMIN_EMAIL'] = administrator.email
      end

      it 'handles invalid actions through routing (would return 404)' do
        expect {
          get :invalid_action, format: :json
        }.to raise_error(ActionController::UrlGenerationError)
      end
    end

    describe 'parameter filtering' do
      it 'only permits allowed parameters in create' do
        post :create, format: :json, params: {
          name: 'Test Department',
          limited: true,
          unauthorized_param: 'should_not_be_permitted'
        }

        expect(response).to have_http_status(:created)
        created_department = Department.last
        expect(created_department.name).to eq('Test Department')
        expect(created_department.limited).to be true
      end

      it 'only permits allowed parameters in update' do
        put :update, format: :json, params: {
          id: department.id,
          name: 'Updated Department',
          limited: true,
          unauthorized_param: 'should_not_be_permitted'
        }

        expect(response).to have_http_status(:ok)
        department.reload
        expect(department.name).to eq('Updated Department')
        expect(department.limited).to be true
      end
    end
  end
end
