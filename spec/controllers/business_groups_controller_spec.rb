require 'rails_helper'

RSpec.describe BusinessGroupsController, type: :controller do
  before do
    @admin = create(:administrator)
    @user = create(:user)

    @auth_headers = @admin.create_new_auth_token
    @user_auth_headers = @user.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    before do
      @business_group = create(:business_group, name: 'Foo')
      @business_group2 = create(:business_group, name: 'Car', deleted_at: Time.zone.now)
      @business_group3 = create(:business_group, name: 'Bar')

      @business = create(:business, name: 'Sub Foo', business_group: @business_group)
      @business2 = create(:business, name: 'Sub Car', business_group: @business_group2)
    end

    context 'with administrator' do
      context 'datatable request' do
        let(:datatable)  { double(:datatable, to_json: true) }
        let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
        let(:parameters) { @auth_headers.merge(datatable_columns) }

        it 'initializes the business group datatable' do
          expect(BusinessGroupDatatable).to receive(:new).and_return(datatable)

          get :index, xhr: true, format: :datatable, params: parameters
        end
      end

      context 'json request' do
        it 'initializes the business group searcher' do
          expect(BusinessGroupSearcher).to receive(:new).with(ActionController::Parameters.new('current_user' => nil).permit(
            :current_user,
          )).and_call_original

          get :index, xhr: true, format: :json, params: @auth_headers
        end

        it 'searches the business groups' do
          expect_any_instance_of(BusinessGroupSearcher).to receive(:search).and_call_original

          get :index, xhr: true, format: :json, params: @auth_headers
        end

        it 'renders the index page' do
          get :index, xhr: true, format: :json, params: @auth_headers

          expect(response).to render_template(:index)
        end

        it 'returns ok status' do
          get :index, xhr: true, format: :json, params: @auth_headers

          expect(response).to have_http_status(:ok)
        end

        it 'renders the json structure' do
          get :index, xhr: true, format: :json, params: @auth_headers

          expect_json_types('*', id: :string, name: :string, description: :string, deleted: :boolean)
        end

        it 'renders the business groups data' do
          get :index, xhr: true, format: :json, params: @auth_headers

          expect_json_sizes(3)
          expect_json('0', id: @business_group3.id, name: @business_group3.name, description: @business_group3.description, deleted: @business_group3.discarded?)
          expect_json('1', id: @business_group.id, name: @business_group.name, description: @business_group.description, deleted: @business_group.discarded?)
          expect_json('2', id: @business_group2.id, name: @business_group2.name, description: @business_group2.description, deleted: @business_group2.discarded?)
        end
      end

      context 'when request using active param' do
        let(:parameters) { {active: true}.merge(@auth_headers) }

        it 'only return active business groups' do
          get :index, xhr: true, format: :json, params: parameters.merge({ active: true })
          expect_json_sizes(2)
          expect_json('0', id: @business_group3.id, name: @business_group3.name, description: @business_group3.description, deleted: @business_group3.discarded?)
          expect_json('1', id: @business_group.id, name: @business_group.name, description: @business_group.description, deleted: @business_group.discarded?)
        end

        it 'only return inactive business groups' do
          get :index, xhr: true, format: :json, params: parameters.merge({ active: false })
          expect_json_sizes(1)
          expect_json('0', id: @business_group2.id, name: @business_group2.name, description: @business_group2.description, deleted: @business_group2.discarded?)
        end
      end
    end

    context 'with user' do
      context 'json request' do
        let(:searcher) { double(search: [@business_group3, @business_group, @business_group2]) }

        before { allow(BusinessGroupSearcher).to receive(:new).and_return searcher }

        it 'initializes the business group searcher' do
          expect(BusinessGroupSearcher).to receive(:new).with(ActionController::Parameters.new().permit(:deleted).merge(current_user: @user)).and_call_original

          get :index, xhr: true, format: :json, params: @user_auth_headers
        end

        it 'searches the business groups' do
          expect(searcher).to receive(:search)

          get :index, xhr: true, format: :json, params: @user_auth_headers
        end

        it 'renders the index page' do
          get :index, xhr: true, format: :json, params: @user_auth_headers

          expect(response).to render_template(:index)
        end

        it 'returns ok status' do
          get :index, xhr: true, format: :json, params: @user_auth_headers

          expect(response).to have_http_status(:ok)
        end

        it 'renders the json structure' do
          get :index, xhr: true, format: :json, params: @user_auth_headers

          expect_json_types('*', id: :string, name: :string, description: :string, deleted: :boolean)
        end

        it 'renders the business groups data' do
          get :index, xhr: true, format: :json, params: @user_auth_headers

          expect_json_sizes(3)
          expect_json('0', id: @business_group3.id, name: @business_group3.name, description: @business_group3.description, deleted: @business_group3.discarded?)
          expect_json('1', id: @business_group.id, name: @business_group.name, description: @business_group.description, deleted: @business_group.discarded?)
          expect_json('2', id: @business_group2.id, name: @business_group2.name, description: @business_group2.description, deleted: @business_group2.discarded?)
        end
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '', 'description' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'foolalo' } }

      it 'initializes the business group service' do
        expect(BusinessGroupService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :description
        )).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'creates the business group' do
        expect_any_instance_of(BusinessGroupService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:created)
      end
    end

    context 'with user' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'foolalo' }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PUT update' do
    before { @business_group = create(:business_group) }

    context 'with invalid parameters' do
      let(:parameters) {  { 'name' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @business_group.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @business_group.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'batz' } }

      it 'initializes the business group service' do
        expect(BusinessGroupService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :description
        )).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @business_group.id).merge(@auth_headers)
      end

      it 'updates the business_group' do
        expect_any_instance_of(BusinessGroupService).to receive(:update).with(@business_group.id.to_s).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @business_group.id).merge(@auth_headers)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @business_group.id).merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with user' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'batz' }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        put :update, xhr: true, params: parameters.merge(id: @business_group.id)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { { id: @business_group.id }.merge(@auth_headers) }

    before { @business_group = create(:business_group) }

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(id: :string, name: :string, deleted: :boolean)
    end

    it 'returns the business group data' do
      get :show, xhr: true, params: parameters

      expect_json(id: @business_group.id, name: @business_group.name, description: @business_group.description, deleted: @business_group.discarded?)
    end

    context 'with user' do
      let(:parameters) { { id: @business_group.id }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        get :show, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @business_group.id }.merge(@auth_headers) }

    before { @business_group = create(:business_group) }

    context 'with error' do
      let(:business_group_service) { double(:business_group_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(BusinessGroupService).to receive(:new).and_return(business_group_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the business group service' do
        expect(BusinessGroupService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the business group' do
        expect_any_instance_of(BusinessGroupService).to receive(:destroy).with(@business_group.id.to_s).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'with user' do
      let(:parameters) { { id: @business_group.id }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PATCH activate' do
    let(:parameters) { { id: @business_group.id }.merge(@auth_headers) }

    before do
      @business_group = create(:business_group, deleted_at: Time.zone.now)
    end

    context 'with error' do
      let(:business_group_service) { double(:business_group_service, activate: false, success: false, errors: ['foo']) }

      before do
        allow(BusinessGroupService).to receive(:new).and_return(business_group_service)
      end

      it 'returns bad request status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        patch :activate, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the business group service' do
        expect(BusinessGroupService).to receive(:new).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'activates the business group' do
        expect_any_instance_of(BusinessGroupService).to receive(:activate).with(@business_group.id.to_s).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'returns no content status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'with user' do
      let(:parameters) { { id: @business_group.id }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
