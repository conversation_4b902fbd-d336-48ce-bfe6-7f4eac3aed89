require 'rails_helper'

RSpec.describe TemplatesController, type: :controller do
  before do
    @admin = create(:administrator)
    @user = create(:user)

    @auth_headers = @admin.create_new_auth_token
    @user_auth_headers = @user.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    before do
      @template = create(:template, name: 'Foo')
      @template2 = create(:template, name: 'Car', deleted_at: Time.zone.now)
      @template3 = create(:template, name: 'Bar')
    end

    context 'with administrator' do
      context 'datatable request' do
        let(:datatable)  { double(:datatable, to_json: true) }
        let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
        let(:parameters) { @auth_headers.merge(datatable_columns) }

        it 'initializes the template datatable' do
          expect(TemplateDatatable).to receive(:new).and_return(datatable)

          get :index, xhr: true, format: :datatable, params: parameters
        end
      end

      context 'json request' do
        let(:parameters) { @auth_headers }

        it 'initializes the template searcher' do
          expect(TemplateSearcher).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:deleted)).and_call_original

          get :index, xhr: true, format: :json, params: parameters
        end

        it 'searches the templates' do
          expect_any_instance_of(TemplateSearcher).to receive(:search).and_call_original

          get :index, xhr: true, format: :json, params: parameters
        end

        it 'renders the index page' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to render_template(:index)
        end

        it 'returns ok status' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to have_http_status(:ok)
        end

        it 'renders the json structure' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_types('*', id: :string, name: :string, description: :string, deleted: :boolean, has_fields: :boolean)
        end

        it 'renders the templates data' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_sizes(3)
          expect_json('0', id: @template3.id, name: @template3.name, description: @template3.description, deleted: @template3.discarded?, has_fields: @template3.has_fields?)
          expect_json('1', id: @template.id, name: @template.name, description: @template.description, deleted: @template.discarded?, has_fields: @template.has_fields?)
          expect_json('2', id: @template2.id, name: @template2.name, description: @template2.description, deleted: @template2.discarded?, has_fields: @template2.has_fields?)
        end

        context 'when request using active param' do
          let(:parameters) { {active: true}.merge(@auth_headers) }

          it 'only return active templates' do
            get :index, xhr: true, format: :json, params: parameters.merge({ active: true })
            expect_json_sizes(2)
            expect_json('0', id: @template3.id, name: @template3.name, description: @template3.description, deleted: @template3.discarded?, has_fields: @template3.has_fields?)
            expect_json('1', id: @template.id, name: @template.name, description: @template.description, deleted: @template.discarded?, has_fields: @template.has_fields?)
          end

          it 'only return inactive templates' do
            get :index, xhr: true, format: :json, params: parameters.merge({ active: false })
            expect_json_sizes(1)
            expect_json('0', id: @template2.id, name: @template2.name, description: @template2.description, deleted: @template2.discarded?, has_fields: @template2.has_fields?)
          end
        end
      end
    end

    context 'with user' do
      context 'json request' do
        let(:parameters) { @user_auth_headers }

        it 'initializes the template searcher' do
          expect(TemplateSearcher).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:deleted)).and_call_original

          get :index, xhr: true, format: :json, params: parameters
        end

        it 'searches the templates' do
          expect_any_instance_of(TemplateSearcher).to receive(:search).and_call_original

          get :index, xhr: true, format: :json, params: parameters
        end

        it 'renders the index page' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to render_template(:index)
        end

        it 'returns ok status' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to have_http_status(:ok)
        end

        it 'renders the json structure' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_types('*', id: :string, name: :string, description: :string, deleted: :boolean, has_fields: :boolean)
        end

        it 'renders the templates data' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_sizes(3)
          expect_json('0', id: @template3.id, name: @template3.name, description: @template3.description, deleted: @template3.discarded?, has_fields: @template3.has_fields?)
          expect_json('1', id: @template.id, name: @template.name, description: @template.description, deleted: @template.discarded?, has_fields: @template.has_fields?)
          expect_json('2', id: @template2.id, name: @template2.name, description: @template2.description, deleted: @template2.discarded?, has_fields: @template2.has_fields?)
        end
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'template' => { 'name' => '', 'description' => '' } }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'foolalo' }.merge(@auth_headers) }

      it 'initializes the template service' do
        expect(TemplateService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:name, :description)).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'creates the template' do
        expect_any_instance_of(TemplateService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:created)
      end

      it 'returns the json structure' do
        post :create, xhr: true, params: parameters

        expect_json_types(id: :string)
      end

      it 'returns the template data' do
        post :create, xhr: true, params: parameters

        expect_json(id: Template.last.id)
      end
    end

    context 'with user' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'foolalo' }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PUT update' do
    before do
      @template = create(:template)
    end

    context 'with invalid parameters' do
      let(:parameters) {  { 'name' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @template.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @template.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'batz' }.merge(@auth_headers) }

      it 'initializes the template service' do
        expect(TemplateService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:name, :description)).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @template.id)
      end

      it 'updates the template' do
        expect_any_instance_of(TemplateService).to receive(:update).with(@template.id.to_s).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @template.id)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @template.id)

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with user' do
      let(:parameters) { { 'name' => 'Foobar', 'description' => 'batz' }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        put :update, xhr: true, params: parameters.merge(id: @template.id)

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { { id: @template.id }.merge(@auth_headers) }

    before do
      @template = create(:template)

      @field1 = create(:field, template: @template, order: 0)
      @field2 = create(:field, template: @template, order: 1, deleted_at: Time.zone.now)
      @field4 = create(:field, template: @template, order: 1)
      @field3 = create(:field, template: @template, type: :dropdown, order: 2, required: true, options: [{ label: 'Foo', value: 'foo', order: 0 }, { label: 'Bar', value: 'bar', order: 1 }])
      @field5 = create(:field, template: @template, type: :dropdown, order: 2, required: true, options: [{ label: 'Foo', value: 'foo', order: 0 }, { label: 'Bar', value: 'bar', order: 1 }], deleted_at: Time.zone.now)

      @business = create(:business, :with_dependencies)

      create(:step_template, step: create(:step, :with_dependencies, business: @business), template: @template)
    end

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(id: :string, name: :string, deleted: :boolean, has_fields: :boolean, fields: :array_of_objects, businesses: :array_of_objects)
      expect_json_types('fields.*', id: :string, type: :string, size: :string, label: :string, tooltip: :string, required: :boolean, deleted: :boolean,
        form_name: :string, order: :integer, options: :array_of_objects_or_null)
      expect_json_types('fields.?.options.*', label: :string, value: :string, order: :integer)
      expect_json_types('deleted_fields.*', id: :string, type: :string, size: :string, label: :string, tooltip: :string, required: :boolean, deleted: :boolean,
        form_name: :string, order: :integer, options: :array_of_objects_or_null)
      expect_json_types('deleted_fields.?.options.*', label: :string, value: :string, order: :integer)
    end

    it 'returns the template data with the active fields' do
      get :show, xhr: true, params: parameters

      expect_json(id: @template.id, name: @template.name, description: @template.description, deleted: @template.discarded?, has_fields: @template.has_fields?)

      expect_json('fields.0', id: @field1.id, label: @field1.label, type: @field1.type, size: @field1.size, order: @field1.order, tooltip: @field1.tooltip,
        form_name: @field1.form_name, required: @field1.required?, deleted: @field1.discarded?)
      expect_json('fields.1', id: @field4.id, label: @field4.label, type: @field4.type, size: @field4.size, order: @field4.order, tooltip: @field4.tooltip,
        form_name: @field4.form_name, required: @field4.required?, deleted: @field4.discarded?)
      expect_json('fields.2', id: @field3.id, label: @field3.label, type: @field3.type, size: @field3.size, order: @field3.order, tooltip: @field3.tooltip,
        form_name: @field3.form_name, required: @field3.required?, deleted: @field3.discarded?)
      expect_json('fields.2.options.0', label: 'Foo', value: 'foo', order: 0)
      expect_json('fields.2.options.1', label: 'Bar', value: 'bar', order: 1)

      expect_json('deleted_fields.0', id: @field2.id, label: @field2.label, type: @field2.type, size: @field2.size, order: @field2.order, tooltip: @field2.tooltip,
        form_name: @field2.form_name, required: @field2.required?, deleted: @field2.discarded?)
      expect_json('deleted_fields.1', id: @field5.id, label: @field5.label, type: @field5.type, size: @field5.size, order: @field5.order, tooltip: @field5.tooltip,
        form_name: @field5.form_name, required: @field5.required?, deleted: @field5.discarded?)
      expect_json('deleted_fields.1.options.0', label: 'Foo', value: 'foo', order: 0)
      expect_json('deleted_fields.1.options.1', label: 'Bar', value: 'bar', order: 1)

      expect_json('businesses.0', id: @business.id, name: @business.name)
    end

    context 'with user' do
      let(:parameters) { { id: @template.id }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        get :show, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @template.id }.merge(@auth_headers) }

    before do
      @template = create(:template)
    end

    context 'with error' do
      let(:template_service) { double(:template_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(TemplateService).to receive(:new).and_return(template_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the template service' do
        expect(TemplateService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the template' do
        expect_any_instance_of(TemplateService).to receive(:destroy).with(@template.id.to_s).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'with user' do
      let(:parameters) { { id: @template.id }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PATCH activate' do
    let(:parameters) { { id: @template.id }.merge(@auth_headers) }

    before do
      @template = create(:template, deleted_at: Time.zone.now)
    end

    context 'with error' do
      let(:template_service) { double(:template_service, restore: false, success: false, errors: ['foo']) }

      before do
        allow(TemplateService).to receive(:new).and_return(template_service)
      end

      it 'returns bad request status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        patch :activate, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the template service' do
        expect(TemplateService).to receive(:new).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'activates the template' do
        expect_any_instance_of(TemplateService).to receive(:restore).with(@template.id.to_s).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'returns no content status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'with user' do
      let(:parameters) { { id: @template.id }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
