require 'rails_helper'

RSpec.describe StepPermissionsController, type: :controller do
  let(:user) { create(:user) }
  let(:user2) { create(:user) }
  let(:department) { create(:department) }

  let(:step) { create(:step, :with_dependencies) }
  let(:permission1) { create(:step_permission, user: user, step: step, scope: 'edit') }
  let(:permission2) { create(:step_permission, department: department, step: step, scope: 'read') }
  let(:step_permission_searcher) { double(:step_permission_searcher, search: StepPermission.where(id: [permission1.id, permission2.id])) }

  before do
    @admin = create(:administrator)
    @auth_headers = @admin.create_new_auth_token
    @user_auth_headers = user.create_new_auth_token

    allow(StepPermissionSearcher).to receive(:new).and_return(step_permission_searcher)
  end

  describe 'GET index' do
    render_views

    context 'with business_id' do
      context 'json request' do
        let(:parameters) { @auth_headers.merge(business_id: step.business_id) }

        it 'renders the index page' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to render_template(:index)
        end

        it 'returns ok status' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to have_http_status(:ok)
        end

        it 'renders the json structure' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_types('*', id: :string)
          expect_json_types('*.department', optional(id: :string, name: :string))
          expect_json_types('*.user', optional(id: :string, name: :string))
          expect_json_types('*.step', id: :string, name: :string)
        end

        it 'renders the templates data' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_sizes(2)
          expect_json('0', id: permission1.id, department: nil)
          expect_json('0.user', id: permission1.user.id)
          expect_json('1', id: permission2.id, user: nil)
          expect_json('1.department', id: permission2.department.id)
        end
      end

      context 'with params' do
        let(:parameters) { @auth_headers.merge(business_id: step.business_id, user_id: user.id) }

        it 'initializes searcher' do
          expect(StepPermissionSearcher).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:business_id, :user_id))
          get :index, xhr: true, format: :json, params: parameters
        end
      end
    end
  end
  describe 'GET for_current_user' do
    render_views
    subject { get :for_current_user, xhr: true, format: :json, params: parameters }

    context 'with business_id' do
      context 'json request' do
        let(:parameters) { @user_auth_headers.merge(business_id: step.business_id) }

        it 'returns ok status' do
          subject
          expect(response).to have_http_status(:ok)
        end

        it 'step id and permission scope map' do
          subject
          expect_json(step.id, %w[edit read])
        end
      end

      context 'with params' do
        let(:parameters) { @user_auth_headers.merge(business_id: step.business_id, user_id: user.id) }

        it 'initializes searcher' do
          expect(StepPermissionSearcher).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:business_id).merge(user_id: user.id))
          subject
        end
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'step_id' => '', 'user_id' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'step_id' => step.id.to_s, 'user_id' => user.id.to_s }.merge(@auth_headers) }

      it 'initializes the step permission service' do
        expect(StepPermissionService).to receive(:new).with(
          ActionController::Parameters.new(parameters).permit(
            :step_id, :user_id, :department_id, :scope, :business_id, steps_id: [], users_id: [], departments_id: []
          )
        ).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'creates the step permission' do
        expect_any_instance_of(StepPermissionService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters
        expect(response).to have_http_status(:created)
      end
    end

    context 'with batch parameters (new contract)' do
      let(:user_b) { create(:user) }
      let(:department_b) { create(:department) }
      let(:step_b) { create(:step, :with_dependencies) }

      let(:parameters) do
        {
          'steps_id' => [step.id.to_s, step_b.id.to_s],
          'users_id' => [user.id.to_s, user_b.id.to_s],
          'departments_id' => [department.id.to_s, department_b.id.to_s],
          'scope' => 'edit'
        }.merge(@auth_headers)
      end

      it 'initializes the service with new contract params' do
        expected_permitted = ActionController::Parameters.new(parameters).permit(
          :step_id, :user_id, :department_id, :scope, :business_id, steps_id: [], users_id: [], departments_id: []
        )
        expect(StepPermissionService).to receive(:new).with(expected_permitted).and_call_original
        post :create, xhr: true, params: parameters
      end

      it 'creates batch permissions and returns created' do
        expect {
          post :create, xhr: true, params: parameters
        }.to change(StepPermission, :count)
        expect(response).to have_http_status(:created)
      end

      it 'is idempotent when records already exist' do
        post :create, xhr: true, params: parameters
        expect {
          post :create, xhr: true, params: parameters
        }.to_not change(StepPermission, :count)
        expect(response).to have_http_status(:created)
      end
    end

    context 'with user' do
      let(:parameters) { { 'step_id' => step.id.to_s, 'user_id' => user.id.to_s }.merge(@user_auth_headers) }

      it 'returns the unauthorized status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST create as update (unified flow)' do
    let(:user_b) { create(:user) }
    let(:department_b) { create(:department) }
    let(:step_b) { create(:step, :with_dependencies) }

    it 'updates scope for a batch (users set)' do
      create(:step_permission, step: step, user: user, scope: 'read')
      create(:step_permission, step: step_b, user: user_b, scope: 'read')
      parameters = { steps_id: [step.id, step_b.id], users_id: [user.id, user_b.id], scope: 'edit' }.merge(@auth_headers)
      post :create, xhr: true, params: parameters
      expect(response).to have_http_status(:created)
      expect(StepPermission.where(step_id: [step.id, step_b.id], user_id: [user.id, user_b.id]).pluck(:scope).uniq).to eq(['edit'])
    end

    it 'updates scope for a batch (departments set)' do
      create(:step_permission, step: step, department: department, scope: 'read')
      create(:step_permission, step: step_b, department: department_b, scope: 'read')
      parameters = { steps_id: [step.id, step_b.id], departments_id: [department.id, department_b.id], scope: 'approvement' }.merge(@auth_headers)
      post :create, xhr: true, params: parameters
      expect(response).to have_http_status(:created)
      expect(StepPermission.where(step_id: [step.id, step_b.id], department_id: [department.id, department_b.id]).pluck(:scope).uniq).to eq(['approvement'])
    end

    it 'updates scope for legacy single' do
      sp = create(:step_permission, step: step, user: user, scope: 'read')
      parameters = { step_id: step.id, user_id: user.id, scope: 'edit' }.merge(@auth_headers)
      post :create, xhr: true, params: parameters

      expect(response).to have_http_status(:created)
      expect(sp.reload.scope).to eq('edit')
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @step_permission.id }.merge(@auth_headers) }

    before do
      @step = create(:step, :with_dependencies)
      user = create(:user)
      @step_permission = create(:step_permission, step: @step, user: user)
    end

    context 'with error' do
      let(:step_permission_service) { double(:step_permission_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(StepPermissionService).to receive(:new).and_return(step_permission_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the step template service' do
        expect(StepPermissionService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the step template' do
        expect_any_instance_of(StepPermissionService).to receive(:destroy).with(@step_permission.id.to_s).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end
  end
end
