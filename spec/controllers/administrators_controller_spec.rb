require 'rails_helper'

RSpec.describe AdministratorsController, type: :controller do
  before do
    @admin = create(:administrator, name: '<PERSON><PERSON>')

    @auth_headers = @admin.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    before do
      @user = create(:administrator, name: '<PERSON>')
      @user2 = create(:administrator, name: '<PERSON>', deleted_at: Time.zone.now)
    end

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(datatable_columns) }

      it 'initializes the administrator datatable' do
        expect(AdministratorDatatable).to receive(:new).and_return(datatable)

        get :index, xhr: true, format: :datatable, params: parameters
      end
    end

    context 'json request' do
      let(:parameters) { @auth_headers }

      it 'renders the index page' do
        get :index, xhr: true, format: :json, params: parameters

        expect(response).to render_template(:index)
      end

      it 'returns ok status' do
        get :index, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :index, xhr: true, format: :json, params: parameters

        expect_json_types('*', id: :string, name: :string_or_null, email: :string, deleted: :boolean)
      end

      it 'renders the users data ordered by not deleted and name' do
        get :index, xhr: true, format: :json, params: parameters

        expect_json_sizes(3)
        expect_json('0', id: @admin.id, name: @admin.name, email: @admin.email, deleted: @admin.discarded?)
        expect_json('1', id: @user.id, name: @user.name, email: @user.email, deleted: @user.discarded?)
        expect_json('2', id: @user2.id, name: @user2.name, email: @user2.email, deleted: @user2.discarded?)
      end
    end

    context 'when request using active param' do
      let(:parameters) { {active: true}.merge(@auth_headers) }

      it 'only return active administrators' do
        get :index, xhr: true, format: :json, params: parameters.merge({ active: true })
        expect_json_sizes(2)
        expect_json('0', id: @admin.id, name: @admin.name, email: @admin.email, deleted: @admin.discarded?)
        expect_json('1', id: @user.id, name: @user.name, email: @user.email, deleted: @user.discarded?)
      end

      it 'only return inactive administrators' do
        get :index, xhr: true, format: :json, params: parameters.merge({ active: false })
        expect_json_sizes(1)
        expect_json('0', id: @user2.id, name: @user2.name, email: @user2.email, deleted: @user2.discarded?)
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '', 'email' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foobar', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>', 'limited' => 'true', 'coordinator' => 'true' }.merge(@auth_headers) }

      it 'initializes the user service' do
        expect(AdministratorService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:name, :email, :password, :password_confirmation)).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'creates the user' do
        expect_any_instance_of(AdministratorService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:created)
      end
    end

    context 'with invalid administrator in public tenant' do
      before do
        Apartment::Tenant.switch! 'public'
        normal_admin = create(:administrator, owner: false)
        @auth_headers_normal_admin = normal_admin.create_new_auth_token
      end

      let(:parameters) { { 'name' => '', 'email' => '' }.merge(@auth_headers_normal_admin) }

      it 'returns the unauthorized status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect(JSON.parse(response.body)).to eq('errors' => 'Você não possui permissão para acessar esse recurso')
      end
    end
  end

  describe 'PUT update' do
    before do
      @user = create(:administrator)
    end

    context 'with invalid parameters' do
      let(:parameters) {  { 'email' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @user.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @user.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      before do
        @user.update(provider: 'email')
      end

      let(:parameters) { { 'name' => 'Foobar', 'email' => '<EMAIL>', 'limited' => 'true', 'coordinator' => 'true' }.merge(@auth_headers) }

      it 'initializes the user service' do
        expect(AdministratorService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:name, :email, :password, :password_confirmation)).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @user.id)
      end

      it 'updates the user' do
        expect_any_instance_of(AdministratorService).to receive(:update).with(@user.id.to_s).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @user.id)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @user.id)

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid administrator in public tenant' do
      before do
        Apartment::Tenant.switch! 'public'
        normal_admin = create(:administrator, owner: false)
        @auth_headers_normal_admin = normal_admin.create_new_auth_token
      end

      let(:parameters) { { 'name' => '', 'email' => '' }.merge(@auth_headers_normal_admin) }

      it 'returns the unauthorized status' do
        put :update, xhr: true, params: parameters.merge(id: @user.id)

        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns the errors' do
        put :update, xhr: true, params: parameters.merge(id: @user.id)

        expect(JSON.parse(response.body)).to eq('errors' => 'Você não possui permissão para acessar esse recurso')
      end
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { { id: @user.id }.merge(@auth_headers) }

    before do
      @user = create(:administrator)
    end

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(id: :string, name: :string, email: :string, deleted: :boolean)
    end

    it 'returns the administrator data' do
      get :show, xhr: true, params: parameters

      expect_json(id: @user.id, name: @user.name, email: @user.email, deleted: @user.discarded?)
    end

    context 'with invalid administrator in public tenant' do
      before do
        Apartment::Tenant.switch! 'public'
        normal_admin = create(:administrator, owner: false)
        @auth_headers_normal_admin = normal_admin.create_new_auth_token
      end

      let(:parameters) { { id: @user.id }.merge(@auth_headers_normal_admin) }

      it 'returns the unauthorized status' do
        get :show, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns the errors' do
        get :show, xhr: true, params: parameters

        expect(JSON.parse(response.body)).to eq('errors' => 'Você não possui permissão para acessar esse recurso')
      end
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @user.id }.merge(@auth_headers) }

    before do
      @user = create(:administrator)
    end

    context 'with error' do
      let(:user_service) { double(:user_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(controller).to receive(:authorize).and_return(true)
        allow(AdministratorService).to receive(:new).and_return(user_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the user service' do
        expect(AdministratorService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the user' do
        expect_any_instance_of(AdministratorService).to receive(:destroy).with(@user.id.to_s).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'with invalid administrator in public tenant' do
      before do
        Apartment::Tenant.switch! 'public'
        normal_admin = create(:administrator, owner: false)
        @auth_headers_normal_admin = normal_admin.create_new_auth_token
      end

      let(:parameters) { { id: @user.id }.merge(@auth_headers_normal_admin) }

      it 'returns the unauthorized status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns the errors' do
        delete :destroy, xhr: true, params: parameters

        expect(JSON.parse(response.body)).to eq('errors' => 'Você não possui permissão para acessar esse recurso')
      end
    end
  end

  describe 'PATCH activate' do
    let(:parameters) { { id: @user.id }.merge(@auth_headers) }

    before do
      @user = create(:administrator, deleted_at: Time.zone.now)
    end

    context 'with error' do
      let(:user_service) { double(:user_service, restore: false, success: false, errors: ['foo']) }

      before do
        allow(AdministratorService).to receive(:new).and_return(user_service)
      end

      it 'returns bad request status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        patch :activate, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the user service' do
        expect(AdministratorService).to receive(:new).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'activates the user' do
        expect_any_instance_of(AdministratorService).to receive(:restore).with(@user.id.to_s).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'returns no content status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'with invalid administrator in public tenant' do
      before do
        Apartment::Tenant.switch! 'public'
        normal_admin = create(:administrator, owner: false)
        @auth_headers_normal_admin = normal_admin.create_new_auth_token
      end

      let(:parameters) { { id: @user.id }.merge(@auth_headers_normal_admin) }

      it 'returns the unauthorized status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns the errors' do
        patch :activate, xhr: true, params: parameters

        expect(JSON.parse(response.body)).to eq('errors' => 'Você não possui permissão para acessar esse recurso')
      end
    end
  end
end
