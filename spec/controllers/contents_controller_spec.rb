require 'rails_helper'

RSpec.describe ContentsController, type: :controller do
  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group:) }
  let(:business_param) { { 'business_id' => business.id } }
  let!(:step1) { create(:step, business:) }
  let!(:template) { create(:template, step_templates: [build(:step_template, step: step1)]) }
  let!(:field1) { create(:field, :random_type, template:, required: false) }
  let!(:field2) { create(:field, :random_type, template:, required: false) }

  before do
    @user = create(:user)

    @auth_headers = @user.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    let(:business2) { create(:business, business_group:) }
    let(:step2) { create(:step, business: business2) }

    before do
      @content = create(:content, :with_created_by, business:).tap do |content|
        create(:answer, user: create(:user), step: step1, position: 0, content:)
      end

      @content2 = create(:content, deleted_at: Time.zone.now, business:).tap do |content|
        create(:answer, user: create(:user), step: step1, position: 0, content:, values: { field1.id => 'bar', field2.id => 'batz' })
      end

      create(:step_permission, step: step1, user: @user)

      @content3 = create(:content, business: business2).tap do |content|
        create(:answer, user: create(:user), step: step2, position: 0, content:)
      end

      @content4 = create(:content, business:, draft: true).tap do |content|
        create(:answer, user: create(:user), step: step1, position: 0, content:)
      end
    end

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(business_param).merge(datatable_columns) }

      it 'initializes the content datatable' do
        expect(ContentDatatable).to receive(:new).and_return(datatable)

        get :index, xhr: true, format: :datatable, params: parameters
      end
    end

    context 'json request' do
      let(:parameters) { business_param.merge(search_params) }
      let(:grid_params) do
        {
          startRow: 0, endRow: 10, sort: [{ colId: 'status', sort: 'asc' }],
          search: {
            'b26de4cb-7af8-4281-8332-ce28e1a4f29d': {
              filterType: 'text', operator: 'AND',
              condition1: { filterType: 'text', type: 'contains', filter: 'bar' },
              condition2: { filterType: 'text', type: 'contains', filter: 'batz' }
            },
            'e430d9ad-82dd-4896-be8c-7d6a1f9321e0': { filterType: 'text', type: 'contains', filter: 'abcd' }
          }
        }
      end
      let(:search_params) { { grid_params: grid_params.to_json, query: 'foobar' }.with_indifferent_access }
      let(:searcher) { double(:searcher, search: [@content], metadata: { total: 1 }) }

      before { allow(ContentSearcher).to receive(:new).and_return(searcher) }

      it 'initializes the content searcher with the parameters' do
        expect(ContentSearcher).to receive(:new).with(hash_including(query: search_params[:query], business_id: business.id, user: @user)).and_return(searcher)

        get :index, xhr: true, format: :json, params: parameters.merge(@auth_headers)
      end

      it 'searches the contents' do
        expect(searcher).to receive(:search)

        get :index, xhr: true, format: :json, params: parameters.merge(@auth_headers)
      end

      it 'renders the index page' do
        get :index, xhr: true, format: :json, params: parameters.merge(@auth_headers)

        expect(response).to render_template(:index)
      end

      it 'returns ok status' do
        get :index, xhr: true, format: :json, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :index, xhr: true, format: :json, params: parameters.merge(@auth_headers)

        expect_json_types('meta', total: :integer)
        expect_json_types('data.*', id: :string, status: :string, note: :string_or_null, current_step: :string, updated_at: :string, created_by_name: :string_or_null, deletion_reason: :string_or_null)
      end

      it 'renders the contents' do
        get :index, xhr: true, format: :json, params: parameters.merge(@auth_headers)

        expect_json_sizes(2)
        expect_json('data.0', id: @content.id, status: @content.status, note: @content.note, current_step: @content.current_step_name, updated_at: I18n.l(@content.updated_at, format: :short_datatable, created_by_name: @content.created_by.name), deletion_reason: @content.deletion_reason)
      end
    end
  end

  describe 'GET all_values' do
    let(:answer) { create(:answer, :with_dependencies, user: create(:user), values: { 'foo' => 'bar' }) }
    let(:parameters) { { id: answer.content_id }.merge(@auth_headers) }

    it 'renders the contents' do
      get :all_values, xhr: true, format: :json, params: parameters

      expect_json_sizes(1)
      expect_json('foo', 'bar')
    end
  end

  describe 'GET note' do
    render_views

    let(:answer) { create(:answer, :with_dependencies, user: create(:user), values: { 'foo' => 'bar' }) }
    let(:parameters) { { id: answer.content_id }.merge(@auth_headers) }

    before { answer.content.update(note: 'bla bla bla') }

    it 'renders the contents' do
      get :note, xhr: true, format: :json, params: parameters

      expect_json(id: answer.content.id, business_id: answer.content.business_id, note: answer.content.note)
    end

    context 'when user is limited' do
      before do
        @user.update!(limited: true)

        answer.content.update(created_by_id:)

        get :note, xhr: true, format: :json, params: parameters
      end

      context 'and the content is not created by him' do
        let(:created_by_id) { create(:user).id }

        it { expect(response).to have_http_status(:forbidden) }
      end

      context 'and the content is created by him' do
        let(:created_by_id) { @user.id }

        it { expect(response).to have_http_status(:ok) }
      end
    end

    context 'when user department is limited' do
      let(:department) { create(:department, limited: true) }

      before do
        @user.departments << department
        @user.save!

        answer.content.update(created_by_id:)
      end

      def do_get
        get :note, xhr: true, format: :json, params: parameters
      end

      context 'and the content was not created by him' do
        context 'but it was created by another user of the same department' do
          let(:user) { create(:user) }
          let(:created_by_id) { user.id }

          before do
            user.departments << department
            user.save!
          end

          it 'returns ok status' do
            do_get

            expect(response).to have_http_status(:ok)
          end
        end

        context 'and it was created by another user of different department' do
          let(:user) { create(:user) }
          let(:department2) { create(:department) }
          let(:created_by_id) { user.id }

          before do
            user.departments << department2
            user.save!
          end

          it 'returns forbidden status' do
            do_get

            expect(response).to have_http_status(:forbidden)
          end
        end
      end

      context 'and the content was created by him' do
        let(:created_by_id) { @user.id }

        it { expect(response).to have_http_status(:ok) }
      end
    end
  end

  describe 'GET summary_values' do
    let(:answer) { create(:answer, :with_dependencies, user: create(:user), values: { 'foo' => 'bar' }) }
    let(:parameters) { { id: answer.content_id }.merge(@auth_headers) }

    it 'renders the contents' do
      get :summary_values, xhr: true, format: :json, params: parameters

      expect_json_sizes(1)
      expect_json(answer.step_id, values: { foo: 'bar' })
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { @auth_headers.merge('business_id' => '') }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:service) { double(:service, create: true, success: true, record: double(:record, id: '99')) }
      let(:parameters) { business_param.merge(field1.id => 'foo', field2.id => 'bar') }

      before { allow(ContentService).to receive(:new).and_return(service) }

      it 'initializes the content service' do
        expect(ContentService).to receive(:new) do |args|
          expect(args['note']).to eq(parameters['note'])
          expect(args['created_by_id']).to eq(@user.id)
          expect(args['remote_ip']).to eq(request.remote_ip)
          expect(args[field1.id]).to eq(parameters[field1.id])
          expect(args[field2.id]).to eq(parameters[field2.id])

          service
        end

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'creates the content' do
        expect(service).to receive(:create)

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:created)
      end

      it 'returns the created content id' do
        post :create, xhr: true, params: parameters.merge(@auth_headers)

        expect_json_types(id: :string)
        expect_json(id: service.record.id)
      end
    end
  end

  describe 'PUT update' do
    before { @content = create(:content, :with_dependencies) }

    context 'with invalid parameters' do
      let(:parameters) {  { 'name' => '' }.merge(@auth_headers) }

      before do
        allow_any_instance_of(ContentService).to receive(:update).and_return false
        allow_any_instance_of(ContentService).to receive(:errors).and_return ['error']
      end

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @content.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @content.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:service) { double(:service, update: true, success: true, record: @content.reload) }
      let(:parameters) { { 'note' => 'Foo', field1.id => 'foo', field2.id => 'bar' }.merge(business_param) }

      before { allow(ContentService).to receive(:new).and_return(service) }

      it 'initializes the service' do
        expect(ContentService).to receive(:new) do |args|
          expect(args['note']).to eq(parameters['note'])
          expect(args[field1.id]).to eq(parameters[field1.id])
          expect(args[field2.id]).to eq(parameters[field2.id])

          service
        end

        put :update, xhr: true, params: parameters.merge(id: @content.id).merge(@auth_headers)
      end

      it 'updates the content' do
        expect(service).to receive(:update).with(@content.id.to_s)

        put :update, xhr: true, params: parameters.merge(id: @content.id).merge(@auth_headers)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @content.id).merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET reference_detail' do
    let(:searcher_result) { ['field_label' => 'state', 'value' => 'SP'] }
    let(:parameters) { { id: content.id }.merge(@auth_headers) }
    let(:content) { create(:content, :with_dependencies) }

    before { allow_any_instance_of(ContentSearcher).to receive(:search_detail).and_return(searcher_result) }

    it 'search details' do
      expect_any_instance_of(ContentSearcher).to receive(:search_detail).with(content.id).and_return(searcher_result)

      get :reference_detail, xhr: true, params: parameters
    end

    it 'renders result' do
      get :reference_detail, xhr: true, params: parameters

      expect_json('0', field_label: 'state', value: 'SP')
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { { id: @content.id }.merge(@auth_headers) }
    let(:template) { create(:template) }
    let(:field) { create(:field, :random_type, template:) }

    let(:option1) { { 'label' => 'Option1', 'value' => 'op1', 'order' => 1 } }
    let(:option2) { { 'label' => 'Option2', 'value' => 'op2', 'order' => 2 } }
    let(:field2) { create(:field, template:, type: :dropdown, options: [option1, option2]) }

    let(:business2) { create(:business, business_group:) }
    let(:step2) { create(:step, business: business2) }
    let(:template2) { create(:template) }
    let(:field3) { create(:field, :random_type, template: template2) }

    let(:field4) { create(:field, template:, type: :reference, reference_business: business2, reference_field: field3, reference_value_field_id: field3.id) }

    before do
      StepTemplate.create(step_id: step2.id, template_id: template2.id)
      3.times do |index|
        create(:content, business: business2, deleted_at: index == 2 ? Time.zone.now : nil).tap do |content|
          values = { field3.id => "Field3-Value#{index}", 'bar' => 'batz' }
          create(:answer, :completed, step: step2, position: 0, content:, values:)
        end
      end

      @content = create(:content, business:).tap do |content|
        values = { field.id => 'bar', field2.id => 'op2' }
        create(:answer, :completed, step: step1, position: 0, content:, values:)
        create(:step_template, step: step1, template:)
      end

      create(:step_permission, step: step1, user: @user)
    end

    it 'loads the content' do
      expect(Content).to receive(:find).with(@content.id).and_call_original

      get :show, xhr: true, params: parameters
    end

    it 'assigns the content decorator' do
      get :show, xhr: true, params: parameters

      expect(assigns(:content)).to be_decorated_with ContentDecorator
    end

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(id: :string, name: :string, business_id: :string, current_step_name: :string, current_step: :integer, done: :boolean,
                        updated_at: :string, answers: :array_of_objects, deletion_reason: :string_or_null)
      expect_json_types('answers.*', id: :string, name: :string, description: :string, order: :integer, user_id: :string_or_null, status: :string, deleted: :boolean, templates: :array_of_objects)
      expect_json_types('answers.*.templates.*', id: :string)
    end

    it 'returns the content data' do
      get :show, xhr: true, params: parameters

      expect_json(id: @content.id, name: @content.business.translated_attribute('name'), business_id: @content.business_id, current_step_name: @content.current_step_name, current_step: @content.current_step,
                  updated_at: @content.last_update_at.iso8601, done: @content.done?, deletion_reason: @content.deletion_reason)

      answer = @content.answers.first

      expect_json('answers.0.id', answer.id)
      expect_json('answers.0.name', answer.step.name)
      expect_json('answers.0.description', answer.step.description)
      expect_json('answers.0.order', answer.position)
      expect_json('answers.0.user_id', answer.user_id)
      expect_json('answers.0.status', answer.status)
      expect_json('answers.0.deleted', answer.discarded?)

      expect_json('answers.0.templates.0', id: template.id)
    end

    context 'when user is limited' do
      before do
        @user.update!(limited: true)

        @content.update(created_by_id:)

        get :show, xhr: true, params: parameters
      end

      context 'and the content is not created by him' do
        let(:created_by_id) { create(:user).id }

        it { expect(response).to have_http_status(:forbidden) }
      end

      context 'and the content is created by him' do
        let(:created_by_id) { @user.id }

        it { expect(response).to have_http_status(:ok) }
      end
    end

    context 'when user department is limited' do
      let(:department) { create(:department, limited: true) }

      before do
        @user.departments << department
        @user.save!

        @content.update(created_by_id:)
      end

      def do_get
        get :show, xhr: true, params: parameters
      end

      context 'and the content was not created by him' do
        context 'but it was created by another user of the same department' do
          let(:user) { create(:user) }
          let(:created_by_id) { user.id }

          before do
            user.departments << department
            user.save!
          end

          it 'returns ok status' do
            do_get

            expect(response).to have_http_status(:ok)
          end
        end

        context 'and it was created by another user of different department' do
          let(:user) { create(:user) }
          let(:department2) { create(:department) }
          let(:created_by_id) { user.id }

          before do
            user.departments << department2
            user.save!
          end

          it 'returns forbidden status' do
            do_get

            expect(response).to have_http_status(:forbidden)
          end
        end
      end

      context 'and the content was created by him' do
        let(:created_by_id) { @user.id }

        it { expect(response).to have_http_status(:ok) }
      end
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @content.id, deletion_reason: 'foobar' }.merge(@auth_headers).merge(business_param) }

    before do
      @content = create(:content, business:)
    end

    context 'with error' do
      let(:content_service) { double(:content_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(ContentService).to receive(:new).and_return(content_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the content service' do
        expect(ContentService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the content' do
        expect_any_instance_of(ContentService).to receive(:destroy).with(@content.id.to_s, ActionController::Parameters.new({ deletion_reason: 'foobar', current_user: @user, current_user_ip: '0.0.0.0' }).permit!).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end
  end

  describe 'GET show_on_list_values' do
    render_views

    let(:parameters) { @auth_headers.merge(id: '1234568793213') }
    let(:content) { double(:content, show_on_list_values: { '123' => 'abc' }) }

    it 'finds content' do
      finder = double
      expect(Content).to receive(:includes).with(answers: :step).and_return(finder)
      expect(finder).to receive(:find).with(parameters[:id]).and_return(content)
      expect(content).to receive(:show_on_list_values)

      get :show_on_list_values, xhr: true, format: :json, params: parameters
    end

    it 'renders content show_on_list_values' do
      finder = double(find: content)
      allow(Content).to receive(:includes).with(answers: :step).and_return(finder)

      get :show_on_list_values, xhr: true, format: :json, params: parameters
      expect(JSON.parse(response.body)).to eq(content.show_on_list_values)
    end
  end

  describe 'GET show_on_form_values' do
    render_views

    let(:parameters) { @auth_headers.merge(id: '1234568793213') }
    let(:content) { double(:content, show_on_form_values: { '123' => 'abc' }) }

    before do
      allow(Content).to receive(:find).with(parameters[:id]).and_return(content)
    end

    it 'finds content' do
      expect(Content).to receive(:find).with(parameters[:id]).and_return(content)
      expect(content).to receive(:show_on_form_values)

      get :show_on_form_values, xhr: true, format: :json, params: parameters
    end

    it 'renders content show_on_form_values' do
      get :show_on_form_values, xhr: true, format: :json, params: parameters

      expect(JSON.parse(response.body)).to eq(content.show_on_form_values)
    end

    context 'with discarded content' do
      let(:discarded_content) { create(:content, business:, deleted_at: Time.zone.yesterday) }
      let(:parameters) { { id: discarded_content.id.to_s, with_discarded: 'true' }.merge(@auth_headers) }

      it 'finds content with discarded' do
        expect(discarded_content.discarded?).to be_truthy
        expect(Content).to receive_message_chain(:with_discarded, :find).with(parameters[:id]).and_return(discarded_content)

        get :show_on_form_values, xhr: true, format: :json, params: parameters
      end
    end
  end

  describe 'GET show_modifications' do
    render_views

    let(:parameters) { @auth_headers.merge(id: '1234568793213') }
    let(:content) { double(:content, answers: []) }

    before do
      allow(Content).to receive(:find).with(parameters[:id]).and_return(content)
    end

    it 'finds content' do
      expect(Content).to receive(:find).with(parameters[:id]).and_return(content)

      get :show_modifications, xhr: true, format: :json, params: parameters
    end

    it 'renders content show_modifications' do
      get :show_modifications, xhr: true, format: :json, params: parameters

      expect(response).to render_template('contents/show_modifications')
    end
  end

  describe 'PATCH restore' do
    let(:content) { create(:content, business:, deleted_at: Time.zone.yesterday) }
    let(:parameters) { { 'id' => content.id.to_s }.merge(@auth_headers) }
    let(:service) { double(:service, restore: true, success: true) }

    def do_patch
      patch :restore, xhr: true, format: :json, as: :json, params: parameters
    end

    before { allow(ContentService).to receive(:new).and_return(service) }

    it 'initializes the service' do
      expect(ContentService).to receive(:new).and_return(service)

      do_patch
    end

    it 'tries to call restore on service' do
      expect(service).to receive(:restore).with(content.id, { current_user: @user, current_user_ip: '0.0.0.0' })

      do_patch
    end

    context 'with error' do
      let(:service) { double(:service, restore: false, success: false, errors: { foo: 'bar' }) }

      before { do_patch }

      it { expect(response).to have_http_status(:bad_request) }
    end

    context 'with success' do
      before { do_patch }

      it { expect(response).to have_http_status(:no_content) }
    end
  end
end
