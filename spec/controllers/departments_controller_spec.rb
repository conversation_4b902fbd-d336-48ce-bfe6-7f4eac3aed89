require 'rails_helper'

RSpec.describe DepartmentsController, type: :controller do
  before do
    @department = create(:department, name: 'Abc')
    @department2 = create(:department, name: 'Bcd')

    @user = create(:user)
    @user.departments << @department2
    @user.save!

    @admin = create(:administrator)

    @auth_headers = @admin.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(datatable_columns) }

      it 'initializes the department datatable' do
        expect(DepartmentDatatable).to receive(:new).and_return(datatable)

        get :index, xhr: true, format: :datatable, params: parameters
      end
    end

    context 'json request' do
      let(:parameters) { @auth_headers }

      it 'renders the index page' do
        get :index, xhr: true, format: :json, params: parameters

        expect(response).to render_template(:index)
      end

      it 'returns ok status' do
        get :index, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :index, xhr: true, format: :json, params: parameters

        expect_json_types('*', id: :string, name: :string)
      end

      it 'renders the departments data' do
        get :index, xhr: true, format: :json, params: parameters

        expect_json_sizes(2)
        expect_json('0', id: @department.id, name: @department.name)
        expect_json('1', id: @department2.id, name: @department2.name)
      end

      context 'with_users=true parameter' do
        let(:parameters) { @auth_headers.merge(with_users: true) }

        it 'returns only departments with users' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_sizes(1)
          expect_json('0', id: @department2.id, name: @department2.name)
        end
      end

      context 'when request using active param' do
        let(:parameters) { {active: true}.merge(@auth_headers) }

        it 'only return active departments' do
          @department2.update! deleted_at: Time.zone.now

          get :index, xhr: true, format: :json, params: parameters.merge({ active: true })

          expect_json_sizes(1)
          expect_json('0', id: @department.id, name: @department.name)
          # expect_json('1', id: @department2.id, name: @department2.name)
        end

        it 'only return inactive departments' do
          @department.update! deleted_at: Time.zone.now
          get :index, xhr: true, format: :json, params: parameters.merge({ active: false })
          expect_json_sizes(1)
          expect_json('0', id: @department.id, name: @department.name)
        end
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '', 'limited' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foobar', 'limited' => 'true' }.with_indifferent_access }

      it 'initializes the department service' do
        expect(DepartmentService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :limited
        )).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'creates the department' do
        expect_any_instance_of(DepartmentService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:created)
      end
    end
  end

  describe 'PUT update' do
    context 'with invalid parameters' do
      let(:parameters) {  { 'name' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @department.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @department.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foobar', 'limited' => 'true' }.with_indifferent_access }

      it 'initializes the department service' do
        expect(DepartmentService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :limited
        )).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @department.id).merge(@auth_headers)
      end

      it 'updates the department' do
        expect_any_instance_of(DepartmentService).to receive(:update).with(@department.id.to_s).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @department.id).merge(@auth_headers)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @department.id).merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { { id: @department.id }.merge(@auth_headers) }

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(id: :string, name: :string, limited: :boolean)
    end

    it 'returns the department data' do
      get :show, xhr: true, params: parameters

      expect_json(id: @department.id, name: @department.name, limited: @department.limited?)
    end
  end
end
