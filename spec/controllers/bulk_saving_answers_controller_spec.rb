require 'rails_helper'

RSpec.describe BulkSavingAnswersController, type: :controller do
  let(:step) { create(:step, :with_dependencies) }
  let(:field) { create(:field, :with_dependencies) }

  before do
    @admin = create(:administrator)
    @user = create(:user)

    @auth_headers = @admin.create_new_auth_token
    @user_auth_headers = @user.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    let(:parameters) { @user_auth_headers }

    context 'json request' do
      it 'returns no_content' do
        get :index, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @user_auth_headers.merge(datatable_columns) }

      it 'initializes the bulk saving datatable' do
        expect(BulkSavingAnswerDatatable).to receive(:new).and_return(datatable)

        get :index, xhr: true, format: :datatable, params: parameters
      end

      it 'returns ok status' do
        get :index, xhr: true, format: :datatable, params: parameters

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { @user_auth_headers.merge(id: @bulk_saving_answer.id) }

    before do
      @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies)
    end

    it 'loads the bulk_saving_answer' do
      expect(BulkSavingAnswer).to receive(:find).with(@bulk_saving_answer.id).and_call_original

      get :show, xhr: true, params: parameters
    end

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(id: :string, end_at: :string, start_at: :string)
      expect_json_types('step', id: :string, name: :string, description: :string)
      expect_json_types('step.business', id: :string, name: :string)
    end

    it 'returns the content data' do
      get :show, xhr: true, params: parameters

      expect_json(id: @bulk_saving_answer.id, end_at: @bulk_saving_answer.end_at.iso8601, start_at: @bulk_saving_answer.start_at.iso8601)
      expect_json('step', id: @bulk_saving_answer.step_id, name: @bulk_saving_answer.step.name)
      expect_json('step.business', id: @bulk_saving_answer.step.business.id, name: @bulk_saving_answer.step.business.name)
    end
  end

  describe 'POST fill' do
    before { create(:step_template, step: step, template: field.template) }

    context 'with invalid parameters' do
      let(:parameters) { @user_auth_headers.merge('step_id' => '-1') }

      it 'returns the unprocessable entity status' do
        post :fill, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :fill, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { @user_auth_headers.merge(step_id: step.id, answers: [{ field.id => 'value1' }]) }

      it 'initializes the content service' do
        expect(BulkAnswerService).to receive(:new).with(hash_including(step_id: step.id.to_s)).and_call_original

        post :fill, xhr: true, params: parameters
      end

      it 'creates the bulk saving answer' do
        expect_any_instance_of(BulkAnswerService).to receive(:pre_filled).and_call_original

        post :fill, xhr: true, params: parameters
      end

      it 'returns the created status' do
        post :fill, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'POST create' do
    before { create(:step_template, step: step, template: field.template) }

    context 'with invalid parameters' do
      let(:parameters) { @user_auth_headers.merge('step_id' => '-1') }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { @user_auth_headers.merge(step_id: step.id, answers: [{ field.id => 'value1' }]) }

      it 'initializes the content service' do
        expect(BulkAnswerService).to receive(:new).with(hash_including(step_id: step.id.to_s)).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'creates the bulk saving answer' do
        expect_any_instance_of(BulkAnswerService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'PUT update' do
    before do
      create(:step_template, step: step, template: field.template)

      @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies)
      @answer_processing = create(:answer_processing, status: :failed, bulk_saving_answer: @bulk_saving_answer)
    end

    context 'with invalid parameters' do
      let(:parameters) { @user_auth_headers.merge('step_id' => '-1', id: @bulk_saving_answer.id) }

      it 'returns ok status (no changes to process)' do
        put :update, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
        expect(response.body).to be_blank
      end
    end

    context 'with valid parameters' do
      let(:parameters) { @user_auth_headers.merge(id: @bulk_saving_answer.id, step_id: step.id, answers: [{ id: @answer_processing.id, field.id => 'value1' }]) }

      it 'initializes the content service' do
        expect(BulkAnswerService).to receive(:new).with(hash_including(step_id: step.id.to_s)).and_call_original

        put :update, xhr: true, params: parameters
      end

      it 'creates the bulk saving answer' do
        expect_any_instance_of(BulkAnswerService).to receive(:update).with(@bulk_saving_answer.id).and_call_original

        put :update, xhr: true, params: parameters
      end

      it 'returns the created status' do
        put :update, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'PUT process_orphans' do
    let(:parameters) { @user_auth_headers.merge(id: @bulk_saving_answer.id) }

    before do
      create(:step_template, step: step, template: field.template)

      @bulk_saving_answer = create(:bulk_saving_answer, :with_step_dependencies)
      @answer_processing = create(:answer_processing, status: :failed, bulk_saving_answer: @bulk_saving_answer)
    end

    context 'with valid parameters' do
      it 'initializes the content service' do
        expect(BulkAnswerService).to receive(:new).with({ user_id: @user.id }).and_call_original

        put :process_orphans, xhr: true, params: parameters
      end

      it 'process_orphans' do
        expect_any_instance_of(BulkAnswerService).to receive(:process_orphans).with(@bulk_saving_answer.id).and_call_original

        put :process_orphans, xhr: true, params: parameters
      end

      it 'returns the ok status' do
        put :process_orphans, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid parameters' do
      let(:service) { double(:service, success: false, process_orphans: false, errors: ['foo']) }

      before { allow(BulkAnswerService).to receive(:new).and_return(service) }

      it 'returns the unprocessable entity status' do
        put :process_orphans, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        put :process_orphans, xhr: true, params: parameters

        expect(JSON.parse(response.body)['errors']).to eq(['foo'])
      end
    end
  end

  describe 'POST bulk_alteration_preview' do
    render_views

    let(:business) { create(:business, :with_dependencies) }
    let(:step) { create(:step, business: business) }
    let!(:step_template) { create(:step_template, template: template, step: step, order: 0) }
    let(:template) { create(:template) }
    let(:field) { create(:field, label: 'field', template: template, type: :text) }
    let(:field2) { create(:field, label: 'field2', template: template, type: :integer) }
    let(:field3) { create(:field, label: 'field3', template: template, type: :text) }

    let(:criterions) do
      [
        { step_id: step.id, field_id: field.id, value: 'bar', operator: 'equals' },
        { step_id: step.id, field_id: field2.id, value: '5', operator: 'greaterThan' }
      ]
    end

    let(:parameters) { { business_id: business.id, criterions: criterions }.merge(@user_auth_headers) }

    before do
      create(:content, business: business).tap do |content|
        values = { field.id => 'bar', field2.id =>  '10', field3.id => 'foo' }

        @answer = create(:answer, :completed, step: step, position: 0, content: content, values: values)
      end
    end

    it 'renders bulk_alteration_preview' do
      post :bulk_alteration_preview, xhr: true, format: :json, params: parameters

      expect(JSON.parse(response.body)).to eq('count' => 1)
    end
  end

  describe 'POST bulk_alteration' do
    render_views

    let(:business) { create(:business, :with_dependencies) }
    let(:step) { create(:step, business: business) }
    let!(:step_template) { create(:step_template, template: template, step: step, order: 0) }
    let(:template) { create(:template) }
    let(:field) { create(:field, label: 'field', template: template, type: :text) }
    let(:field2) { create(:field, label: 'field2', template: template, type: :text) }
    let(:field3) { create(:field, label: 'field3', template: template, type: :text) }

    let(:criterions) do
      [
        { step_id: step.id, field_id: field.id, value: 'bar', operator: 'equals' },
        { step_id: step.id, field_id: field2.id, value: 'abc', operator: 'equals' },
        { step_id: step.id, field_id: field3.id, value: 'foo', operator: 'equals' }
      ]
    end

    let(:parameters) { { business_id: business.id, bulk_action: 'inactivate', criterions: criterions }.merge(@user_auth_headers) }

    before do
      allow(BulkAnswerService).to receive(:new).and_call_original

      create(:content, business: business).tap do |content|
        values = { field.id => 'bar', field2.id => 'abc', field3.id => 'foo' }

        @answer = create(:answer, :completed, step: step, position: 0, content: content, values: values)
      end
    end

    context 'with invalid parameters' do
      let(:parameters) { { business_id: '-1', bulk_action: 'inactivate', criterions: criterions }.merge(@user_auth_headers) }

      it 'returns the unprocessable entity status' do
        post :bulk_alteration, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :bulk_alteration, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { business_id: business.id, bulk_action: 'inactivate', criterions: criterions, verification_url: Faker::Internet.url, validation_url: Faker::Internet.url }.with_indifferent_access }

      it 'initializes the business service' do
        expect(BulkAnswerService).to receive(:new).with(ActionController::Parameters.new(parameters.merge(user_id: @user.id, ip: '0.0.0.0')).permit(
          :user_id, :ip, :business_id, :bulk_action, :validation_url, :verification_url, alterations: [:step_id, :field_id, :field_label, :label_value, :value, { value: [] }],
          criterions: [:step_id, :field_id, :operator, :text, :value, { value: [] }], approvals: [:step_id]
        )
        ).and_call_original

        post :bulk_alteration, xhr: true, params: parameters.merge(@user_auth_headers)
      end

      it 'bulk_alteration the business' do
        expect_any_instance_of(BulkAnswerService).to receive(:bulk_alteration).and_call_original

        post :bulk_alteration, xhr: true, params: parameters.merge(@user_auth_headers)
      end

      it 'returns the ok status' do
        post :bulk_alteration, xhr: true, params: parameters.merge(@user_auth_headers)

        expect(response).to have_http_status(:ok)
      end

      context 'with array value for criterions' do
        let(:criterions) { [{ step_id: step.id, field_id: field.id, value: %w[bar foo], operator: 'equals' }] }

        it 'initializes the business service' do
          allow(BulkAnswerService).to receive(:new) do |args|
            expect(args[:criterions].first[:value]).to eq(%w[bar foo])

            double(bulk_alteration: true, success: true)
          end

          post :bulk_alteration, xhr: true, params: parameters
        end
      end

      context 'with array value for alterations' do
        let(:parameters) { { business_id: business.id, bulk_action: 'updating', alterations: [{ step_id: step.id, value: %w[bar foo] }] }.merge(@user_auth_headers) }

        it 'initializes the business service' do
          allow(BulkAnswerService).to receive(:new) do |args|
            expect(args[:alterations].first[:value]).to eq(%w[bar foo])
            double(bulk_alteration: true, success: true)
          end

          post :bulk_alteration, xhr: true, params: parameters
        end
      end
    end
  end
end
