require 'rails_helper'

RSpec.describe AnswerVersionDatatable, type: :model do
  def datatable_params(extra = {})
    ActionController::Parameters.new({
      search: { value: '' },
      columns: {
        '0' => { 'data' => 'id', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } },
        '1' => { 'data' => 'key_fields', 'searchable' => 'true', 'orderable' => 'false', 'search' => { 'value' => '', 'regex' => 'false' } }
      }
    }.deep_merge(extra))
  end

  before do
    allow(Elasticsearch::BusinessSetupWorker).to receive(:perform_in)
  end

  let!(:answer_version) do
    av = create(:answer_version, :with_dependencies, :filled)
    av.update!(
      form_values: (av.form_values || {}).merge('__spec_key__' => 'needle-xyz'),
      values: (av.values || {}).merge('__spec_id__' => 'needle-xyz')
    )
    av
  end

  describe '#filter_records' do
    it 'filters by term present in answer_versions (form_values/values) without using content_values' do
      params = datatable_params(search: { value: 'needle-xyz' })

      datatable = described_class.new(params)
      records = datatable.send(:filter_records, datatable.send(:base_scope))

      expect(records.pluck(:id)).to include(answer_version.id)
    end

    it 'respects existing filters (business_id, step_id, whodunnit_id)' do
      params = datatable_params(
        search: { value: 'needle-xyz' },
        business_id: answer_version.answer.content.business_id,
        step_id: answer_version.answer.step_id,
        whodunnit_id: answer_version.whodunnit_id
      )

      datatable = described_class.new(params)
      records = datatable.send(:filter_records, datatable.send(:base_scope))
      expect(records.pluck(:id)).to include(answer_version.id)
    end

    it 'respects date range' do
      from = (answer_version.created_at - 1.day).to_date.to_s
      to   = (answer_version.created_at + 1.day).to_date.to_s

      params = datatable_params(
        start_on: from,
        end_on: to,
        search: { value: 'needle-xyz' }
      )

      datatable = described_class.new(params)
      records = datatable.send(:filter_records, datatable.send(:base_scope))
      expect(records.pluck(:id)).to include(answer_version.id)
    end
  end
end

