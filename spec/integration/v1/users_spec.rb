# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V1 Users API', type: :request, swagger_doc: 'v1/swagger.yaml' do
  let(:department) { create(:department) }
  let(:test_user) { create(:user, departments: [department]) }
  let(:Authorization) { 'Basic test_api_key' }

  before do
    Apartment::Tenant.switch! 'test'
    Company.current.update!(api_key: 'test_api_key')
  end

  path '/external/users.json' do
    post 'Cria um novo usuário' do
      tags 'Users'
      description 'Cria um novo usuário no sistema'
      operationId 'createUser'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :user, in: :body, schema: { '$ref' => '#/components/schemas/UserV1' }

      response '201', 'Usuário criado com sucesso' do
        schema '$ref' => '#/components/schemas/UserResponseV1'

        let(:user) do
          {
            name: "<PERSON>",
            email: "<EMAIL>",
            password: "Teste123@@**********!",
            password_confirmation: "Teste123@@**********!",
            department_ids: [department.id],
            provider: "email",
            limited: false,
            coordinator: false,
            notification: false,
            approved: true,
            create_confirmed: false,
            block_menus: [
              "dashboard",
              "search",
              "favorites",
              "bulk_saving_answers",
              "answer_versions",
              "report",
              "statistics"
            ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponseV1'

        context 'quando dados são inválidos' do
          let(:user) do
            {
              name: "",
              email: "invalid_email",
              provider: "email",
              password: "123",
              password_confirmation: "456"
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
            expect(data['errors']).to be_an(Array)
          end
        end
      end
    end
  end

  path '/external/users/{id}.json' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do usuário'

    put 'Atualiza um usuário' do
      tags 'Users'
      description 'Atualiza um usuário existente no sistema'
      operationId 'updateUser'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :user, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Nome completo do usuário' },
          email: { type: :string, format: :email, description: 'Email do usuário' },
          password: { type: :string, description: 'Nova senha do usuário' },
          password_confirmation: { type: :string, description: 'Confirmação da nova senha' },
          department_ids: {
            type: :array,
            items: { type: :string, format: :uuid },
            description: 'IDs dos departamentos do usuário'
          },
          provider: { type: :string, description: 'Provedor de autenticação' },
          limited: { type: :boolean, description: 'Se o usuário tem acesso limitado' },
          coordinator: { type: :boolean, description: 'Se o usuário é coordenador' },
          notification: { type: :boolean, description: 'Se o usuário recebe notificações' },
          approved: { type: :boolean, description: 'Se o usuário foi aprovado' },
          chat_enabled: { type: :boolean, description: 'Se o chat está habilitado' },
          block_menus: {
            type: :array,
            items: { type: :string },
            description: 'Menus bloqueados para o usuário'
          }
        }
      }

      response '200', 'Usuário atualizado com sucesso' do
        let(:id) { test_user.id }
        let(:user) do
          {
            name: "Nome Atualizado",
            limited: true,
            coordinator: true
          }
        end

        run_test! do |response|
          expect(response.status).to eq(200)
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponseV1'

        let(:id) { test_user.id }
        let(:user) do
          {
            name: "",
            email: "invalid_email"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('errors')
          expect(data['errors']).to be_an(Array)
        end
      end
    end

    delete 'Remove um usuário' do
      tags 'Users'
      description 'Remove um usuário do sistema (soft delete)'
      operationId 'deleteUser'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'

      response '204', 'Usuário removido com sucesso' do
        let(:id) { test_user.id }

        run_test! do |response|
          expect(response.status).to eq(204)
        end
      end

      response '404', 'Usuário não encontrado' do
        schema '$ref' => '#/components/schemas/NotFoundResponseV1'

        let(:id) { 'invalid_id' }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('status')
          expect(data['status']).to eq(404)
        end
      end
    end
  end
end
