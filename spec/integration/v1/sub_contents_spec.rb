# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V1 Sub Contents API', type: :request, swagger_doc: 'v1/swagger.yaml' do
  let(:business_group) { create(:business_group) }
  let(:parent_business) { create(:business, business_group: business_group) }
  let(:sub_business) { create(:business, business_group: business_group, sub_business: true) }
  let(:parent_step) { create(:step, business: parent_business, order: 0) }
  let(:parent_template) { create(:template) }
  let(:sub_business_field) { create(:field, template: parent_template, type: :sub_business, reference_sub_business: sub_business) }
  let(:step) { create(:step, business: sub_business, order: 0) }
  let(:template) { create(:template) }
  let(:field) { create(:field, template: template, type: :text) }
  let(:user) { create(:user) }
  let(:parent_content) { create(:content, business: parent_business, created_by: user) }
  let(:sub_content) { create(:content, business: sub_business, parent: parent_content, created_by: user) }
  let(:answer) { create(:answer, content: sub_content, step: step, user: user, values: { field.id => 'test value' }) }
  let(:Authorization) { 'Basic test_api_key' }

  before do
    Apartment::Tenant.switch! 'test'
    Company.current.update!(api_key: 'test_api_key')
    create(:step_template, step: parent_step, template: parent_template)
    create(:step_template, step: step, template: template)
    sub_business_field
    answer
  end

  path '/external/sub_contents.xml' do
    get 'Lista sub-conteúdos em formato XML' do
      tags 'Sub Contents'
      description 'Retorna uma lista de sub-conteúdos em formato XML com base nos parâmetros fornecidos'
      operationId 'getSubContents'
      produces 'application/xml'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :sub_business_id, in: :query, type: :string, format: :uuid, required: true, description: 'ID do sub-business'
      parameter name: :contents, in: :query, type: :boolean, required: false, description: 'Se deve incluir conteúdos na resposta', default: true
      parameter name: :structures, in: :query, type: :boolean, required: false, description: 'Se deve incluir estruturas na resposta', default: false
      parameter name: :deleted, in: :query, type: :boolean, required: false, description: 'Se deve incluir conteúdos deletados', default: false
      parameter name: :start, in: :query, type: :string, format: 'date-time', required: false, description: 'Data de início para filtrar por updated_at'
      parameter name: :end, in: :query, type: :string, format: 'date-time', required: false, description: 'Data de fim para filtrar por updated_at'
      parameter name: :content_id, in: :query, type: :string, format: :uuid, required: false, description: 'ID específico do conteúdo'
      parameter name: :sub_content_id, in: :query, type: :string, format: :uuid, required: false, description: 'ID específico do sub-conteúdo'
      parameter name: :parent_id, in: :query, type: :string, format: :uuid, required: false, description: 'ID do conteúdo pai'
      parameter name: :page, in: :query, type: :integer, required: false, description: 'Número da página para paginação', default: 1

      response '200', 'Sub-conteúdos retornados com sucesso' do
        schema '$ref' => '#/components/schemas/SubContentXmlResponseV1'

        let(:sub_business_id) { sub_business.id }
        let(:contents) { true }
        let(:structures) { false }

        run_test! do |response|
          expect(response.status).to eq(200)
          expect(response.content_type).to include('application/xml')
          expect(response.headers['total-records']).to be_present
          expect(response.headers['pages']).to be_present

          xml_doc = Nokogiri::XML(response.body)
          expect(xml_doc.xpath('//fourmdg')).to be_present
          expect(xml_doc.xpath('//business')).to be_present
          expect(xml_doc.xpath('//contents')).to be_present
        end
      end

      response '200', 'Sub-conteúdos com estruturas retornados com sucesso' do
        schema '$ref' => '#/components/schemas/SubContentXmlResponseV1'

        let(:sub_business_id) { sub_business.id }
        let(:contents) { true }
        let(:structures) { true }

        run_test! do |response|
          expect(response.status).to eq(200)
          expect(response.content_type).to include('application/xml')

          xml_doc = Nokogiri::XML(response.body)
          expect(xml_doc.xpath('//fourmdg')).to be_present
          expect(xml_doc.xpath('//business')).to be_present
          expect(xml_doc.xpath('//contents')).to be_present
          expect(xml_doc.xpath('//structures')).to be_present
          expect(xml_doc.xpath('//structures/steps')).to be_present
          expect(xml_doc.xpath('//structures/templates')).to be_present
        end
      end

      response '200', 'Sub-conteúdo específico retornado com sucesso' do
        schema '$ref' => '#/components/schemas/SubContentXmlResponseV1'

        let(:sub_business_id) { sub_business.id }
        let(:sub_content_id) { sub_content.id }
        let(:contents) { true }

        run_test! do |response|
          expect(response.status).to eq(200)
          expect(response.content_type).to include('application/xml')

          xml_doc = Nokogiri::XML(response.body)
          content_node = xml_doc.xpath('//content').first
          expect(content_node['guid']).to eq(sub_content.id)
          expect(content_node['parent_id']).to eq(parent_content.id)
        end
      end

      response '200', 'Sub-conteúdos filtrados por conteúdo pai retornados com sucesso' do
        schema '$ref' => '#/components/schemas/SubContentXmlResponseV1'

        let(:sub_business_id) { sub_business.id }
        let(:parent_id) { parent_content.id }
        let(:contents) { true }

        run_test! do |response|
          expect(response.status).to eq(200)
          expect(response.content_type).to include('application/xml')

          xml_doc = Nokogiri::XML(response.body)
          expect(xml_doc.xpath('//fourmdg')).to be_present
          expect(xml_doc.xpath('//business')).to be_present
        end
      end

      response '200', 'Sub-conteúdos filtrados por data retornados com sucesso' do
        schema '$ref' => '#/components/schemas/SubContentXmlResponseV1'

        let(:sub_business_id) { sub_business.id }
        let(:start) { 1.day.ago.iso8601 }
        let(:end) { 1.day.from_now.iso8601 }
        let(:contents) { true }

        run_test! do |response|
          expect(response.status).to eq(200)
          expect(response.content_type).to include('application/xml')

          xml_doc = Nokogiri::XML(response.body)
          expect(xml_doc.xpath('//fourmdg')).to be_present
          expect(xml_doc.xpath('//business')).to be_present
        end
      end

      response '404', 'Sub-business não encontrado' do
        let(:sub_business_id) { 'invalid_id' }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end
  end
end
