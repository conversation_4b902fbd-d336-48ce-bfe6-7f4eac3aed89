# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V1 Businesses API', type: :request, swagger_doc: 'v1/swagger.yaml' do
  let(:Authorization) { 'Basic test_api_key' }

  before do
    Apartment::Tenant.switch! 'test'
    Company.current.update!(api_key: 'test_api_key')
  end

  path '/external/businesses' do
    post 'Cria ou atualiza conteúdos via XML' do
      tags 'Businesses'
      description 'Cria ou atualiza conteúdos através de XML estruturado'
      operationId 'createBusinessContents'
      consumes 'application/xml'
      produces 'application/xml'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :body, in: :body, schema: { '$ref' => '#/components/schemas/BusinessXmlRequestV1' }, required: true

      response '422', 'Dados inválidos no XML' do

        let(:body) do
          <<~XML
            <?xml version="1.0" encoding="UTF-8"?>
            <fourmdg>
              <business guid="business_id" name="Business Name" group_guid="group_id">
                <contents>
                  <content guid="content_id" updated_at="2024-01-15T10:30:00Z" created_by_id="user_id" created_by_name="User Name" current_step_id="step_id" current_step_name="Step Name">
                    <step label="Step Name" user="user_id" current_user="current_user_id" guid="step_id">
                      <template guid="template_id">
                        <field guid="field_id" label="Field Label" value="Field Value"/>
                      </template>
                    </step>
                  </content>
                </contents>
              </business>
            </fourmdg>
          XML
        end

        run_test! do |response|
          expect(response.status).to eq(422)
        end
      end

      response '201', 'Conteúdos criados com sucesso' do
      end
    end
  end

  path '/external/businesses/{id}' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do conteúdo'

    put 'Atualiza conteúdos via XML' do
      tags 'Businesses'
      description 'Atualiza conteúdos existentes através de XML estruturado'
      operationId 'updateBusinessContents'
      consumes 'application/xml'
      produces 'application/xml'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :body, in: :body, schema: { '$ref' => '#/components/schemas/BusinessXmlRequestV1' }, required: true

      response '422', 'Dados inválidos no XML' do
        let(:id) { 'content_id' }
        let(:body) do
          <<~XML
            <?xml version="1.0" encoding="UTF-8"?>
            <fourmdg>
              <business guid="business_id" name="Business Name" group_guid="group_id">
                <contents>
                  <content guid="content_id" updated_at="2024-01-15T10:30:00Z" created_by_id="user_id" created_by_name="User Name" current_step_id="step_id" current_step_name="Step Name">
                    <step label="Step Name" user="user_id" current_user="current_user_id" guid="step_id">
                      <template guid="template_id">
                        <field guid="field_id" label="Field Label" value="Field Value"/>
                      </template>
                    </step>
                  </content>
                </contents>
              </business>
            </fourmdg>
          XML
        end

        run_test! do |response|
          expect([200, 422, 404]).to include(response.status)
        end
      end

      response '404', 'Conteúdo não encontrado' do
        let(:id) { 'invalid_id' }
      end
    end

    delete 'Remove um conteúdo' do
      tags 'Businesses'
      description 'Remove um conteúdo específico do sistema'
      operationId 'deleteBusinessContent'
      produces 'application/xml'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :user_who_deleted_id, in: :query, type: :string, format: :uuid, required: false, description: 'ID do usuário que está deletando'
      parameter name: :deletion_reason, in: :query, type: :string, required: false, description: 'Motivo da exclusão'

      response '404', 'Conteúdo não encontrado' do
        let(:id) { 'content_id' }

        run_test! do |response|
          expect([200, 404, 400]).to include(response.status)
        end
      end

      response '404', 'Conteúdo não encontrado' do
        let(:id) { 'invalid_id' }
      end

      response '400', 'Erro ao remover conteúdo' do
        let(:id) { 'content_id' }
      end
    end
  end
end
