# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V1 Administrators API', type: :request, swagger_doc: 'v1/swagger.yaml' do
  let(:test_administrator) { create(:administrator) }
  let(:Authorization) { 'Basic test_api_key' }

  before do
    Apartment::Tenant.switch! 'test'
    Company.current.update!(api_key: 'test_api_key')
  end

  path '/external/administrators.json' do
    post 'Cria um novo administrador' do
      tags 'Administrators'
      description 'Cria um novo administrador no sistema'
      operationId 'createAdministrator'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :administrator, in: :body, schema: { '$ref' => '#/components/schemas/AdministratorV1' }

      response '201', 'Administrador criado com sucesso' do
        let(:administrator) do
          {
            name: "<PERSON>",
            email: "<EMAIL>",
            password: "T%YÜ&I*&8abc<PERSON><PERSON><PERSON><PERSON><PERSON>",
            password_confirmation: "T%YÜ&I*&8abcdefghijk",
            approved: true
          }
        end

        run_test! do |response|
          expect(response.status).to eq(201)
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponseV1'

        context 'quando dados são inválidos' do
          let(:administrator) do
            {
              name: "",
              email: "invalid_email",
              password: "123",
              password_confirmation: "456"
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
            expect(data['errors']).to be_an(Array)
          end
        end

        context 'quando email já existe' do
          let(:administrator) do
            {
              name: "Test Admin",
              email: test_administrator.email,
              password: "T%YÜ&I*&8abcdefghijk",
              password_confirmation: "T%YÜ&I*&8abcdefghijk",
              approved: true
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
            expect(data['errors']).to include("Email já está em uso")
          end
        end
      end
    end
  end

  path '/external/administrators/{id}.json' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do administrador'

    delete 'Remove um administrador' do
      tags 'Administrators'
      description 'Remove um administrador do sistema (soft delete)'
      operationId 'deleteAdministrator'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'

      response '204', 'Administrador removido com sucesso' do
        let(:id) { test_administrator.id }

        run_test! do |response|
          expect(response.status).to eq(204)
        end
      end

      response '404', 'Administrador não encontrado' do
        schema '$ref' => '#/components/schemas/NotFoundResponseV1'

        let(:id) { 'invalid_id' }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('status')
          expect(data['status']).to eq(404)
        end
      end
    end
  end
end
