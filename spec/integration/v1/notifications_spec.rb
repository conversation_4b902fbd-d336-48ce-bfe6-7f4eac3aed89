# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V1 Notifications API', type: :request, swagger_doc: 'v1/swagger.yaml' do
  let(:test_user) { create(:user) }
  let(:test_notification) { create(:notification, user: test_user) }
  let(:department) { create(:department) }
  let(:Authorization) { 'Basic test_api_key' }

  before do
    Apartment::Tenant.switch! 'test'
    Company.current.update!(api_key: 'test_api_key')
  end

  path '/external/notifications.json' do
    post 'Cria uma nova notificação' do
      tags 'Notifications'
      description 'Cria uma nova notificação no sistema'
      operationId 'createNotification'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :notification, in: :body, schema: { '$ref' => '#/components/schemas/NotificationV1' }

      response '201', 'Notificação criada com sucesso' do
        schema '$ref' => '#/components/schemas/NotificationCreateResponseV1'
        
        let(:notification) do
          {
            title: "Nova notificação",
            message: "Esta é uma mensagem de teste",
            user_id: test_user.id,
            destiny_users: [test_user.id],
            destiny_departments: [department.id]
          }
        end

        run_test! do |response|
          expect(response.status).to eq(201)
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponseV1'
        
        context 'quando dados são inválidos' do
          let(:notification) do
            {
              title: "",
              message: "",
              user_id: test_user.id
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
            expect(data['errors']).to be_an(Array)
          end
        end
      end
    end
  end

  path '/external/notifications/{id}.json' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID da notificação'

    get 'Busca uma notificação' do
      tags 'Notifications'
      description 'Busca uma notificação específica'
      operationId 'getNotification'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'

      response '200', 'Notificação encontrada' do
        schema '$ref' => '#/components/schemas/NotificationResponseV1'
        
        let(:id) { test_notification.id }

        run_test! do |response|
          expect(response.status).to eq(200)
        end
      end

      response '404', 'Notificação não encontrada' do
        schema '$ref' => '#/components/schemas/NotFoundResponseV1'
        
        let(:id) { 'invalid_id' }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('status')
          expect(data['status']).to eq(404)
        end
      end
    end

    put 'Atualiza uma notificação' do
      tags 'Notifications'
      description 'Atualiza uma notificação existente'
      operationId 'updateNotification'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :notification, in: :body, schema: { '$ref' => '#/components/schemas/NotificationUpdateV1' }

      response '200', 'Notificação atualizada com sucesso' do
        let(:id) { test_notification.id }
        let(:notification) do
          {
            title: "Título atualizado",
            message: "Mensagem atualizada",
            destiny_users: [test_user.id],
            destiny_departments: [department.id]
          }
        end

        run_test! do |response|
          expect(response.status).to eq(200)
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponseV1'
        
        let(:id) { test_notification.id }
        let(:notification) do
          {
            title: "",
            message: ""
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('errors')
          expect(data['errors']).to be_an(Array)
        end
      end
    end

    delete 'Remove uma notificação' do
      tags 'Notifications'
      description 'Remove uma notificação do sistema (soft delete)'
      operationId 'deleteNotification'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'

      response '204', 'Notificação removida com sucesso' do
        let(:id) { test_notification.id }

        run_test! do |response|
          expect(response.status).to eq(204)
        end
      end
    end
  end
end
