# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V1 Answers API', type: :request, swagger_doc: 'v1/swagger.yaml' do
  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group: business_group) }
  let(:content) { create(:content, business: business) }
  let(:step) { create(:step, business: business, order: 0) }
  let(:user) { create(:user) }
  let(:answer) { create(:answer, :waiting_authorization, content: content, step: step) }
  let(:Authorization) { 'Basic test_api_key' }

  before do
    Apartment::Tenant.switch! 'test'
    Company.current.update!(api_key: 'test_api_key')
    answer.step.step_permissions.create!(user_id: user.id, scope: :approvement)
  end

  path '/external/contents/{content_id}/steps/{id}/authorize' do
    parameter name: :content_id, in: :path, type: :string, format: :uuid, description: 'ID do conteúdo'
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do step'

    patch 'Autoriza uma resposta' do
      tags 'Answers'
      description 'Autoriza uma resposta específica de um step'
      operationId 'authorizeAnswer'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :answer_action, in: :body, schema: { '$ref' => '#/components/schemas/AnswerActionV1' }

      response '200', 'Resposta autorizada com sucesso' do
        schema '$ref' => '#/components/schemas/AnswerSuccessResponseV1'

        let(:content_id) { content.id }
        let(:id) { step.id }
        let(:answer_action) do
          {
            user_id: user.id
          }
        end

        run_test! do |response|
          expect(response.status).to eq(200)
          data = JSON.parse(response.body)
          expect(data).to have_key('success')
          expect(data['success']).to be true
          expect(data).to have_key('guids')
          expect(data['guids']).to be_an(Array)
        end
      end

      response '422', 'Erro ao autorizar resposta' do
        schema '$ref' => '#/components/schemas/AnswerErrorResponseV1'

        context 'quando parâmetros são inválidos' do
          let(:content_id) { 'invalid_id' }
          let(:id) { step.id }
          let(:answer_action) do
            {
              user_id: user.id
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
            expect(data['errors']).to be_an(Array)
          end
        end
      end
    end
  end

  path '/external/contents/{content_id}/steps/{id}/reject' do
    parameter name: :content_id, in: :path, type: :string, format: :uuid, description: 'ID do conteúdo'
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do step'

    patch 'Rejeita uma resposta' do
      tags 'Answers'
      description 'Rejeita uma resposta específica de um step'
      operationId 'rejectAnswer'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'
      parameter name: :answer_action, in: :body, schema: { '$ref' => '#/components/schemas/AnswerActionV1' }

      response '200', 'Resposta rejeitada com sucesso' do
        schema '$ref' => '#/components/schemas/AnswerSuccessResponseV1'

        let(:content_id) { content.id }
        let(:id) { step.id }
        let(:answer_action) do
          {
            user_id: user.id
          }
        end

        run_test! do |response|
          expect(response.status).to eq(200)
          data = JSON.parse(response.body)
          expect(data).to have_key('success')
          expect(data['success']).to be true
          expect(data).to have_key('guids')
          expect(data['guids']).to be_an(Array)
        end
      end

      response '422', 'Erro ao rejeitar resposta' do
        schema '$ref' => '#/components/schemas/AnswerErrorResponseV1'

        context 'quando parâmetros são inválidos' do
          let(:content_id) { 'invalid_id' }
          let(:id) { step.id }
          let(:answer_action) do
            {
              user_id: user.id
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
            expect(data['errors']).to be_an(Array)
          end
        end
      end
    end
  end

  path '/external/contents/{content_id}/steps/{id}/changing' do
    parameter name: :content_id, in: :path, type: :string, format: :uuid, description: 'ID do conteúdo'
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do step'

    patch 'Marca resposta como em alteração' do
      tags 'Answers'
      description 'Marca uma resposta como em processo de alteração'
      operationId 'changingAnswer'
      consumes 'application/json'
      produces 'application/json'
      security [{ basic_auth: [] }]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Basic authentication with API key'

      response '200', 'Resposta marcada como em alteração' do
        schema '$ref' => '#/components/schemas/AnswerSuccessResponseV1'

        let(:changing_step) { create(:step, business: business, order: 1) }
        let(:content_id) { content.id }
        let(:id) { changing_step.id }

        let!(:completed_answer) { create(:answer, :completed, content: content, step: changing_step) }

        run_test! do |response|
          expect(response.status).to eq(200)
          data = JSON.parse(response.body)
          expect(data).to have_key('success')
          expect(data['success']).to be true
          expect(data).to have_key('guids')
          expect(data['guids']).to be_an(Array)
        end
      end

      response '422', 'Erro ao marcar resposta como em alteração' do
        schema '$ref' => '#/components/schemas/AnswerErrorResponseV1'

        context 'quando parâmetros são inválidos' do
          let(:content_id) { 'invalid_id' }
          let(:id) { step.id }

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
            expect(data['errors']).to be_an(Array)
          end
        end
      end
    end
  end
end
