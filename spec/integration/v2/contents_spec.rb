# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V2 Contents API', type: :request, swagger_doc: 'v2/swagger.yaml' do
  let(:auth_user) { create(:user, authorization_token: SecureRandom.hex(32)) }
  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group: business_group) }
  let(:step) { create(:step, business: business) }
  let(:template) { create(:template) }
  let(:step_template) { create(:step_template, step: step, template: template) }
  let(:field) { create(:field, template: template) }
  let(:step_permission) { create(:step_permission, step: step, user: auth_user, scope: :edit) }
  let(:test_content) { create(:content, business: business) }

  let(:user_token) { auth_user.authorization_token }
  let(:user_email) { auth_user.email }

  before do
    Apartment::Tenant.switch! 'test'
  end

  path '/external/v2/businesses/{business_id}/contents/show' do
    parameter name: :business_id, in: :path, type: :string, format: :uuid, description: 'ID do negócio'

    post 'Lista conteúdos' do
      tags 'Contents'
      description 'Retorna uma lista paginada de conteúdos de um negócio'
      operationId 'listContents'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'USER_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do usuário'
      parameter name: 'USER_EMAIL', in: :header, type: :string, required: true, description: 'Email do usuário'
      parameter name: :content_params, in: :body, schema: {
        type: :object,
        properties: {
          offset: { type: :integer, description: 'Número de registros para pular (padrão: 0)' },
          limit: { type: :integer, description: 'Número máximo de registros a retornar (padrão: 10, máximo: 500)' },
          parent_id: { type: :string, format: :uuid, description: 'ID do conteúdo pai (para subnegócios)' },
          query: {
            type: :object,
            description: 'Parâmetros de consulta para filtrar conteúdos',
            properties: {
              conditions: {
                type: :array,
                items: {
                  type: :object,
                  properties: {
                    field: { type: :string, description: 'ID do campo' },
                    operator: { type: :string, enum: ['=', '!=', 'LIKE', 'NOT LIKE', 'IN', 'BETWEEN'], description: 'Operador de comparação' },
                    value: { type: :string, description: 'Valor para comparação' }
                  }
                }
              }
            }
          }
        }
      }

      response '200', 'Lista de conteúdos retornada com sucesso' do
        schema type: :object,
               properties: {
                 contents: {
                   type: :array,
                   items: { '$ref' => '#/components/schemas/Content' }
                 },
                 total_contents: { type: :integer, description: 'Total de conteúdos no sistema' },
                 has_more_contents: { type: :boolean, description: 'Se há mais conteúdos para carregar' }
               }

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:content_params) do
          {
            offset: 0,
            limit: 10
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('contents')
          expect(data).to have_key('total_contents')
          expect(data).to have_key('has_more_contents')
        end
      end

      response '422', 'Limite excedido' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:content_params) do
          {
            limit: 501
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.contents.errors.max_limit"))
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('USER_TOKEN') { 'invalid_token' }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:content_params) { {} }

        run_test!
      end
    end
  end

  path '/external/v2/businesses/{business_id}/contents' do
    parameter name: :business_id, in: :path, type: :string, format: :uuid, description: 'ID do negócio'

    post 'Cria um novo conteúdo' do
      tags 'Contents'
      description 'Cria um novo conteúdo no sistema'
      operationId 'createContent'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'USER_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do usuário'
      parameter name: 'USER_EMAIL', in: :header, type: :string, required: true, description: 'Email do usuário'
      parameter name: :content, in: :body, schema: {
        type: :object,
        properties: {
          steps: {
            type: :array,
            items: {
              type: :object,
              properties: {
                skip_mandatory_fields: { type: :boolean, description: 'Pular campos obrigatórios' },
                skip_business_rules: { type: :boolean, description: 'Pular regras de negócio' },
                skip_external_input: { type: :boolean, description: 'Pular entrada externa' },
                skip_external_validation: { type: :boolean, description: 'Pular validação externa' },
                skip_webhook: { type: :boolean, description: 'Pular webhook' },
                id: { type: :string, description: 'ID do step' },
                templates: {
                  type: :array,
                  items: {
                    type: :object,
                    properties: {
                      fields: {
                        type: :array,
                        items: {
                          type: :object,
                          properties: {
                            id: { type: :string, description: 'ID do campo' },
                            value: { type: :string, description: 'Valor do campo' }
                          },
                          required: ['id', 'value']
                        }
                      }
                    },
                    required: ['fields']
                  }
                }
              },
              required: ['id', 'templates']
            }
          }
        },
        required: ['steps']
      }

      response '201', 'Conteúdo criado com sucesso' do
        schema '$ref' => '#/components/schemas/ContentResponse'

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:content) do
          step_template
          field
          step_permission
          {
            steps: [
              {
                skip_mandatory_fields: true,
                skip_business_rules: true,
                skip_external_input: true,
                skip_external_validation: true,
                skip_webhook: true,
                id: step.id,
                templates: [
                  {
                    fields: [
                      {
                        id: field.id,
                        value: "test_value"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }

        context 'quando steps não é fornecido' do
          let(:content) { {} }

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
          end
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('USER_TOKEN') { 'invalid_token' }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:content) do
          {
            steps: [
              {
                id: "string",
                templates: [
                  {
                    fields: [
                      {
                        id: "string",
                        value: "string"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        end

        run_test!
      end
    end

    put 'Atualiza/Cria conteúdo (upsert)' do
      tags 'Contents'
      description 'Atualiza um conteúdo existente ou cria um novo baseado em campos chave'
      operationId 'upsertContent'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'USER_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do usuário'
      parameter name: 'USER_EMAIL', in: :header, type: :string, required: true, description: 'Email do usuário'
      parameter name: :content, in: :body, schema: {
        type: :object,
        properties: {
          key_fields: {
            type: :array,
            items: {
              type: :object,
              properties: {
                id: { type: :string, description: 'ID do campo chave' },
                value: { type: :string, description: 'Valor do campo chave' }
              },
              required: ['id', 'value']
            },
            description: 'Campos chave para identificar o conteúdo'
          },
          parent_id: { type: :string, format: :uuid, description: 'ID do conteúdo pai (obrigatório para subnegócios)' },
          steps: {
            type: :array,
            items: {
              type: :object,
              properties: {
                skip_mandatory_fields: { type: :boolean, description: 'Pular campos obrigatórios' },
                skip_business_rules: { type: :boolean, description: 'Pular regras de negócio' },
                skip_external_input: { type: :boolean, description: 'Pular entrada externa' },
                skip_external_validation: { type: :boolean, description: 'Pular validação externa' },
                skip_webhook: { type: :boolean, description: 'Pular webhook' },
                id: { type: :string, description: 'ID do step' },
                templates: {
                  type: :array,
                  items: {
                    type: :object,
                    properties: {
                      fields: {
                        type: :array,
                        items: {
                          type: :object,
                          properties: {
                            id: { type: :string, description: 'ID do campo' },
                            value: { type: :string, description: 'Valor do campo' }
                          },
                          required: ['id', 'value']
                        }
                      }
                    },
                    required: ['fields']
                  }
                }
              },
              required: ['id', 'templates']
            }
          }
        },
        required: ['key_fields', 'steps']
      }

      response '200', 'Conteúdo atualizado/criado com sucesso' do
        schema type: :object,
               properties: {
                 id: { type: :string, format: :uuid, description: 'ID do conteúdo' }
               }

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:content) do
          step_template
          field
          step_permission
          {
            key_fields: [
              {
                id: field.id,
                value: "field_value"
              }
            ],
            steps: [
              {
                id: step.id,
                templates: [
                  {
                    fields: [
                      {
                        id: field.id,
                        value: "test_value"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }

        context 'quando key_fields não é fornecido' do
          let(:content) do
            {
              steps: [
                {
                  id: "string",
                  templates: [
                    {
                      fields: [
                        {
                          id: "string",
                          value: "string"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data['errors']).to eq(I18n.t("api.v2.contents.errors.incorrect_parameters"))
          end
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('USER_TOKEN') { 'invalid_token' }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:content) do
          {
            key_fields: [
              {
                id: "field_id",
                value: "field_value"
              }
            ],
            steps: [
              {
                id: "string",
                templates: [
                  {
                    fields: [
                      {
                        id: "string",
                        value: "string"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        end

        run_test!
      end
    end
  end

  path '/external/v2/businesses/{business_id}/contents/{id}' do
    parameter name: :business_id, in: :path, type: :string, format: :uuid, description: 'ID do negócio'
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do conteúdo'

    get 'Busca um conteúdo específico' do
      tags 'Contents'
      description 'Retorna os dados de um conteúdo específico'
      operationId 'showContent'
      produces 'application/json'

      parameter name: 'USER_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do usuário'
      parameter name: 'USER_EMAIL', in: :header, type: :string, required: true, description: 'Email do usuário'

      response '200', 'Conteúdo encontrado' do
        schema '$ref' => '#/components/schemas/Content'

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:id) { test_content.id }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('content_id')
          expect(data['content_id']).to eq(test_content.id)
        end
      end

      response '404', 'Conteúdo não encontrado' do
        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:id) { SecureRandom.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.contents.errors.content_not_found"))
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('USER_TOKEN') { 'invalid_token' }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:id) { test_content.id }

        run_test!
      end
    end

    put 'Atualiza um conteúdo' do
      tags 'Contents'
      description 'Atualiza um conteúdo existente no sistema'
      operationId 'updateContent'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'USER_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do usuário'
      parameter name: 'USER_EMAIL', in: :header, type: :string, required: true, description: 'Email do usuário'
      parameter name: :content, in: :body, schema: {
        type: :object,
        properties: {
          steps: {
            type: :array,
            items: {
              type: :object,
              properties: {
                skip_mandatory_fields: { type: :boolean, description: 'Pular campos obrigatórios' },
                skip_business_rules: { type: :boolean, description: 'Pular regras de negócio' },
                skip_external_input: { type: :boolean, description: 'Pular entrada externa' },
                skip_external_validation: { type: :boolean, description: 'Pular validação externa' },
                skip_webhook: { type: :boolean, description: 'Pular webhook' },
                id: { type: :string, description: 'ID do step' },
                templates: {
                  type: :array,
                  items: {
                    type: :object,
                    properties: {
                      fields: {
                        type: :array,
                        items: {
                          type: :object,
                          properties: {
                            id: { type: :string, description: 'ID do campo' },
                            value: { type: :string, description: 'Valor do campo' }
                          },
                          required: ['id', 'value']
                        }
                      }
                    },
                    required: ['fields']
                  }
                }
              },
              required: ['id', 'templates']
            }
          }
        },
        required: ['steps']
      }

      response '200', 'Conteúdo atualizado com sucesso' do
        schema '$ref' => '#/components/schemas/ContentResponse'

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:id) { test_content.id }
        let(:content) do
          step_template
          field
          step_permission
          {
            steps: [
              {
                id: step.id,
                templates: [
                  {
                    fields: [
                      {
                        id: field.id,
                        value: "updated_value"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('USER_TOKEN') { user_token }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:id) { test_content.id }

        context 'quando steps não é fornecido' do
          let(:content) { {} }

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data['errors']).to eq(I18n.t("api.v2.contents.errors.incorrect_parameters"))
          end
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('USER_TOKEN') { 'invalid_token' }
        let('USER_EMAIL') { user_email }
        let(:business_id) { business.id }
        let(:id) { test_content.id }
        let(:content) do
          {
            steps: [
              {
                id: "string",
                templates: [
                  {
                    fields: [
                      {
                        id: "string",
                        value: "string"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        end

        run_test!
      end
    end
  end
end
