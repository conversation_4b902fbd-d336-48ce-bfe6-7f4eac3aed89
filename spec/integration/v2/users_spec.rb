# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V2 Users API', type: :request, swagger_doc: 'v2/swagger.yaml' do
  let(:administrator) { create(:administrator, authorization_token: SecureRandom.hex(32)) }
  let(:user) { create(:user) }

  let(:admin_token) { administrator.authorization_token }
  let(:admin_email) { administrator.email }

  before do
    Apartment::Tenant.switch! 'test'
  end

  path '/external/v2/users' do
    get 'Lista usuários' do
      tags 'Users'
      description 'Retorna uma lista paginada de usuários'
      operationId 'listUsers'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :offset, in: :query, type: :integer, required: false, description: 'Número de registros para pular (padrão: 0)'
      parameter name: :limit, in: :query, type: :integer, required: false, description: 'Número máximo de registros a retornar (padrão: 10, máximo: 500)'

      response '200', 'Lista de usuários retornada com sucesso' do
        schema type: :object,
               properties: {
                 users: {
                   type: :array,
                   items: { '$ref' => '#/components/schemas/User' }
                 },
                 total_users: { type: :integer, description: 'Total de usuários no sistema' },
                 has_more_users: { type: :boolean, description: 'Se há mais usuários para carregar' }
               }

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:offset) { 0 }
        let(:limit) { 10 }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('users')
          expect(data).to have_key('total_users')
          expect(data).to have_key('has_more_users')
        end
      end

      response '422', 'Limite excedido' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:limit) { 501 }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.users.errors.max_limit"))
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }

        run_test!
      end
    end

    post 'Cria um novo usuário' do
      tags 'Users'
      description 'Cria um novo usuário no sistema'
      operationId 'createUser'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :user, in: :body, schema: {
        type: :object,
        properties: {
          provider: {
            type: :string,
            enum: ['email', 'google_oauth2', 'entra_id', 'openid_connect'],
            description: 'Provedor de autenticação'
          },
          email: { type: :string, format: :email, description: 'Email do usuário' },
          name: { type: :string, description: 'Nome completo do usuário' },
          password: { type: :string, description: 'Senha do usuário (obrigatório para provider email)' },
          password_confirmation: { type: :string, description: 'Confirmação da senha (obrigatório para provider email)' },
          limited: { type: :boolean, description: 'Se o usuário tem acesso limitado', default: false },
          coordinator: { type: :boolean, description: 'Se o usuário é coordenador', default: false },
          notification: { type: :boolean, description: 'Se o usuário recebe notificações', default: true },
          approved: { type: :boolean, description: 'Se o usuário está aprovado', default: true },
          confirmed_at: { type: :string, format: 'date-time', description: 'Data de confirmação do usuário' },
          chat_enabled: { type: :boolean, description: 'Se o chat está habilitado para o usuário', default: true },
          block_menus: {
            type: :array,
            items: { type: :string },
            description: 'Lista de menus bloqueados para o usuário',
            default: []
          },
          department_ids: {
            type: :array,
            items: { type: :string, format: :uuid },
            description: 'IDs dos departamentos do usuário',
            default: []
          }
        },
        required: ['provider', 'email', 'name']
      }

      response '201', 'Usuário criado com sucesso' do
        schema '$ref' => '#/components/schemas/UserResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:user) do
          {
            provider: 'email',
            email: '<EMAIL>',
            name: 'Thun Thun Sahur',
            password: 'Teste123@@1234',
            password_confirmation: 'Teste123@@1234',
            limited: false,
            coordinator: true,
            notification: true,
            approved: true,
            confirmed_at: '2025-05-20T19:46:29.880Z',
            chat_enabled: true,
            block_menus: [],
            department_ids: []
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }

        context 'quando provider é email e senha está em branco' do
          let(:user) do
            {
              provider: 'email',
              email: '<EMAIL>',
              name: 'Test User',
              password: '',
              password_confirmation: 'password123'
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data['errors']).to include(I18n.t("api.v2.users.errors.password_required"))
          end
        end

        context 'quando provider é email e confirmação de senha está em branco' do
          let(:user) do
            {
              provider: 'email',
              email: '<EMAIL>',
              name: 'Test User',
              password: 'password123',
              password_confirmation: ''
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data['errors']).to include(I18n.t("api.v2.users.errors.confirmation_password_required"))
          end
        end

        context 'quando provider é inválido' do
          let(:user) do
            {
              provider: 'invalid_provider',
              email: '<EMAIL>',
              name: 'Test User'
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data['errors']).to eq(I18n.t("api.v2.users.errors.invalid_provider"))
          end
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:user) do
          {
            provider: 'email',
            email: '<EMAIL>',
            name: 'Test User',
            password: 'password123',
            password_confirmation: 'password123'
          }
        end

        run_test!
      end
    end
  end

  path '/external/v2/users' do
    put 'Atualiza um usuário' do
      tags 'Users'
      description 'Atualiza um usuário existente no sistema. Pode buscar por email ou id.'
      operationId 'updateUser'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :user, in: :body, schema: {
        type: :object,
        properties: {
          id: { type: :string, format: :uuid, description: 'ID do usuário (alternativo ao email)' },
          email: { type: :string, format: :email, description: 'Email do usuário (alternativo ao id)' },
          provider: {
            type: :string,
            enum: ['email', 'google_oauth2', 'entra_id', 'openid_connect'],
            description: 'Provedor de autenticação'
          },
          name: { type: :string, description: 'Nome completo do usuário' },
          password: { type: :string, description: 'Nova senha do usuário' },
          password_confirmation: { type: :string, description: 'Confirmação da nova senha' },
          limited: { type: :boolean, description: 'Se o usuário tem acesso limitado' },
          coordinator: { type: :boolean, description: 'Se o usuário é coordenador' },
          notification: { type: :boolean, description: 'Se o usuário recebe notificações' },
          approved: { type: :boolean, description: 'Se o usuário está aprovado' },
          confirmed_at: { type: :string, format: 'date-time', description: 'Data de confirmação do usuário' },
          chat_enabled: { type: :boolean, description: 'Se o chat está habilitado para o usuário' },
          block_menus: {
            type: :array,
            items: { type: :string },
            description: 'Lista de menus bloqueados para o usuário'
          },
          department_ids: {
            type: :array,
            items: { type: :string, format: :uuid },
            description: 'IDs dos departamentos do usuário'
          }
        }
      }

      response '200', 'Usuário atualizado com sucesso' do
        schema '$ref' => '#/components/schemas/UserResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:user) do
          {
            email: create(:user).email,
            name: 'Nome Atualizado',
            coordinator: true,
            notification: false
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '404', 'Usuário não encontrado' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:user) do
          {
            email: '<EMAIL>',
            name: 'Nome Atualizado'
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.users.errors.user_not_found"))
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }

        context 'quando provider é inválido' do
          let(:user) do
            {
              email: create(:user).email,
              provider: 'invalid_provider',
              name: 'Nome Atualizado'
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data['errors']).to eq(I18n.t("api.v2.users.errors.invalid_provider"))
          end
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:user) do
          {
            email: create(:user).email,
            name: 'Nome Atualizado'
          }
        end

        run_test!
      end
    end
  end

  path '/external/v2/users/{id}' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do usuário'

    get 'Busca um usuário específico' do
      tags 'Users'
      description 'Retorna os dados de um usuário específico'
      operationId 'showUser'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'

      response '200', 'Usuário encontrado' do
        schema '$ref' => '#/components/schemas/User'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { user.id }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to eq(user.id)
        end
      end

      response '404', 'Usuário não encontrado' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { SecureRandom.uuid }

        run_test!
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { user.id }

        run_test!
      end
    end

    delete 'Remove um usuário' do
      tags 'Users'
      description 'Remove um usuário do sistema'
      operationId 'deleteUser'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'

      response '200', 'Usuário removido com sucesso' do
        schema '$ref' => '#/components/schemas/UserResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { user.id }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to eq(user.id)
        end
      end

      response '404', 'Usuário não encontrado' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { SecureRandom.uuid }

        run_test!
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { user.id }

        run_test!
      end
    end
  end
end
