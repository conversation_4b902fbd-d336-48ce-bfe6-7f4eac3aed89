# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V2 Departments API', type: :request, swagger_doc: 'v2/swagger.yaml' do
  let(:auth_administrator) { create(:administrator, authorization_token: SecureRandom.hex(32)) }
  let(:test_department) { create(:department) }

  let(:admin_token) { auth_administrator.authorization_token }
  let(:admin_email) { auth_administrator.email }

  before do
    Apartment::Tenant.switch! 'test'
  end

  path '/external/v2/departments' do
    get 'Lista departamentos' do
      tags 'Departments'
      description 'Retorna uma lista paginada de departamentos'
      operationId 'listDepartments'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :offset, in: :query, type: :integer, required: false, description: 'Número de registros para pular (padrão: 0)'
      parameter name: :limit, in: :query, type: :integer, required: false, description: 'Número máximo de registros a retornar (padrão: 10, máximo: 500)'

      response '200', 'Lista de departamentos retornada com sucesso' do
        schema type: :object,
               properties: {
                 departments: {
                   type: :array,
                   items: { '$ref' => '#/components/schemas/Department' }
                 },
                 total_departments: { type: :integer, description: 'Total de departamentos no sistema' },
                 has_more_departments: { type: :boolean, description: 'Se há mais departamentos para carregar' }
               }

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:offset) { 0 }
        let(:limit) { 10 }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('departments')
          expect(data).to have_key('total_departments')
          expect(data).to have_key('has_more_departments')
        end
      end

      response '422', 'Limite excedido' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:limit) { 501 }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.departments.errors.max_limit"))
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }

        run_test!
      end
    end

    post 'Cria um novo departamento' do
      tags 'Departments'
      description 'Cria um novo departamento no sistema'
      operationId 'createDepartment'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :department, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Nome do departamento' },
          limited: { type: :boolean, description: 'Se o departamento tem acesso limitado', default: false }
        },
        required: ['name']
      }

      response '201', 'Departamento criado com sucesso' do
        schema '$ref' => '#/components/schemas/DepartmentResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:department) do
          {
            name: 'LALELI',
            limited: false
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }

        context 'quando nome está em branco' do
          let(:department) do
            {
              name: '',
              limited: false
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
          end
        end

        context 'quando nome não é fornecido' do
          let(:department) do
            {
              limited: false
            }
          end

          run_test!
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:department) do
          {
            name: 'Test Department',
            limited: false
          }
        end

        run_test!
      end
    end
  end

  path '/external/v2/departments/{id}' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do departamento'

    get 'Busca um departamento específico' do
      tags 'Departments'
      description 'Retorna os dados de um departamento específico'
      operationId 'showDepartment'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'

      response '200', 'Departamento encontrado' do
        schema '$ref' => '#/components/schemas/Department'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_department.id }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to eq(test_department.id)
        end
      end

      response '404', 'Departamento não encontrado' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { SecureRandom.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.departments.errors.department_not_found"))
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_department.id }

        run_test!
      end
    end

    put 'Atualiza um departamento' do
      tags 'Departments'
      description 'Atualiza um departamento existente no sistema'
      operationId 'updateDepartment'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :department, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Nome do departamento' },
          limited: { type: :boolean, description: 'Se o departamento tem acesso limitado' }
        }
      }

      response '200', 'Departamento atualizado com sucesso' do
        schema '$ref' => '#/components/schemas/DepartmentResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_department.id }
        let(:department) do
          {
            name: 'Nome Atualizado',
            limited: true
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '404', 'Departamento não encontrado' do
        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }

        context 'quando departamento não é encontrado' do
          let(:id) { SecureRandom.uuid }
          let(:department) do
            {
              name: 'Nome Atualizado'
            }
          end

          run_test! do |response|
            expect(response.status).to eq(404)
          end
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_department.id }
        let(:department) do
          {
            name: 'Nome Atualizado'
          }
        end

        run_test!
      end
    end
  end
end
