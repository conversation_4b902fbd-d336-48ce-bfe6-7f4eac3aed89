# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'External V2 Administrators API', type: :request, swagger_doc: 'v2/swagger.yaml' do
  let(:auth_administrator) { create(:administrator, authorization_token: SecureRandom.hex(32)) }
  let(:test_administrator) { create(:administrator) }

  let(:admin_token) { auth_administrator.authorization_token }
  let(:admin_email) { auth_administrator.email }

  before do
    Apartment::Tenant.switch! 'test'
  end

  path '/external/v2/administrators' do
    get 'Lista administradores' do
      tags 'Administrators'
      description 'Retorna uma lista paginada de administradores'
      operationId 'listAdministrators'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :offset, in: :query, type: :integer, required: false, description: 'Número de registros para pular (padrão: 0)'
      parameter name: :limit, in: :query, type: :integer, required: false, description: 'Número máximo de registros a retornar (padrão: 10, máximo: 500)'

      response '200', 'Lista de administradores retornada com sucesso' do
        schema type: :object,
               properties: {
                 administrators: {
                   type: :array,
                   items: { '$ref' => '#/components/schemas/Administrator' }
                 },
                 total_administrators: { type: :integer, description: 'Total de administradores no sistema' },
                 has_more_administrators: { type: :boolean, description: 'Se há mais administradores para carregar' }
               }

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:offset) { 0 }
        let(:limit) { 10 }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('administrators')
          expect(data).to have_key('total_administrators')
          expect(data).to have_key('has_more_administrators')
        end
      end

      response '422', 'Limite excedido' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:limit) { 501 }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.administrators.errors.max_limit"))
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }

        run_test!
      end
    end

    post 'Cria um novo administrador' do
      tags 'Administrators'
      description 'Cria um novo administrador no sistema'
      operationId 'createAdministrator'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :administrator, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Nome completo do administrador' },
          email: { type: :string, format: :email, description: 'Email do administrador' },
          password: { type: :string, description: 'Senha do administrador' },
          password_confirmation: { type: :string, description: 'Confirmação da senha' },
          approved: { type: :boolean, description: 'Se o administrador está aprovado', default: true },
          owner: { type: :boolean, description: 'Se o administrador é proprietário', default: false }
        },
        required: ['name', 'email', 'password', 'password_confirmation']
      }

      response '201', 'Administrador criado com sucesso' do
        schema '$ref' => '#/components/schemas/AdministratorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:administrator) do
          {
            name: 'Bruno de Almeida',
            email: '<EMAIL>',
            password: 'Teste123@@1234',
            password_confirmation: 'Teste123@@1234',
            approved: true
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }

        context 'quando email já existe' do
          let(:administrator) do
            {
              name: 'Test Admin',
              email: test_administrator.email,
              password: 'password123',
              password_confirmation: 'password123',
              approved: true
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data).to have_key('errors')
          end
        end

        context 'quando senha está em branco' do
          let(:administrator) do
            {
              name: 'Test Admin',
              email: '<EMAIL>',
              password: '',
              password_confirmation: 'password123',
              approved: true
            }
          end

          run_test!
        end

        context 'quando confirmação de senha não confere' do
          let(:administrator) do
            {
              name: 'Test Admin',
              email: '<EMAIL>',
              password: 'password123',
              password_confirmation: 'different_password',
              approved: true
            }
          end

          run_test!
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:administrator) do
          {
            name: 'Test Admin',
            email: '<EMAIL>',
            password: 'password123',
            password_confirmation: 'password123',
            approved: true
          }
        end

        run_test!
      end
    end
  end

  path '/external/v2/administrators/{id}' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do administrador'

    put 'Atualiza um administrador' do
      tags 'Administrators'
      description 'Atualiza um administrador existente no sistema'
      operationId 'updateAdministrator'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'
      parameter name: :administrator, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Nome completo do administrador' },
          email: { type: :string, format: :email, description: 'Email do administrador' },
          password: { type: :string, description: 'Nova senha do administrador' },
          password_confirmation: { type: :string, description: 'Confirmação da nova senha' },
          approved: { type: :boolean, description: 'Se o administrador está aprovado' },
          owner: { type: :boolean, description: 'Se o administrador é proprietário' }
        }
      }

      response '200', 'Administrador atualizado com sucesso' do
        schema '$ref' => '#/components/schemas/AdministratorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_administrator.id }
        let(:administrator) do
          {
            name: 'Nome Atualizado',
            approved: false
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to be_present
        end
      end

      response '422', 'Dados inválidos' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }

        context 'quando administrador não é encontrado' do
          let(:id) { SecureRandom.uuid }
          let(:administrator) do
            {
              name: 'Nome Atualizado'
            }
          end

          run_test!
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_administrator.id }
        let(:administrator) do
          {
            name: 'Nome Atualizado'
          }
        end

        run_test!
      end
    end
  end

  path '/external/v2/administrators/{id}' do
    parameter name: :id, in: :path, type: :string, format: :uuid, description: 'ID do administrador'

    get 'Busca um administrador específico' do
      tags 'Administrators'
      description 'Retorna os dados de um administrador específico'
      operationId 'showAdministrator'
      produces 'application/json'

      parameter name: 'ADMIN_TOKEN', in: :header, type: :string, required: true, description: 'Token de autorização do administrador'
      parameter name: 'ADMIN_EMAIL', in: :header, type: :string, required: true, description: 'Email do administrador'

      response '200', 'Administrador encontrado' do
        schema '$ref' => '#/components/schemas/Administrator'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_administrator.id }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          expect(data['id']).to eq(test_administrator.id)
        end
      end

      response '404', 'Administrador não encontrado' do
        schema '$ref' => '#/components/schemas/ErrorResponse'

        let('ADMIN_TOKEN') { admin_token }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { SecureRandom.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['errors']).to eq(I18n.t("api.v2.administrators.errors.administrator_not_found"))
        end
      end

      response '401', 'Não autorizado' do
        schema '$ref' => '#/components/schemas/UnauthorizedResponse'

        let('ADMIN_TOKEN') { 'invalid_token' }
        let('ADMIN_EMAIL') { admin_email }
        let(:id) { test_administrator.id }

        run_test!
      end
    end
  end
end
