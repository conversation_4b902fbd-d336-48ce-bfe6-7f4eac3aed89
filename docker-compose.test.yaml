services:
  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: .docker/dev/entrypoint_web.sh
    env_file: .env.test
    ports:
      - "3000:3000"
    stdin_open: true
    tty: true
    depends_on:
      - postgres
      - valkey

  postgres:
    image: "postgres:17-alpine"
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=93pbf34K8p7A
    shm_size: 128m
    ports:
      - "5432:5432"

  valkey:
    image: valkey/valkey:8-alpine
    env_file: .env.test
    ports:
      - "6379:6379"

networks:
  default:
    name: fourmdg-net
    driver: bridge
    external: false
