---
openapi: 3.0.1
info:
  title: 4MDG External API V2
  version: v2
  description: API V2 para integração externa com o sistema 4MDG
paths:
  "/external/v2/administrators":
    get:
      summary: Lista administradores
      tags:
      - Administrators
      description: Retorna uma lista paginada de administradores
      operationId: listAdministrators
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      - name: offset
        in: query
        required: false
        description: 'Número de registros para pular (padrão: 0)'
        schema:
          type: integer
      - name: limit
        in: query
        required: false
        description: 'Número máximo de registros a retornar (padrão: 10, máximo: 500)'
        schema:
          type: integer
      responses:
        '200':
          description: Lista de administradores retornada com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  administrators:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Administrator"
                  total_administrators:
                    type: integer
                    description: Total de administradores no sistema
                  has_more_administrators:
                    type: boolean
                    description: Se há mais administradores para carregar
        '422':
          description: Limite excedido
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
    post:
      summary: Cria um novo administrador
      tags:
      - Administrators
      description: Cria um novo administrador no sistema
      operationId: createAdministrator
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '201':
          description: Administrador criado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AdministratorResponse"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Nome completo do administrador
                email:
                  type: string
                  format: email
                  description: Email do administrador
                password:
                  type: string
                  description: Senha do administrador
                password_confirmation:
                  type: string
                  description: Confirmação da senha
                approved:
                  type: boolean
                  description: Se o administrador está aprovado
                  default: true
                owner:
                  type: boolean
                  description: Se o administrador é proprietário
                  default: false
              required:
              - name
              - email
              - password
              - password_confirmation
  "/external/v2/administrators/{id}":
    parameters:
    - name: id
      in: path
      format: uuid
      description: ID do administrador
      required: true
      schema:
        type: string
    put:
      summary: Atualiza um administrador
      tags:
      - Administrators
      description: Atualiza um administrador existente no sistema
      operationId: updateAdministrator
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '200':
          description: Administrador atualizado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AdministratorResponse"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Nome completo do administrador
                email:
                  type: string
                  format: email
                  description: Email do administrador
                password:
                  type: string
                  description: Nova senha do administrador
                password_confirmation:
                  type: string
                  description: Confirmação da nova senha
                approved:
                  type: boolean
                  description: Se o administrador está aprovado
                owner:
                  type: boolean
                  description: Se o administrador é proprietário
    get:
      summary: Busca um administrador específico
      tags:
      - Administrators
      description: Retorna os dados de um administrador específico
      operationId: showAdministrator
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '200':
          description: Administrador encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Administrator"
        '404':
          description: Administrador não encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
  "/external/v2/businesses/{business_id}/contents/show":
    parameters:
    - name: business_id
      in: path
      format: uuid
      description: ID do negócio
      required: true
      schema:
        type: string
    post:
      summary: Lista conteúdos
      tags:
      - Contents
      description: Retorna uma lista paginada de conteúdos de um negócio
      operationId: listContents
      parameters:
      - name: USER_TOKEN
        in: header
        required: true
        description: Token de autorização do usuário
        schema:
          type: string
      - name: USER_EMAIL
        in: header
        required: true
        description: Email do usuário
        schema:
          type: string
      responses:
        '200':
          description: Lista de conteúdos retornada com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Content"
                  total_contents:
                    type: integer
                    description: Total de conteúdos no sistema
                  has_more_contents:
                    type: boolean
                    description: Se há mais conteúdos para carregar
        '422':
          description: Limite excedido
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                offset:
                  type: integer
                  description: 'Número de registros para pular (padrão: 0)'
                limit:
                  type: integer
                  description: 'Número máximo de registros a retornar (padrão: 10,
                    máximo: 500)'
                parent_id:
                  type: string
                  format: uuid
                  description: ID do conteúdo pai (para subnegócios)
                query:
                  type: object
                  description: Parâmetros de consulta para filtrar conteúdos
                  properties:
                    conditions:
                      type: array
                      items:
                        type: object
                        properties:
                          field:
                            type: string
                            description: ID do campo
                          operator:
                            type: string
                            enum:
                            - "="
                            - "!="
                            - LIKE
                            - NOT LIKE
                            - IN
                            - BETWEEN
                            description: Operador de comparação
                          value:
                            type: string
                            description: Valor para comparação
  "/external/v2/businesses/{business_id}/contents":
    parameters:
    - name: business_id
      in: path
      format: uuid
      description: ID do negócio
      required: true
      schema:
        type: string
    post:
      summary: Cria um novo conteúdo
      tags:
      - Contents
      description: Cria um novo conteúdo no sistema
      operationId: createContent
      parameters:
      - name: USER_TOKEN
        in: header
        required: true
        description: Token de autorização do usuário
        schema:
          type: string
      - name: USER_EMAIL
        in: header
        required: true
        description: Email do usuário
        schema:
          type: string
      responses:
        '201':
          description: Conteúdo criado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ContentResponse"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                steps:
                  type: array
                  items:
                    type: object
                    properties:
                      skip_mandatory_fields:
                        type: boolean
                        description: Pular campos obrigatórios
                      skip_business_rules:
                        type: boolean
                        description: Pular regras de negócio
                      skip_external_input:
                        type: boolean
                        description: Pular entrada externa
                      skip_external_validation:
                        type: boolean
                        description: Pular validação externa
                      skip_webhook:
                        type: boolean
                        description: Pular webhook
                      id:
                        type: string
                        description: ID do step
                      templates:
                        type: array
                        items:
                          type: object
                          properties:
                            fields:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: string
                                    description: ID do campo
                                  value:
                                    type: string
                                    description: Valor do campo
                                required:
                                - id
                                - value
                          required:
                          - fields
                    required:
                    - id
                    - templates
              required:
              - steps
    put:
      summary: Atualiza/Cria conteúdo (upsert)
      tags:
      - Contents
      description: Atualiza um conteúdo existente ou cria um novo baseado em campos
        chave
      operationId: upsertContent
      parameters:
      - name: USER_TOKEN
        in: header
        required: true
        description: Token de autorização do usuário
        schema:
          type: string
      - name: USER_EMAIL
        in: header
        required: true
        description: Email do usuário
        schema:
          type: string
      responses:
        '200':
          description: Conteúdo atualizado/criado com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                    description: ID do conteúdo
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                key_fields:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: ID do campo chave
                      value:
                        type: string
                        description: Valor do campo chave
                    required:
                    - id
                    - value
                  description: Campos chave para identificar o conteúdo
                parent_id:
                  type: string
                  format: uuid
                  description: ID do conteúdo pai (obrigatório para subnegócios)
                steps:
                  type: array
                  items:
                    type: object
                    properties:
                      skip_mandatory_fields:
                        type: boolean
                        description: Pular campos obrigatórios
                      skip_business_rules:
                        type: boolean
                        description: Pular regras de negócio
                      skip_external_input:
                        type: boolean
                        description: Pular entrada externa
                      skip_external_validation:
                        type: boolean
                        description: Pular validação externa
                      skip_webhook:
                        type: boolean
                        description: Pular webhook
                      id:
                        type: string
                        description: ID do step
                      templates:
                        type: array
                        items:
                          type: object
                          properties:
                            fields:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: string
                                    description: ID do campo
                                  value:
                                    type: string
                                    description: Valor do campo
                                required:
                                - id
                                - value
                          required:
                          - fields
                    required:
                    - id
                    - templates
              required:
              - key_fields
              - steps
  "/external/v2/businesses/{business_id}/contents/{id}":
    parameters:
    - name: business_id
      in: path
      format: uuid
      description: ID do negócio
      required: true
      schema:
        type: string
    - name: id
      in: path
      format: uuid
      description: ID do conteúdo
      required: true
      schema:
        type: string
    get:
      summary: Busca um conteúdo específico
      tags:
      - Contents
      description: Retorna os dados de um conteúdo específico
      operationId: showContent
      parameters:
      - name: USER_TOKEN
        in: header
        required: true
        description: Token de autorização do usuário
        schema:
          type: string
      - name: USER_EMAIL
        in: header
        required: true
        description: Email do usuário
        schema:
          type: string
      responses:
        '200':
          description: Conteúdo encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Content"
        '404':
          description: Conteúdo não encontrado
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
    put:
      summary: Atualiza um conteúdo
      tags:
      - Contents
      description: Atualiza um conteúdo existente no sistema
      operationId: updateContent
      parameters:
      - name: USER_TOKEN
        in: header
        required: true
        description: Token de autorização do usuário
        schema:
          type: string
      - name: USER_EMAIL
        in: header
        required: true
        description: Email do usuário
        schema:
          type: string
      responses:
        '200':
          description: Conteúdo atualizado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ContentResponse"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                steps:
                  type: array
                  items:
                    type: object
                    properties:
                      skip_mandatory_fields:
                        type: boolean
                        description: Pular campos obrigatórios
                      skip_business_rules:
                        type: boolean
                        description: Pular regras de negócio
                      skip_external_input:
                        type: boolean
                        description: Pular entrada externa
                      skip_external_validation:
                        type: boolean
                        description: Pular validação externa
                      skip_webhook:
                        type: boolean
                        description: Pular webhook
                      id:
                        type: string
                        description: ID do step
                      templates:
                        type: array
                        items:
                          type: object
                          properties:
                            fields:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: string
                                    description: ID do campo
                                  value:
                                    type: string
                                    description: Valor do campo
                                required:
                                - id
                                - value
                          required:
                          - fields
                    required:
                    - id
                    - templates
              required:
              - steps
  "/external/v2/departments":
    get:
      summary: Lista departamentos
      tags:
      - Departments
      description: Retorna uma lista paginada de departamentos
      operationId: listDepartments
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      - name: offset
        in: query
        required: false
        description: 'Número de registros para pular (padrão: 0)'
        schema:
          type: integer
      - name: limit
        in: query
        required: false
        description: 'Número máximo de registros a retornar (padrão: 10, máximo: 500)'
        schema:
          type: integer
      responses:
        '200':
          description: Lista de departamentos retornada com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  departments:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Department"
                  total_departments:
                    type: integer
                    description: Total de departamentos no sistema
                  has_more_departments:
                    type: boolean
                    description: Se há mais departamentos para carregar
        '422':
          description: Limite excedido
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
    post:
      summary: Cria um novo departamento
      tags:
      - Departments
      description: Cria um novo departamento no sistema
      operationId: createDepartment
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '201':
          description: Departamento criado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/DepartmentResponse"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Nome do departamento
                limited:
                  type: boolean
                  description: Se o departamento tem acesso limitado
                  default: false
              required:
              - name
  "/external/v2/departments/{id}":
    parameters:
    - name: id
      in: path
      format: uuid
      description: ID do departamento
      required: true
      schema:
        type: string
    get:
      summary: Busca um departamento específico
      tags:
      - Departments
      description: Retorna os dados de um departamento específico
      operationId: showDepartment
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '200':
          description: Departamento encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Department"
        '404':
          description: Departamento não encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
    put:
      summary: Atualiza um departamento
      tags:
      - Departments
      description: Atualiza um departamento existente no sistema
      operationId: updateDepartment
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '200':
          description: Departamento atualizado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/DepartmentResponse"
        '404':
          description: Departamento não encontrado
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Nome do departamento
                limited:
                  type: boolean
                  description: Se o departamento tem acesso limitado
  "/external/v2/users":
    get:
      summary: Lista usuários
      tags:
      - Users
      description: Retorna uma lista paginada de usuários
      operationId: listUsers
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      - name: offset
        in: query
        required: false
        description: 'Número de registros para pular (padrão: 0)'
        schema:
          type: integer
      - name: limit
        in: query
        required: false
        description: 'Número máximo de registros a retornar (padrão: 10, máximo: 500)'
        schema:
          type: integer
      responses:
        '200':
          description: Lista de usuários retornada com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      "$ref": "#/components/schemas/User"
                  total_users:
                    type: integer
                    description: Total de usuários no sistema
                  has_more_users:
                    type: boolean
                    description: Se há mais usuários para carregar
        '422':
          description: Limite excedido
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
    post:
      summary: Cria um novo usuário
      tags:
      - Users
      description: Cria um novo usuário no sistema
      operationId: createUser
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '201':
          description: Usuário criado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UserResponse"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                provider:
                  type: string
                  enum:
                  - email
                  - google_oauth2
                  - entra_id
                  - openid_connect
                  description: Provedor de autenticação
                email:
                  type: string
                  format: email
                  description: Email do usuário
                name:
                  type: string
                  description: Nome completo do usuário
                password:
                  type: string
                  description: Senha do usuário (obrigatório para provider email)
                password_confirmation:
                  type: string
                  description: Confirmação da senha (obrigatório para provider email)
                limited:
                  type: boolean
                  description: Se o usuário tem acesso limitado
                  default: false
                coordinator:
                  type: boolean
                  description: Se o usuário é coordenador
                  default: false
                notification:
                  type: boolean
                  description: Se o usuário recebe notificações
                  default: true
                approved:
                  type: boolean
                  description: Se o usuário está aprovado
                  default: true
                confirmed_at:
                  type: string
                  format: date-time
                  description: Data de confirmação do usuário
                chat_enabled:
                  type: boolean
                  description: Se o chat está habilitado para o usuário
                  default: true
                block_menus:
                  type: array
                  items:
                    type: string
                  description: Lista de menus bloqueados para o usuário
                  default: []
                department_ids:
                  type: array
                  items:
                    type: string
                    format: uuid
                  description: IDs dos departamentos do usuário
                  default: []
              required:
              - provider
              - email
              - name
    put:
      summary: Atualiza um usuário
      tags:
      - Users
      description: Atualiza um usuário existente no sistema. Pode buscar por email
        ou id.
      operationId: updateUser
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '200':
          description: Usuário atualizado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UserResponse"
        '404':
          description: Usuário não encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                  description: ID do usuário (alternativo ao email)
                email:
                  type: string
                  format: email
                  description: Email do usuário (alternativo ao id)
                provider:
                  type: string
                  enum:
                  - email
                  - google_oauth2
                  - entra_id
                  - openid_connect
                  description: Provedor de autenticação
                name:
                  type: string
                  description: Nome completo do usuário
                password:
                  type: string
                  description: Nova senha do usuário
                password_confirmation:
                  type: string
                  description: Confirmação da nova senha
                limited:
                  type: boolean
                  description: Se o usuário tem acesso limitado
                coordinator:
                  type: boolean
                  description: Se o usuário é coordenador
                notification:
                  type: boolean
                  description: Se o usuário recebe notificações
                approved:
                  type: boolean
                  description: Se o usuário está aprovado
                confirmed_at:
                  type: string
                  format: date-time
                  description: Data de confirmação do usuário
                chat_enabled:
                  type: boolean
                  description: Se o chat está habilitado para o usuário
                block_menus:
                  type: array
                  items:
                    type: string
                  description: Lista de menus bloqueados para o usuário
                department_ids:
                  type: array
                  items:
                    type: string
                    format: uuid
                  description: IDs dos departamentos do usuário
  "/external/v2/users/{id}":
    parameters:
    - name: id
      in: path
      format: uuid
      description: ID do usuário
      required: true
      schema:
        type: string
    get:
      summary: Busca um usuário específico
      tags:
      - Users
      description: Retorna os dados de um usuário específico
      operationId: showUser
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '200':
          description: Usuário encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/User"
        '404':
          description: Usuário não encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
    delete:
      summary: Remove um usuário
      tags:
      - Users
      description: Remove um usuário do sistema
      operationId: deleteUser
      parameters:
      - name: ADMIN_TOKEN
        in: header
        required: true
        description: Token de autorização do administrador
        schema:
          type: string
      - name: ADMIN_EMAIL
        in: header
        required: true
        description: Email do administrador
        schema:
          type: string
      responses:
        '200':
          description: Usuário removido com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UserResponse"
        '404':
          description: Usuário não encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '401':
          description: Não autorizado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UnauthorizedResponse"
servers:
- url: https://{subdomain}.4mdg.com.br
  description: Servidor de produção
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: https://{subdomain}.staging.4mdg.com.br
  description: Servidor de staging
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: https://{subdomain}.uat.4mdg.com.br
  description: Servidor de UAT
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: https://{subdomain}.sandbox.4mdg.com.br
  description: Servidor de sandbox
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: http://{subdomain}.lvh.me:3000
  description: Servidor local
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
components:
  securitySchemes:
    admin_token:
      type: apiKey
      name: ADMIN_TOKEN
      in: header
      description: Token de autorização do administrador
    admin_email:
      type: apiKey
      name: ADMIN_EMAIL
      in: header
      description: Email do administrador
  schemas:
    Administrator:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único do administrador
        email:
          type: string
          format: email
          description: Email do administrador
        name:
          type: string
          description: Nome completo do administrador
        approved:
          type: boolean
          description: Se o administrador está aprovado
        confirmed_at:
          type:
          - string
          - 'null'
          format: date-time
          description: Data de confirmação do administrador
          nullable: true
        deleted_at:
          type:
          - string
          - 'null'
          format: date-time
          description: Data de exclusão do administrador
          nullable: true
        created_at:
          type: string
          format: date-time
          description: Data de criação
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
      required:
      - id
      - email
      - name
    AdministratorResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID do administrador criado/atualizado
      required:
      - id
    ErrorResponse:
      type: object
      properties:
        errors:
          oneOf:
          - type: string
          - type: array
            items:
              type: string
          description: Mensagem(ns) de erro
      required:
      - errors
    NotFoundResponse:
      type: object
      properties:
        error:
          type: string
          description: Mensagem de erro
          example: Registro não encontrado
      required:
      - error
    SuccessResponse:
      type: object
      properties:
        message:
          type: string
          description: Mensagem de sucesso
          example: Operação realizada com sucesso
      required:
      - message
    UnauthorizedResponse:
      type: object
      properties:
        error:
          type: string
          description: Mensagem de erro de autorização
      required:
      - error
    Company:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único da empresa
        name:
          type: string
          description: Nome da empresa
        subdomain:
          type: string
          description: Subdomínio da empresa
        api_key:
          type: string
          description: Chave de API da empresa
        created_at:
          type: string
          format: date-time
          description: Data de criação
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
      required:
      - id
      - name
      - subdomain
    CompanyResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID da empresa criada/atualizada
      required:
      - id
    Content:
      type: object
      properties:
        content_id:
          type: string
          format: uuid
          description: ID único do conteúdo
        steps:
          type: array
          items:
            type: object
          description: Steps do conteúdo
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
      required:
      - content_id
    ContentResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID do conteúdo criado/atualizado
        sub_ids:
          type: array
          items:
            type: string
            format: uuid
          description: IDs dos subconteúdos criados
      required:
      - id
    Department:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único do departamento
        name:
          type: string
          description: Nome do departamento
        limited:
          type: boolean
          description: Se o departamento tem acesso limitado
        deleted_at:
          type:
          - string
          - 'null'
          format: date-time
          description: Data de exclusão do departamento
          nullable: true
        created_at:
          type: string
          format: date-time
          description: Data de criação
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
      required:
      - id
      - name
    DepartmentResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID do departamento criado/atualizado
      required:
      - id
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único do usuário
        email:
          type: string
          format: email
          description: Email do usuário
        name:
          type: string
          description: Nome completo do usuário
        limited:
          type: boolean
          description: Se o usuário tem acesso limitado
        coordinator:
          type: boolean
          description: Se o usuário é coordenador
        notification:
          type: boolean
          description: Se o usuário recebe notificações
        approved:
          type: boolean
          description: Se o usuário está aprovado
        confirmed_at:
          type:
          - string
          - 'null'
          format: date-time
          description: Data de confirmação do usuário
          nullable: true
        deleted_at:
          type:
          - string
          - 'null'
          format: date-time
          description: Data de exclusão do usuário
          nullable: true
        chat_enabled:
          type: boolean
          description: Se o chat está habilitado para o usuário
        block_menus:
          type: array
          items:
            type: string
          description: Lista de menus bloqueados para o usuário
        allow_password_change:
          type: boolean
          description: Se o usuário pode alterar a senha
        department_ids:
          type: array
          items:
            type: string
            format: uuid
          description: IDs dos departamentos do usuário
        created_at:
          type: string
          format: date-time
          description: Data de criação
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
      required:
      - id
      - email
      - name
    UserResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID do usuário criado/atualizado
      required:
      - id
