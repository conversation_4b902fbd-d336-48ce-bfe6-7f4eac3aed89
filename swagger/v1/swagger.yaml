---
openapi: 3.0.1
info:
  title: 4MDG API V1
  version: v1
  description: API V1 para integração com o sistema 4MDG
paths:
  "/external/administrators.json":
    post:
      summary: Cria um novo administrador
      tags:
      - Administrators
      description: Cria um novo administrador no sistema
      operationId: createAdministrator
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '201':
          description: Administrador criado com sucesso
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponseV1"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/AdministratorV1"
  "/external/administrators/{id}.json":
    parameters:
    - name: id
      in: path
      format: uuid
      description: ID do administrador
      required: true
      schema:
        type: string
    delete:
      summary: Remove um administrador
      tags:
      - Administrators
      description: Remove um administrador do sistema (soft delete)
      operationId: deleteAdministrator
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '204':
          description: Administrador removido com sucesso
        '404':
          description: Administrador não encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/NotFoundResponseV1"
  "/external/contents/{content_id}/steps/{id}/authorize":
    parameters:
    - name: content_id
      in: path
      format: uuid
      description: ID do conteúdo
      required: true
      schema:
        type: string
    - name: id
      in: path
      format: uuid
      description: ID do step
      required: true
      schema:
        type: string
    patch:
      summary: Autoriza uma resposta
      tags:
      - Answers
      description: Autoriza uma resposta específica de um step
      operationId: authorizeAnswer
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '200':
          description: Resposta autorizada com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AnswerSuccessResponseV1"
        '422':
          description: Erro ao autorizar resposta
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AnswerErrorResponseV1"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/AnswerActionV1"
  "/external/contents/{content_id}/steps/{id}/reject":
    parameters:
    - name: content_id
      in: path
      format: uuid
      description: ID do conteúdo
      required: true
      schema:
        type: string
    - name: id
      in: path
      format: uuid
      description: ID do step
      required: true
      schema:
        type: string
    patch:
      summary: Rejeita uma resposta
      tags:
      - Answers
      description: Rejeita uma resposta específica de um step
      operationId: rejectAnswer
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '200':
          description: Resposta rejeitada com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AnswerSuccessResponseV1"
        '422':
          description: Erro ao rejeitar resposta
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AnswerErrorResponseV1"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/AnswerActionV1"
  "/external/contents/{content_id}/steps/{id}/changing":
    parameters:
    - name: content_id
      in: path
      format: uuid
      description: ID do conteúdo
      required: true
      schema:
        type: string
    - name: id
      in: path
      format: uuid
      description: ID do step
      required: true
      schema:
        type: string
    patch:
      summary: Marca resposta como em alteração
      tags:
      - Answers
      description: Marca uma resposta como em processo de alteração
      operationId: changingAnswer
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '200':
          description: Resposta marcada como em alteração
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AnswerSuccessResponseV1"
        '422':
          description: Erro ao marcar resposta como em alteração
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/AnswerErrorResponseV1"
  "/external/businesses":
    post:
      summary: Cria ou atualiza conteúdos via XML
      tags:
      - Businesses
      description: Cria ou atualiza conteúdos através de XML estruturado
      operationId: createBusinessContents
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '422':
          description: Dados inválidos no XML
      requestBody:
        content:
          application/xml:
            schema:
              "$ref": "#/components/schemas/BusinessXmlRequestV1"
        required: true
  "/external/businesses/{id}":
    parameters:
    - name: id
      in: path
      format: uuid
      description: ID do conteúdo
      required: true
      schema:
        type: string
    put:
      summary: Atualiza conteúdos via XML
      tags:
      - Businesses
      description: Atualiza conteúdos existentes através de XML estruturado
      operationId: updateBusinessContents
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '422':
          description: Dados inválidos no XML
      requestBody:
        content:
          application/xml:
            schema:
              "$ref": "#/components/schemas/BusinessXmlRequestV1"
        required: true
    delete:
      summary: Remove um conteúdo
      tags:
      - Businesses
      description: Remove um conteúdo específico do sistema
      operationId: deleteBusinessContent
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      - name: user_who_deleted_id
        in: query
        format: uuid
        required: false
        description: ID do usuário que está deletando
        schema:
          type: string
      - name: deletion_reason
        in: query
        required: false
        description: Motivo da exclusão
        schema:
          type: string
      responses:
        '404':
          description: Conteúdo não encontrado
  "/external/contents.xml":
    get:
      summary: Lista conteúdos em formato XML
      tags:
      - Contents
      description: Retorna uma lista de conteúdos em formato XML com base nos parâmetros
        fornecidos
      operationId: getContents
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      - name: business_id
        in: query
        format: uuid
        required: true
        description: ID do business
        schema:
          type: string
      - name: contents
        in: query
        required: false
        description: Se deve incluir conteúdos na resposta
        default: true
        schema:
          type: boolean
      - name: structures
        in: query
        required: false
        description: Se deve incluir estruturas na resposta
        default: false
        schema:
          type: boolean
      - name: deleted
        in: query
        required: false
        description: Se deve incluir conteúdos deletados
        default: false
        schema:
          type: boolean
      - name: start
        in: query
        format: date-time
        required: false
        description: Data de início para filtrar por updated_at
        schema:
          type: string
      - name: end
        in: query
        format: date-time
        required: false
        description: Data de fim para filtrar por updated_at
        schema:
          type: string
      - name: content_id
        in: query
        format: uuid
        required: false
        description: ID específico do conteúdo
        schema:
          type: string
      - name: sub_content_id
        in: query
        format: uuid
        required: false
        description: ID específico do sub-conteúdo
        schema:
          type: string
      - name: parent_id
        in: query
        format: uuid
        required: false
        description: ID do conteúdo pai
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: Número da página para paginação
        default: 1
        schema:
          type: integer
      responses:
        '200':
          description: Conteúdos filtrados por data retornados com sucesso
          content:
            application/xml:
              schema:
                "$ref": "#/components/schemas/ContentXmlResponseV1"
        '404':
          description: Business não encontrado
  "/external/notifications.json":
    post:
      summary: Cria uma nova notificação
      tags:
      - Notifications
      description: Cria uma nova notificação no sistema
      operationId: createNotification
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '201':
          description: Notificação criada com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/NotificationCreateResponseV1"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponseV1"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/NotificationV1"
  "/external/notifications/{id}.json":
    parameters:
    - name: id
      in: path
      format: uuid
      description: ID da notificação
      required: true
      schema:
        type: string
    get:
      summary: Busca uma notificação
      tags:
      - Notifications
      description: Busca uma notificação específica
      operationId: getNotification
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '200':
          description: Notificação encontrada
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/NotificationResponseV1"
        '404':
          description: Notificação não encontrada
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/NotFoundResponseV1"
    put:
      summary: Atualiza uma notificação
      tags:
      - Notifications
      description: Atualiza uma notificação existente
      operationId: updateNotification
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '200':
          description: Notificação atualizada com sucesso
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponseV1"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/NotificationUpdateV1"
    delete:
      summary: Remove uma notificação
      tags:
      - Notifications
      description: Remove uma notificação do sistema (soft delete)
      operationId: deleteNotification
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '204':
          description: Notificação removida com sucesso
  "/external/sub_contents.xml":
    get:
      summary: Lista sub-conteúdos em formato XML
      tags:
      - Sub Contents
      description: Retorna uma lista de sub-conteúdos em formato XML com base nos
        parâmetros fornecidos
      operationId: getSubContents
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      - name: sub_business_id
        in: query
        format: uuid
        required: true
        description: ID do sub-business
        schema:
          type: string
      - name: contents
        in: query
        required: false
        description: Se deve incluir conteúdos na resposta
        default: true
        schema:
          type: boolean
      - name: structures
        in: query
        required: false
        description: Se deve incluir estruturas na resposta
        default: false
        schema:
          type: boolean
      - name: deleted
        in: query
        required: false
        description: Se deve incluir conteúdos deletados
        default: false
        schema:
          type: boolean
      - name: start
        in: query
        format: date-time
        required: false
        description: Data de início para filtrar por updated_at
        schema:
          type: string
      - name: end
        in: query
        format: date-time
        required: false
        description: Data de fim para filtrar por updated_at
        schema:
          type: string
      - name: content_id
        in: query
        format: uuid
        required: false
        description: ID específico do conteúdo
        schema:
          type: string
      - name: sub_content_id
        in: query
        format: uuid
        required: false
        description: ID específico do sub-conteúdo
        schema:
          type: string
      - name: parent_id
        in: query
        format: uuid
        required: false
        description: ID do conteúdo pai
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: Número da página para paginação
        default: 1
        schema:
          type: integer
      responses:
        '200':
          description: Sub-conteúdos filtrados por data retornados com sucesso
          content:
            application/xml:
              schema:
                "$ref": "#/components/schemas/SubContentXmlResponseV1"
        '404':
          description: Sub-business não encontrado
  "/external/users.json":
    post:
      summary: Cria um novo usuário
      tags:
      - Users
      description: Cria um novo usuário no sistema
      operationId: createUser
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '201':
          description: Usuário criado com sucesso
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/UserResponseV1"
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponseV1"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/UserV1"
  "/external/users/{id}.json":
    parameters:
    - name: id
      in: path
      format: uuid
      description: ID do usuário
      required: true
      schema:
        type: string
    put:
      summary: Atualiza um usuário
      tags:
      - Users
      description: Atualiza um usuário existente no sistema
      operationId: updateUser
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '200':
          description: Usuário atualizado com sucesso
        '422':
          description: Dados inválidos
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponseV1"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Nome completo do usuário
                email:
                  type: string
                  format: email
                  description: Email do usuário
                password:
                  type: string
                  description: Nova senha do usuário
                password_confirmation:
                  type: string
                  description: Confirmação da nova senha
                department_ids:
                  type: array
                  items:
                    type: string
                    format: uuid
                  description: IDs dos departamentos do usuário
                provider:
                  type: string
                  description: Provedor de autenticação
                limited:
                  type: boolean
                  description: Se o usuário tem acesso limitado
                coordinator:
                  type: boolean
                  description: Se o usuário é coordenador
                notification:
                  type: boolean
                  description: Se o usuário recebe notificações
                approved:
                  type: boolean
                  description: Se o usuário foi aprovado
                chat_enabled:
                  type: boolean
                  description: Se o chat está habilitado
                block_menus:
                  type: array
                  items:
                    type: string
                  description: Menus bloqueados para o usuário
    delete:
      summary: Remove um usuário
      tags:
      - Users
      description: Remove um usuário do sistema (soft delete)
      operationId: deleteUser
      security:
      - basic_auth: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Basic authentication with API key
        schema:
          type: string
      responses:
        '204':
          description: Usuário removido com sucesso
        '404':
          description: Usuário não encontrado
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/NotFoundResponseV1"
servers:
- url: https://{subdomain}.4mdg.com.br
  description: Servidor de produção
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: https://{subdomain}.staging.4mdg.com.br
  description: Servidor de staging
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: https://{subdomain}.uat.4mdg.com.br
  description: Servidor de UAT
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: https://{subdomain}.sandbox.4mdg.com.br
  description: Servidor de sandbox
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
- url: http://{subdomain}.lvh.me:3000
  description: Servidor local
  variables:
    subdomain:
      default: testes
      description: Subdomínio da empresa
components:
  securitySchemes:
    basic_auth:
      type: http
      scheme: basic
      description: Autenticação básica usando API Key da empresa
  schemas:
    AdministratorV1:
      type: object
      properties:
        name:
          type: string
          description: Nome completo do administrador
          example: nome do usuário
        email:
          type: string
          format: email
          description: Email do administrador
          example: email do usuário
        password:
          type: string
          description: Senha do administrador
          example: senha do usuário
        password_confirmation:
          type: string
          description: Confirmação da senha
          example: senha do usuário
        approved:
          type: boolean
          description: Se o administrador foi aprovado
          default: false
          example: true
        owner:
          type: boolean
          description: Se o administrador é proprietário
          default: false
          example: false
      required:
      - name
      - email
      - password
      - password_confirmation
    AdministratorResponseV1:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único do administrador
          example: administrator_id
        name:
          type: string
          description: Nome completo do administrador
          example: nome do usuário
        email:
          type: string
          format: email
          description: Email do administrador
          example: email do usuário
        approved:
          type: boolean
          description: Se o administrador foi aprovado
          example: true
        owner:
          type: boolean
          description: Se o administrador é proprietário
          example: false
        created_at:
          type: string
          format: date-time
          description: Data de criação
          example: '2024-01-15T10:30:00Z'
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
          example: '2024-01-15T10:30:00Z'
    ErrorResponseV1:
      type: object
      properties:
        errors:
          type: array
          items:
            type: string
          description: Lista de erros de validação
          example:
          - Email já está em uso
          - Senha é muito curta
    AnswerActionV1:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
          description: ID do usuário que está executando a ação
          example: user_id
      required:
      - user_id
    AnswerSuccessResponseV1:
      type: object
      properties:
        success:
          type: boolean
          description: Indica se a operação foi bem-sucedida
          example: true
        guids:
          type: array
          items:
            type: string
            format: uuid
          description: Lista de IDs das respostas afetadas
          example:
          - answer_id
        warning:
          type: array
          items:
            type: string
          description: Lista de avisos (se houver)
          example: []
    AnswerErrorResponseV1:
      type: object
      properties:
        errors:
          type: array
          items:
            type: string
          description: Lista de erros
          example:
          - Erro ao processar a resposta
    BusinessXmlRequestV1:
      type: string
      format: xml
      description: XML contendo dados de business e conteúdos para criação/atualização
      example: |
        <?xml version="1.0" encoding="UTF-8"?>
        <fourmdg>
          <business guid="business_id" name="Business Name" group_guid="group_id">
            <contents>
              <content guid="content_id" updated_at="2024-01-15T10:30:00Z" created_by_id="user_id" created_by_name="User Name" current_step_id="step_id" current_step_name="Step Name">
                <step label="Step Name" user="user_id" current_user="current_user_id" guid="step_id">
                  <template guid="template_id">
                    <field guid="field_id" label="Field Label" value="Field Value"/>
                  </template>
                </step>
              </content>
            </contents>
          </business>
        </fourmdg>
    BusinessSuccessResponseV1:
      type: object
      properties:
        success:
          type: boolean
          description: Indica se a operação foi bem-sucedida
          example: true
        guids:
          type: array
          items:
            type: string
            format: uuid
          description: Lista de IDs dos conteúdos criados/atualizados
          example:
          - content_id_1
          - content_id_2
        warning:
          type: array
          items:
            type: string
          description: Lista de avisos (se houver)
          example: []
    BusinessErrorResponseV1:
      type: object
      properties:
        errors:
          type: array
          items:
            type: string
          description: Lista de erros
          example:
          - Erro ao processar o XML
    BusinessDeleteRequestV1:
      type: object
      properties:
        user_who_deleted_id:
          type: string
          format: uuid
          description: ID do usuário que está deletando o conteúdo
          example: user_id
        deletion_reason:
          type: string
          description: Motivo da exclusão
          example: Conteúdo obsoleto
    BusinessDeleteSuccessResponseV1:
      type: object
      properties:
        success:
          type: boolean
          description: Indica se a exclusão foi bem-sucedida
          example: true
    BusinessNotFoundResponseV1:
      type: object
      properties:
        success:
          type: boolean
          description: Indica se a operação foi bem-sucedida
          example: false
        errors:
          type: string
          description: Mensagem de erro
          example: Record not found
    NotFoundResponseV1:
      type: object
      properties:
        status:
          type: integer
          description: Código de status HTTP
          example: 404
        error:
          type: string
          description: Mensagem de erro
          example: Not Found
        exception:
          type: string
          description: Tipo da exceção
          example: "#<ActiveRecord::RecordNotFound: Couldn't find User with 'id'=invalid_id>"
    UnauthorizedResponseV1:
      type: object
      properties:
        error:
          type: string
          description: Mensagem de erro de autorização
          example: Unauthorized
    ContentQueryParametersV1:
      type: object
      properties:
        business_id:
          type: string
          format: uuid
          description: ID do business
          example: business_id
        contents:
          type: boolean
          description: Se deve incluir conteúdos na resposta
          default: true
          example: true
        structures:
          type: boolean
          description: Se deve incluir estruturas na resposta
          default: false
          example: false
        deleted:
          type: boolean
          description: Se deve incluir conteúdos deletados
          default: false
          example: false
        start:
          type: string
          format: date-time
          description: Data de início para filtrar por updated_at
          example: '2024-01-01T00:00:00Z'
        end:
          type: string
          format: date-time
          description: Data de fim para filtrar por updated_at
          example: '2024-12-31T23:59:59Z'
        content_id:
          type: string
          format: uuid
          description: ID específico do conteúdo
          example: content_id
        sub_content_id:
          type: string
          format: uuid
          description: ID específico do sub-conteúdo
          example: sub_content_id
        parent_id:
          type: string
          format: uuid
          description: ID do conteúdo pai
          example: parent_content_id
        page:
          type: integer
          description: Número da página para paginação
          default: 1
          example: 1
      required:
      - business_id
    ContentXmlResponseV1:
      type: string
      format: xml
      description: Resposta em formato XML contendo os dados dos conteúdos
      example: |
        <?xml version="1.0" encoding="UTF-8"?>
        <fourmdg tenant="test">
          <business guid="business_id" name="Business Name" group_guid="group_id">
            <contents>
              <content guid="content_id" updated_at="2024-01-15T10:30:00Z" created_by_id="user_id" created_by_name="User Name" current_step_id="step_id" current_step_name="Step Name">
                <step label="Step Name" user="user_id" current_user="current_user_id" guid="step_id">
                  <template guid="template_id">
                    <field guid="field_id" label="Field Label" value="Field Value"/>
                  </template>
                </step>
              </content>
            </contents>
            <structures>
              <steps>
                <step guid="step_id" label="Step Name">
                  <template guid="template_id" name="Template Name" description="Template Description" variable="template_variable"/>
                </step>
              </steps>
              <templates>
                <template guid="template_id" variable="template_variable">
                  <field guid="field_id" type="text" size="100" height="1" tooltip="Field Tooltip" order="1" required="true" enabled="true" visible="true" label="Field Label" input_variable="input_var" output_variable="output_var" show_on_list="true" show_on_form="true">
                    <option label="Option Label" value="option_value"/>
                  </field>
                </template>
              </templates>
            </structures>
          </business>
        </fourmdg>
    NotificationV1:
      type: object
      properties:
        title:
          type: string
          description: Título da notificação
          example: título da notificação
        message:
          type: string
          description: Mensagem da notificação
          example: mensagem da notificação
        user_id:
          type: string
          format: uuid
          description: ID do usuário que criou a notificação (apenas para criação)
          example: user_id
        destiny_users:
          type: array
          items:
            type: string
            format: uuid
          description: IDs dos usuários destinatários
          example:
          - user_id_1
          - user_id_2
        destiny_departments:
          type: array
          items:
            type: string
            format: uuid
          description: IDs dos departamentos destinatários
          example:
          - department_id_1
          - department_id_2
      required:
      - title
      - message
    NotificationUpdateV1:
      type: object
      properties:
        title:
          type: string
          description: Título da notificação
          example: título da notificação
        message:
          type: string
          description: Mensagem da notificação
          example: mensagem da notificação
        destiny_users:
          type: array
          items:
            type: string
            format: uuid
          description: IDs dos usuários destinatários
          example:
          - user_id_1
          - user_id_2
        destiny_departments:
          type: array
          items:
            type: string
            format: uuid
          description: IDs dos departamentos destinatários
          example:
          - department_id_1
          - department_id_2
    NotificationResponseV1:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único da notificação
          example: notification_id
        title:
          type: string
          description: Título da notificação
          example: título da notificação
        message:
          type: string
          description: Mensagem da notificação
          example: mensagem da notificação
        user_id:
          type: string
          format: uuid
          description: ID do usuário que criou a notificação
          example: user_id
        created_at:
          type: string
          format: date-time
          description: Data de criação
          example: '2024-01-15T10:30:00Z'
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
          example: '2024-01-15T10:30:00Z'
    NotificationCreateResponseV1:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID da notificação criada
          example: notification_id
    SubContentQueryParametersV1:
      type: object
      properties:
        sub_business_id:
          type: string
          format: uuid
          description: ID do sub-business
          example: sub_business_id
        contents:
          type: boolean
          description: Se deve incluir conteúdos na resposta
          default: true
          example: true
        structures:
          type: boolean
          description: Se deve incluir estruturas na resposta
          default: false
          example: false
        deleted:
          type: boolean
          description: Se deve incluir conteúdos deletados
          default: false
          example: false
        start:
          type: string
          format: date-time
          description: Data de início para filtrar por updated_at
          example: '2024-01-01T00:00:00Z'
        end:
          type: string
          format: date-time
          description: Data de fim para filtrar por updated_at
          example: '2024-12-31T23:59:59Z'
        content_id:
          type: string
          format: uuid
          description: ID específico do conteúdo
          example: content_id
        sub_content_id:
          type: string
          format: uuid
          description: ID específico do sub-conteúdo
          example: sub_content_id
        parent_id:
          type: string
          format: uuid
          description: ID do conteúdo pai
          example: parent_content_id
        page:
          type: integer
          description: Número da página para paginação
          default: 1
          example: 1
      required:
      - sub_business_id
    SubContentXmlResponseV1:
      type: string
      format: xml
      description: Resposta em formato XML contendo os dados dos sub-conteúdos
      example: |
        <?xml version="1.0" encoding="UTF-8"?>
        <fourmdg tenant="test">
          <business guid="sub_business_id" name="Sub Business Name" group_guid="group_id">
            <contents>
              <content guid="sub_content_id" updated_at="2024-01-15T10:30:00Z" created_by_id="user_id" created_by_name="User Name" current_step_id="step_id" current_step_name="Step Name" parent_id="parent_content_id">
                <step label="Step Name" user="user_id" current_user="current_user_id" guid="step_id">
                  <template guid="template_id">
                    <field guid="field_id" label="Field Label" value="Field Value"/>
                  </template>
                </step>
              </content>
            </contents>
            <structures>
              <steps>
                <step guid="step_id" label="Step Name">
                  <template guid="template_id" name="Template Name" description="Template Description" variable="template_variable"/>
                </step>
              </steps>
              <templates>
                <template guid="template_id" variable="template_variable">
                  <field guid="field_id" type="text" size="100" height="1" tooltip="Field Tooltip" order="1" required="true" enabled="true" visible="true" label="Field Label" input_variable="input_var" output_variable="output_var" show_on_list="true" show_on_form="true">
                    <option label="Option Label" value="option_value"/>
                  </field>
                </template>
              </templates>
            </structures>
          </business>
        </fourmdg>
    UserV1:
      type: object
      properties:
        name:
          type: string
          description: Nome completo do usuário
          example: nome do usuário
        email:
          type: string
          format: email
          description: Email do usuário
          example: email do usuário
        password:
          type: string
          description: Senha do usuário (obrigatória para provider email)
          example: senha do usuário
        password_confirmation:
          type: string
          description: Confirmação da senha (obrigatória para provider email)
          example: senha do usuário
        department_ids:
          type: array
          items:
            type: string
            format: uuid
          description: IDs dos departamentos do usuário
          example:
          - department_id
        provider:
          type: string
          description: Provedor de autenticação
          default: email
          example: email
        limited:
          type: boolean
          description: Se o usuário tem acesso limitado
          default: false
          example: false
        coordinator:
          type: boolean
          description: Se o usuário é coordenador
          default: false
          example: false
        notification:
          type: boolean
          description: Se o usuário recebe notificações
          default: false
          example: false
        approved:
          type: boolean
          description: Se o usuário foi aprovado
          default: true
          example: true
        create_confirmed:
          type: boolean
          description: Se deve criar o usuário já confirmado
          default: false
          example: false
        chat_enabled:
          type: boolean
          description: Se o chat está habilitado
          default: false
          example: false
        block_menus:
          type: array
          items:
            type: string
          description: Menus bloqueados para o usuário
          example:
          - dashboard
          - search
          - favorites
          - bulk_saving_answers
          - answer_versions
          - report
          - statistics
      required:
      - name
      - email
      - provider
    UserResponseV1:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único do usuário
          example: user_id
        name:
          type: string
          description: Nome completo do usuário
          example: nome do usuário
        email:
          type: string
          format: email
          description: Email do usuário
          example: email do usuário
        limited:
          type: boolean
          description: Se o usuário tem acesso limitado
          example: false
        coordinator:
          type: boolean
          description: Se o usuário é coordenador
          example: false
        notification:
          type: boolean
          description: Se o usuário recebe notificações
          example: false
        approved:
          type: boolean
          description: Se o usuário foi aprovado
          example: true
        chat_enabled:
          type: boolean
          description: Se o chat está habilitado
          example: false
        created_at:
          type: string
          format: date-time
          description: Data de criação
          example: '2024-01-15T10:30:00Z'
        updated_at:
          type: string
          format: date-time
          description: Data de atualização
          example: '2024-01-15T10:30:00Z'
