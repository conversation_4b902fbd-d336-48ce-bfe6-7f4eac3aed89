class AddAnswerVersionPerformanceIndexes < ActiveRecord::Migration[7.2]
  def change
    add_index :answer_versions, :created_at,
              name: 'idx_answer_versions_created_at'

    add_index :contents, [:business_id, :id],
              name: 'idx_contents_business_id'

    add_index :answers, [:step_id, :id], name: 'idx_answers_step_id'

    add_index :answer_versions, :id,
              where: 'object_changes IS NOT NULL',
              name: 'idx_answer_versions_with_changes'
  end
end
