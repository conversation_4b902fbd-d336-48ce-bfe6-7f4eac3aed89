class NotificationsController < ApplicationController
  before_action :authenticate_user!

  def index
    records = Notification.order('created_at DESC').kept.not_dismissed(current_user.id)

    department_ids = current_user.departments.pluck(:id)

    @results = if department_ids.blank?
                 (records.without_destination.or(records.for_user(current_user.id))).first(20)
               else
                 (records.without_destination.or(records.for_user(current_user.id)).or(records.for_department(department_ids))).first(20)
               end
  end

  def read
    service = NotificationService.new(user_id: current_user.id)
    service.read(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end
end
