class MenusController < ApplicationController
  before_action :set_company_enable_internationalization, only: %i[index]

  def index
    business_ids_with_permission = Business.kept
                                          .joins(steps: :step_permissions)
                                          .left_joins(steps: { step_permissions: { department: :users } })
                                          .where(
                                            'step_permissions.user_id = ? OR users.id = ?',
                                            current_user.id, current_user.id
                                          )
                                          .distinct
                                          .pluck(:id)

    group_ids_with_businesses = Business.kept
                                       .where(id: business_ids_with_permission)
                                       .pluck(:business_group_id)
                                       .uniq

    groups_query = BusinessGroup.kept.where(id: group_ids_with_businesses).order(:name)
    groups_query = groups_query.includes(:translations) if needs_translations?

    businesses_query = Business.kept.where(id: business_ids_with_permission)
    businesses_query = businesses_query.includes(:translations) if needs_translations?
    businesses_by_group = businesses_query.group_by(&:business_group_id)

    all_subbusiness_ids = Field.kept
                               .joins(template: { steps: :business })
                               .where(type: :sub_business, businesses: { id: business_ids_with_permission })
                               .where.not(reference_sub_business_id: nil)
                               .pluck(:reference_sub_business_id)
                               .uniq

    subbusinesses_by_id = {}
    business_to_subbusiness_map = {}

    if all_subbusiness_ids.any?
      subbusinesses_query = Business.kept.where(id: all_subbusiness_ids)
      subbusinesses_query = subbusinesses_query.includes(:translations) if needs_translations?
      subbusinesses_by_id = subbusinesses_query.index_by(&:id)

      Field.kept
           .joins(template: { steps: :business })
           .where(type: :sub_business, businesses: { id: business_ids_with_permission })
           .where.not(reference_sub_business_id: nil)
           .pluck(:reference_sub_business_id, 'businesses.id')
           .each do |subbusiness_id, business_id|
        business_to_subbusiness_map[business_id] ||= []
        business_to_subbusiness_map[business_id] << subbusiness_id unless business_to_subbusiness_map[business_id].include?(subbusiness_id)
      end
    end

    menus = groups_query.map do |group|
      valid_businesses = (businesses_by_group[group.id] || []).sort_by(&:name)

      {
        id: group.id,
        icon: group.icon,
        name: translated_attribute_optimized(group, 'name'),
        children: valid_businesses.map do |business|
          {
            id: business.id,
            name: translated_attribute_optimized(business, 'name'),
            icon: business.icon,
            children: build_subbusiness_menu(business.id, business_to_subbusiness_map, subbusinesses_by_id)
          }
        end
      }
    end

    menus = menus.select { |m| m[:children].present? }
    render json: menus
  end

  private

  def build_subbusiness_menu(business_id, business_to_subbusiness_map, subbusinesses_by_id)
    subbusiness_ids = business_to_subbusiness_map[business_id] || []

    subbusiness_ids.map do |subbusiness_id|
      subbusiness = subbusinesses_by_id[subbusiness_id]
      next unless subbusiness

      {
        id: subbusiness.id,
        icon: subbusiness.icon,
        name: translated_attribute_optimized(subbusiness, 'name')
      }
    end.compact
  end

  def needs_translations?
    @company_enable_internationalization && !current_administrator
  end

  def translated_attribute_optimized(record, attribute)
    return record[attribute] unless needs_translations?

    translation = record.translations.find { |t| t.attribute_name == attribute && t.language == Translation.devise_languages[I18n.locale] }
    translation&.translated_text || record[attribute]
  end
end
