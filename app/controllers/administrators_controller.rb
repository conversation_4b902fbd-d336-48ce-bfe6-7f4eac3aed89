class AdministratorsController < ApplicationController
  include DiscardedFilter

  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!
  before_action :validate_public_admin, except: :index

  def index
    respond_to do |format|
      format.datatable do
        authorize Administrator
        render json: AdministratorDatatable.new(params, current_administrator: current_administrator, current_tenant: Apartment::Tenant.current), status: :ok
      end
      format.json do
        @administrators = Administrator.all
        @administrators = discarded_filter(@administrators, params[:active])
        @administrators = @administrators.order('deleted_at NULLS FIRST').order(:name)
      end
    end
  end

  def show
    @administrator = Administrator.find(params[:id])
  end

  def create
    service = AdministratorService.new(administrator_params)
    authorize service
    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = AdministratorService.new(administrator_params)
    authorize service
    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = AdministratorService.new
    authorize service
    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = AdministratorService.new

    service.restore(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def administrator_params
    params.permit(:name, :email, :password, :password_confirmation, :approved, :owner)
  end

  def validate_public_admin
    return render json: { errors: I18n.t('forbidden', scope: 'activerecord.errors.messages') }, status: :unauthorized if Apartment::Tenant.current == 'public' && (!current_administrator.owner && current_administrator.id != params[:id])
  end
end
