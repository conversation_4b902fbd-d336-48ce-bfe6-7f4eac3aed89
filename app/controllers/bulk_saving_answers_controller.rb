class BulkSavingAnswersController < ApplicationController
  before_action :authenticate_user!
  include SkipTranslation

  # Read methods
  def index
    respond_to do |format|
      format.datatable do
        render json: BulkSavingAnswerDatatable.new(params, current_user: current_user), status: :ok
      end

      format.json do
        @records = []
      end
    end
  end

  def show
    @record = BulkSavingAnswer.find(params[:id])
  end

  # Helper methods
  def fill
    service = BulkAnswerService.new(answer_params)
    data = service.pre_filled
    if service.success
      render json: data, status: :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def process_orphans
    service = BulkAnswerService.new(process_orphans_params)

    service.process_orphans params[:id]

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  # Create methods
  def create
    service = BulkAnswerService.new(answer_params)

    service.create

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = BulkAnswerService.new(answer_params)

    service.update params[:id]

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def bulk_alteration
    service = BulkAnswerService.new(alteration_params)

    service.bulk_alteration

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def bulk_alteration_preview
    service = BulkAnswerService.new(alteration_params)

    preview_count = service.bulk_alteration_preview

    render json: { count: preview_count }, status: :ok
  end

  private

  def process_orphans_params
    { user_id: current_user.id }
  end

  def answer_params
    params.permit(
      :step_id,
      :lines_to_fill,
      :enable_verification_url,
      :enable_validation_url,
      :enable_business_validations,
      :enable_field_validations,
      answers: [allowed_values]).merge(user_id: current_user.id, ip: remote_ip(request))
  end

  def allowed_values
    step = Step.find_by(id: params[:step_id])

    return unless step

    values = step&.templates&.map do |template|
      template.fields.kept.map do |field|
        field['id'].to_sym
      end
    end
    pk_field_ids = FieldSearcher.new(business_id: step.business_id).pk_fields_for_business.map(&:id)
    values.flatten + %i[id] + pk_field_ids
  end

  def alteration_params
    params.permit(
      :business_id,
      :bulk_action,
      :validation_url,
      :verification_url,
      alterations: [:step_id, :field_id, :field_label, :label_value, :value, { value: [] }],
      criterions: [:step_id, :field_id, :operator, :text, :value, { value: [] }],
      approvals: [:step_id]).merge(user_id: current_user.id, ip: remote_ip(request))
  end
end
