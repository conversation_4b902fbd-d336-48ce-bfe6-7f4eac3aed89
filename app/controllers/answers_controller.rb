class AnswersController < ApplicationController
  before_action :authenticate_user!
  before_action :check_limited, only: %i[show update validate]
  before_action :check_permission, only: %i[show]
  before_action :set_company_enable_internationalization, only: %i[show]
  before_action :skip_verification_url?, only: %i[show]

  include SkipTranslation

  def show
    data = {
      content_id: answer.content_id,
      answer_id: answer.id,
      business_id: answer.content.business_id,
      step_name: answer.step.name
    }

    troubleshooting = Troubleshooting.create!(
      thread_id: Thread.current.object_id,
      request_id: request.uuid,
      subdomain: Apartment::Tenant.current,
      start_at: Time.zone.now,
      url: request.original_url,
      external: false,
      data: data
    )

    @skip_verification_url = skip_verification_url?
    @answer = answer.decorate(context: { request: request, skip_verification_url: @skip_verification_url, parents: parents_params.to_h[:parents] }, current_user: current_user)
    troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: Time.zone.now - troubleshooting.start_at, response_code: 'ok')
  end
  
  def update
    data = {
      content_id: answer.content_id,
      answer_id: answer.id,
      business_id: answer.content.business_id,
      step_name: answer.step.name
    }

    troubleshooting = Troubleshooting.create!(thread_id: Thread.current.object_id, request_id: request.uuid, subdomain: Apartment::Tenant.current, start_at: Time.zone.now, url: request.original_url, external: false, data: data)
    service = AnswerService.new(fix_param_values(answer_params).merge(origin: Answer.origins[:client]))

    service.update(params[:id])

    if service.success
      troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: Time.zone.now - troubleshooting.start_at, response_code: 'ok')

      render json: { current_answer_id: service.record.content.current_answer_id }
    else
      troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: Time.zone.now - troubleshooting.start_at, response_code: 'error')
      render json: { errors: service.errors, sub_business_errors: service.sub_business_errors }, status: :unprocessable_entity
    end
  end

  def authorize
    service = AnswerService.new(authorize_params)

    service.authorize(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def reject
    service = AnswerService.new(reject_params)

    service.reject(params[:id], current_user)

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def revision
    service = AnswerService.new(revision_params)

    service.revision(params[:id])

    if service.success
      render json: { current_answer_id: service.record.content.current_answer_id }
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def validate
    data = {
      content_id: answer.content_id,
      answer_id: answer.id,
      business_id: answer.content.business_id,
      step_name: answer.step.name
    }

    troubleshooting = Troubleshooting.create!(thread_id: Thread.current.object_id, request_id: request.uuid, subdomain: Apartment::Tenant.current, start_at: Time.zone.now, url: request.original_url, external: false, data: data)

    service = AnswerService.new(fix_param_values(answer_params))

    service.validate(params[:id])

    if service.success
      troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: Time.zone.now - troubleshooting.start_at, response_code: 'ok')

      head :ok
    else
      troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: Time.zone.now - troubleshooting.start_at, response_code: 'error')

      render json: { errors: service.errors, sub_business_errors: service.sub_business_errors }, status: :unprocessable_entity
    end
  end

  def verify_token
    if Answer.find(params['id']).authorizer_token == params['token']
      head :ok
    else
      render json: { errors: [I18n.t('invalid_token', scope: 'errors.messages')] }, status: :bad_request
    end
  end

  def validate_dynamic_dependent
    params_request = validate_dynamic_dependent_params

    answer = Answer.find(params_request[:id])

    return nil unless answer.content.business.apply_field_rule

    answer.data['values'] = params_request[:field_values].to_h.except(:required_fields)
    render json: BusinessRulesService.new(answer, :dynamic, current_user, [params_request[:field_id]]).field_attributes.to_json
  end

  private

  def fix_param_values(params)
    params.transform_values do |value|
      value == [""] ? [] : value
    end
  end

  def parents_params
    params.permit(parents: {})
  end

  def skip_verification_url?
    @skip_verification_url = !answer.content.business.enable_verification_web_service? || !answer.available_at.present? || !StepPermission.edit.for_user(current_user).exists?(step_id: answer.step_id)
  end

  def answer_params
    params.permit(:content_id, :review_notes, :for_review, allowed_values, required_fields: []).merge('user_id' => current_user.id, 'remote_ip' => request.remote_ip)
  end

  def authorize_params
    params.permit(:content_id).merge('authorizer_id' => current_user.id, 'remote_ip' => request.remote_ip)
  end

  def reject_params
    params.permit(:content_id).merge('remote_ip' => request.remote_ip)
  end

  def revision_params
    params.permit(:step_id, :content_id, :notes).merge('user_id' => current_user.id, 'remote_ip' => request.remote_ip)
  end

  def validate_dynamic_dependent_params
    params.permit(:id, :field_id, field_values: {})
  end

  def allowed_values
    allowed_values_for_step(answer&.step)
  end

  def allowed_values_for_step(step)
    return [] unless step

    allowed_values = step&.templates&.map do |template|
      template.fields.kept.map do |field|
        if field.upload? || field.multiple? || field.multiple_reference?
          { "#{field['id'].to_sym}": [] }
        elsif field.sub_business?
          { "#{field['id'].to_sym}": allowed_values_for_step(field.reference_sub_business&.steps&.first) + %i[content_id _destroy] }
        elsif field.link?
          # Parse the JSON string if it is a string
          if params[field['id']].blank?
            params[field['id']] = nil
            field['id'].to_sym
          elsif params[field['id']].is_a?(String)
            params[field['id']] = JSON.parse(params[field['id']])
            { "#{field['id'].to_sym}": {} }
          else
            { "#{field['id'].to_sym}": {} }
          end
        else
          field['id'].to_sym
        end
      end
    end

    allowed_values << ['step_authorizer_email'] if step&.require_credentials?
    allowed_values.flatten
  end

  def answer
    @answer ||= Answer.where(content_id: params[:content_id]).find(params[:id])
  end

  def check_limited
    render json: { errors: [I18n.t('forbidden_to_view_record', scope: 'activerecord.errors.messages')] }, status: :forbidden if limited?
  end

  def check_permission
    head :forbidden unless StepPermission.for_user(current_user).exists?(step_id: answer.step_id)
  end

  def limited?
    return false if answer.content.created_by_id == current_user.id
    return true if current_user.limited?
    return false if current_user.departments.blank?

    departments = current_user.departments
    departments.all?(&:limited?) && current_user.departments.all? { |department| department.users.pluck(:id).none? { |id| answer.content.created_by_id == id } }
  end

  def skip_verification_url?
    !enable_verification_web_service? || !answer.available_at.present? || !step_permission_exists?
  end

  def enable_verification_web_service?
    answer.content.business.enable_verification_web_service?
  end

  def step_permission_exists?
    StepPermission.edit.for_user(current_user).exists?(step_id: answer.step_id)
  end
end
