module External
  class ContentsController < BaseController
    before_action :validate_api_key

    def index
      @business = Business.not_sub_business.find_by(id: params[:business_id])

      return head :not_found unless @business

      service = ExternalXmlBuilder.new(@business, params.merge('current_user' => current_user))

      filtered_contents_count = service.filtered_contents.count
      response.header['total-records'] = filtered_contents_count&.to_s
      response.header['pages'] = ((filtered_contents_count / service.per_page) + 1)&.to_s
      render xml: service.generate, status: :ok
    end
  end
end
