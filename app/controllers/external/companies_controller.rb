module External
  class CompaniesController < BaseController
    before_action :validate_admin_api_key

    def create
      service = CompanyService.new(company_params)

      service.create

      if service.success
        render json: { id: service.record.id }, status: :created
      else
        render json: { errors: service.errors }, status: :unprocessable_entity
      end
    end

    private

    def company_params
      params.permit(
        :name, :subdomain, :page_title, :use_elasticsearch, :chat_enabled, :bypass_approval, :expire_password_after_in_days, :favicon_image, :logo_image, :background_image, :internal_logo_image, :enable_google_oauth,
        :enable_microsoft_oauth, :disable_tips, :enable_open_id, :enable_internationalization, :enable_email_and_password_login, :enable_signup, :welcome_video_url,
        :default_department_id, :restrict_access_by_ip, :token_life_span_in_minutes, :limit_user_on_signup, :chatbot_enabled, :contact_us_enabled, :not_validate_auth_domain_openid,
        :custom_openid_name, :custom_openid_logo,
        allowed_ips: [], auth_domain: [], block_menus: [], open_id_config: %i[host response_type identifier secret]
      )
    end
  end
end
