class BusinessGroupsController < ApplicationController
  include DiscardedFilter
  include SkipTranslation

  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!, except: %i[index]
  before_action :authenticate_member!, only: %i[index]
  before_action :set_company_enable_internationalization, only: %i[index show]

  def index
    respond_to do |format|
      format.datatable do
        render json: BusinessGroupDatatable.new(params), status: :ok
      end
      format.json do
        searcher = BusinessGroupSearcher.new(search_params)
        @business_groups = searcher.search
        @business_groups = discarded_filter(@business_groups, params[:active]) 
      end
    end
  end

  def show
    @business_group = BusinessGroup.find(params[:id])
  end

  def create
    service = BusinessGroupService.new(business_group_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = BusinessGroupService.new(business_group_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = BusinessGroupService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = BusinessGroupService.new

    service.activate(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def search_params
    params.permit(:deleted).merge(current_user: current_user)
  end

  def business_group_params
    params.permit(:name, :description, :icon)
  end
end
