class CompaniesController < ApplicationController
  before_action :authenticate_administrator!, except: %i[index detail business_groups find_business content_columns update_data_replacement content_datatable get_name_by_subdomain]
  before_action :authenticate_member!, only: %i[index current]
  around_action :check_access_tenant, only: %i[business_groups find_business content_columns content_datatable update_data_replacement]
  before_action :validate_public_admin, only: %i[destroy update_data_replacement]
  before_action :set_company, only: %i[remove_attachment]

  include SkipTranslation

  def index
    return if current_administrator.blank?

    respond_to do |format|
      format.datatable do
        authorize Company
        @company_datatable = CompanyDatatable.new(params)
        render json: @company_datatable, status: :ok
      end
    end
  end

  def update_elasticsearch_index
    @record = Company.find(params[:id])

    Elasticsearch::BusinessesSetupWorker.perform_async(@record.subdomain)
  end

  def show
    @record = Company.find(params[:id])
  end

  def detail
    @record = Company.find_by(subdomain: params[:subdomain])
  end

  def current
    @record = Company.find_by(subdomain: params[:subdomain])
  end

  def get_name_by_subdomain
    @record = Company.find_by(subdomain: params[:subdomain])
    render json: { name: @record.name }, status: :created
  end

  def create
    authorize Company
    service = CompanyService.new(company_params)
    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    authorize Company
    service = CompanyService.new

    service.destroy(params[:id])

    if service.success?
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def update
    authorize Company
    service = CompanyService.new(company_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update_theme
    current_company = Company.current

    service = CompanyService.new(theme_params)

    service.update(current_company.id)

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def business_groups
    respond_to do |format|
      format.json do
        @business_groups = BusinessGroup.where(deleted_at: nil).order(:name).map do |group|
          valid_business = group.businesses_with_subs.where(deleted_at: nil).order(:name).joins(active_steps: :step_templates).distinct
          {
            id: group.id,
            name: group.name,
            children: valid_business.map { |business| { id: business.id, name: business.name } }
          }
        end

        render json: @business_groups
      end
    end
  end

  def find_business
    respond_to do |format|
      format.json do
        @business = Business.find(params[:business_id])

        render 'businesses/show'
      end
    end
  end

  def content_columns
    respond_to do |format|
      format.json do
        @records = params['business_id'] ? ShowOnListField.active.where(business_id: params['business_id']).order(:order) : []

        render 'show_on_list_fields/index'
      end
    end
  end

  def content_datatable
    respond_to do |format|
      format.datatable do
        render json: ContentDatatable.new(params, business_id: params['business_id'], only_actives: params['only_actives'], current_user: current_user), status: :ok
      end
    end
  end

  def update_data_replacement
    respond_to do |format|
      format.json do
        service = CompanyService.new(data_replacement_params)

        service.update_data_replacement

        if service.success?
          head :ok
        else
          render json: { errors: service.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update_allowed_ips
    current_company = Company.current

    service = CompanyService.new(allowed_ips_params)

    service.update(current_company.id)

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update_token_lifespan
    current_company = Company.current

    service = CompanyService.new(token_life_span_params)

    service.update(current_company.id)

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def check_access_tenant
    authenticate_administrator!

    super
  end

  def remove_attachment
    service = CompanyService.new
    service.remove_attachment(@company.id, params[:attachment_name])

    if service.success
      render json: { message: "#{params[:attachment_name].humanize} was successfully removed." }, status: :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  private

  def set_company
    @company = Company.find(params[:id])
  end

  def company_params
    params.permit(
      :smtp_address, :smtp_custom, :smtp_from, :smtp_password, :smtp_port, :smtp_starttls, :smtp_username,
      :name, :subdomain, :page_title, :use_elasticsearch, :chat_enabled, :bypass_approval, :expire_password_after_in_days, :favicon_image, :logo_image, :background_image, :internal_logo_image, :enable_google_oauth,
      :enable_microsoft_oauth, :disable_tips, :enable_open_id, :enable_internationalization, :enable_email_and_password_login, :enable_signup, :welcome_video_url,
      :default_department_id, :restrict_access_by_ip, :token_life_span_in_minutes, :limit_user_on_signup, :chatbot_enabled, :contact_us_enabled, :not_validate_auth_domain_openid,
      :custom_openid_name, :custom_openid_logo,
      allowed_ips: [], auth_domain: [], block_menus: [], open_id_config: %i[host response_type identifier secret]
    )
  end

  def theme_params
    params.permit(:theme_id)
  end

  def data_replacement_params
    params.permit(data_replacement: %i[text replacement])
  end

  def validate_public_admin
    return render json: { errors: I18n.t('forbidden', scope: 'activerecord.errors.messages') }, status: :unauthorized if Apartment::Tenant.current == 'public' && (!current_administrator.owner && current_administrator.id != params[:id])
  end

  def allowed_ips_params
    params.permit(allowed_ips: [])
  end

  def token_life_span_params
    params.permit(:token_life_span_in_minutes)
  end
end
