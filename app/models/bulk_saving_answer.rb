class BulkSavingAnswer < ApplicationRecord
  belongs_to :step, optional: true
  belongs_to :business, optional: true
  belongs_to :user, optional: false
  has_many :answer_processings, dependent: :destroy
  has_one :alteration_processing, dependent: :destroy

  validates :step_id, presence: true, if: proc { bulk_saving? }
  validates :business_id, presence: true, if: proc { bulk_alteration? }

  attribute :status, :integer
  enum :status, {
    created: 0,
    processing: 1,
    done: 2
  }

  attribute :origin, :integer
  enum :origin, {
    bulk_saving: 0,
    bulk_alteration: 1
  }
end
