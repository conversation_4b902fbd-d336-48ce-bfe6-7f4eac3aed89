class Search < ApplicationRecord
  belongs_to :user
  belongs_to :business
  has_and_belongs_to_many :fields
  has_many :widgets
  validates :title, presence: true, allow_blank: false

  before_destroy :check_for_widgets

  attribute :kind, :integer
  enum :kind, {
    simplified:    0,
    advanced:      1,
    raw:           2
  }

  private

  def check_for_widgets
    statistic_titles = Statistic.joins(:widgets).where(widgets: { search_id: id }).distinct.pluck(:title)

    if statistic_titles.present?
      errors.add(:base, I18n.t(statistic_titles.length > 1 ? 'many' : 'one', title: statistic_titles.join(', '), scope: 'activerecord.errors.messages.cant_delete_search'))
      throw :abort
    end
  end
end
