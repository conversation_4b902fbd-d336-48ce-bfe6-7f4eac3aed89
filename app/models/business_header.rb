class BusinessHeader < ApplicationRecord
  belongs_to :business, optional: false
  belongs_to :field, optional: false
  belongs_to :step, optional: false

  validate :maximum_field_count?
  validate :multiple_reference?, if: proc { field_id.present? }

  has_paper_trail ignore: %i[id created_at updated_at], on: %i[create update destroy]

  private

  def maximum_field_count?
    errors.add(:items_exceeded) if BusinessHeader.where(business_id: business_id).count > 4
  end

  def multiple_reference?
    errors.add(:field_id, :cannot_be_multiple) if field.multiple_reference?
  end
end
