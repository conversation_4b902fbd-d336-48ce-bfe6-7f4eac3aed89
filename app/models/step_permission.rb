class StepPermission < ApplicationRecord
  validate :user_or_department?

  has_paper_trail ignore: %i[id created_at updated_at], on: %i[create update destroy]

  belongs_to :step, optional: false
  belongs_to :user, optional: true
  belongs_to :department, optional: true

  scope :for_user, ->(user_id) { left_joins(:department).left_joins(department: :users).where('step_permissions.user_id = ? or users.id = ?', user_id, user_id) }

  attribute :scope, :integer
  enum :scope, {
    edit: 0,
    read: 1,
    approvement: 2
  }

  after_save :invalidate_cache

  private

  def user_or_department?
    errors.add(:user, :only_user_or_department) if user_id && department_id
    errors.add(:user, :at_least_user_or_department) unless user_id || department_id
  end

  def invalidate_cache
    Rails.cache.delete("user_#{user_id}_step_#{step_id}_permissions") if user_id.present?
    Rails.cache.delete("department_#{department_id}_step_#{step_id}_permissions") if department_id.present?

    true
  end
end
