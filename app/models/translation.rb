class Translation < ApplicationRecord
  belongs_to :actable, polymorphic: true

  validates :actable, :translated_text, :attribute_name, :language, presence: true, allow_blank: false

  attribute :devise_language, :string
  attribute :language, :integer

  enum :language, {
    portuguese:  0,
    english:     1,
    spanish:     2
  }

  enum :devise_language, {
    'pt-BR': 'portuguese',
    'en': 'english',
    'es': 'spanish'
  }

  validate :actable_uniqueness, on: :create

  private

  def actable_uniqueness
    translations = Translation.where(actable_id: actable_id, actable_type: actable_type, attribute_name: attribute_name, language: language)
    errors.add(:base, I18n.t('translation.actable_uniqueness', scope: 'activerecord.errors.models')) if translations.count.positive?
  end
end
