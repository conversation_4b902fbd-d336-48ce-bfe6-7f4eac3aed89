class AnswerProcessing < ApplicationRecord
  belongs_to :bulk_saving_answer
  belongs_to :answer, optional: true

  # Preloaded fields map to avoid repeated queries in sanitize_data
  attr_accessor :fields_by_id

  attribute :status, :integer
  enum :status, { created: 0, processing: 1, done: 2, failed: 3 }

  before_save :sanitize_data

  EMPTY_VALUE_KEY = '*empty*'.freeze

  def self.fields_map_for(step)
    business = step.business

    map = Field.for_step(step.id).index_by(&:id)
    map.merge!(business.key_fields.index_by(&:id))
    if business.sub_business?
      parent = business.parent
      map.merge!(parent.key_fields.index_by(&:id)) if parent
    end

    map
  end

  def self.sanitize_payload(payload, fields_by_id, preserve_blank_empty: false)
    sanitized = {}

    payload.each do |key, value|
      field_id = key.to_s
      field = fields_by_id[field_id]
      next if field.nil?

      if value == EMPTY_VALUE_KEY
        sanitized[field_id] = field.empty_value
        next
      end

      if value.blank?
        if preserve_blank_empty && value == field.empty_value
          sanitized[field_id] = field.empty_value
        end
        next
      end

      if (field.multiple? || field.multiple_reference? || field.upload?) && value.is_a?(String)
        data_array = JSON.parse(value) rescue nil
        parsed_array = data_array.is_a?(Array) ? data_array : nil
        next if parsed_array.nil?
        sanitized[field_id] = parsed_array
      else
        sanitized[field_id] = value
      end
    end

    sanitized
  end

  def sanitize_data
    # Skip if data hasn't changed (e.g., status-only updates in workers)
    return true unless will_save_change_to_data? || new_record?


    fields = fields_by_id || AnswerProcessing.fields_map_for(bulk_saving_answer.step)

    # When persisting again, preserve blanks that match the field's canonical empty value
    self.data = AnswerProcessing.sanitize_payload(data.dup, fields, preserve_blank_empty: !new_record?)

    true
  end

  def treated_data
    data.transform_values { |value| value == EMPTY_VALUE_KEY ? nil : value }
  end

  def editing_data
    values = data_with_verification_url_response || data

    values.transform_values { |value| value.is_a?(Array) ? value.to_s : value }
  end
end
