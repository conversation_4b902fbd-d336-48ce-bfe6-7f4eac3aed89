class ShowOnListField < ApplicationRecord
  include Orderable
  include TranslatedAttribute

  has_paper_trail ignore: %i[id created_at updated_at], on: %i[create update destroy]

  attr_accessor :skip_update_content_keywords

  belongs_to :field
  belongs_to :business
  belongs_to :step, optional: true

  scope :active_fields, -> { joins(:field).where(fields: { deleted_at: nil }) }
  scope :active_steps, -> { joins(:step).where(steps: { deleted_at: nil }) }
  scope :active, -> { active_fields.active_steps.select('show_on_list_fields.*, steps.name step_name, fields.label label, fields.type field_type, fields.options field_options') }
  scope :for_business, -> (business_id) { active.where(business_id: business_id).order(:order) }

  attribute :order_contents, :integer
  enum :order_contents, { asc: 0, desc: 1, none: 2 }, prefix: :order

  validates :field_id, uniqueness: { scope: %i[business_id step_id] }

  before_validation :set_order, on: :create

  after_update :rearrange_fields_order, if: proc { |template| template.saved_change_to_order? }
  after_destroy :rearrange_fields_order

  after_commit :update_content_keywords, on: %i[create destroy], unless: :skip_update_content_keywords

  def update_content_keywords
    Content.update_keywords("contents.id IN (SELECT cv.content_id FROM content_values cv WHERE field_id = '#{field_id}')")
  end

  def set_order
    last_order = ShowOnListField.where(business_id: business_id).ascending_order.pluck(:order).last

    self.order = last_order.nil? ? 0 : last_order + 1
  end

  def rearrange_fields_order
    scope = ShowOnListField.where(business_id: business_id).ascending_order

    if saved_change_to_order.present?
      old_value, new_value = saved_change_to_order

      scope = old_value.to_i > new_value.to_i ? scope.order('updated_at desc') : scope.order('updated_at asc')
    end

    scope.each_with_index do |record, index|
      record.update_attribute(:order, index)
    end
  end
end
