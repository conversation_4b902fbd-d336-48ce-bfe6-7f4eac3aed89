class Business < ApplicationRecord
  include SoftDeletable
  include TranslateEnum
  include Translatable
  include TranslatedAttribute
  include Normalizable
  
  has_paper_trail ignore: %i[updated_at created_at id], on: %i[create update destroy]

  belongs_to :business_group, optional: false

  has_many :contents, dependent: :destroy
  has_many :steps, -> { order('deleted_at NULLS FIRST').order(order: :asc) }, inverse_of: :business
  has_many :active_steps, -> { kept.order(order: :asc) }, class_name: 'Step', inverse_of: :business
  has_many :fields, through: :active_steps
  has_many :answers, through: :contents
  has_many :answer_versions, through: :answers, source: :versions
  has_many :dependent_field_rules, dependent: :destroy
  has_many :show_on_list_fields, dependent: :destroy

  # PS: Não ordenar na associação
  # rubocop:disable Rails/HasAndBelongsToMany
  has_and_belongs_to_many :key_fields, class_name: 'Field', join_table: 'businesses_fields'
  # rubocop:enable Rails/HasAndBelongsToMany
  validates :key_fields, length: { maximum: 5 }

  validates :name, presence: true, allow_blank: false
  validate :not_updatable_sub_business
  validates :webhook_url,
            allow_blank: true,
            format: { with: %r{\Ahttps:\/\/.*\z}, message: "deve começar com 'https://'" }
  
  normalize_squish :name
  
  scope :not_sub_business, -> { where(sub_business: false) }
  scope :sub_business, -> { where(sub_business: true) }
  scope :with_permission_for_user, ->(user) {
    joins(steps: :step_permissions)
      .left_joins(steps: { step_permissions: { department: :users } })
      .where(
        'step_permissions.user_id = ? OR users.id = ?',
        user.id, user.id
      )
      .distinct
  }

  after_update :restore_parent, if: proc { deleted_at.blank? && saved_change_to_deleted_at? }
  after_save :update_answer_index!

  after_discard :remove_show_on_list_fields, :remove_dependent_field_rules

  attribute :who_can_delete_contents, :integer
  enum :who_can_delete_contents, {
    only_coordinators: 0,
    everyone: 1,
    nobody: 2,
    only_creator: 3
  }

  def remove_show_on_list_fields
    show_on_list_fields.destroy_all
  end

  def remove_dependent_field_rules
    dependent_field_rules.discard_all
  end

  def created_by
    administrator = Administrator.where(id: versions.first&.whodunnit)
    return administrator&.first&.id if versions.first&.whodunnit && administrator.present?
  end

  def parent
    Field.kept.find_by(reference_sub_business_id: id)&.template&.steps&.first&.business
  end

  def parent_step
    Field.kept.find_by(reference_sub_business_id: id)&.template&.steps&.first
  end

  def has_key?
    key_fields.any?
  end

  def update_answer_index!
    first_step_id = active_steps&.first&.id
    return if first_step_id.blank? || key_field_ids.blank?

    index_fields = key_field_ids.map { |field_id| "(data->'values'->>'#{field_id}')" }
    index_name = get_index_name(index_fields)
    return if index_exists?(index_name)

    index_definition = get_index_definition(index_name, index_fields, first_step_id)
    puts "Index #{index_name} does not exists or needs to be updated. Creating now..."
    BusinessKeyFieldIndexWorker.perform_async(Apartment::Tenant.current, index_name, index_definition)
  end

  def get_index_definition(index_name, index_fields, first_step_id)
    <<~SQL
      CREATE INDEX CONCURRENTLY #{index_name} ON "#{Apartment::Tenant.current}".answers
        USING btree(#{index_fields.join(',')})
      WHERE step_id = '#{first_step_id}'
        and deleted_at IS NULL
    SQL
  end

  private

  def get_index_name(index_fields)
    "answers_#{id[0..7]}_#{id[24..]}_#{Digest::MD5.hexdigest(index_fields.join('_'))}"
  end

  def index_exists?(index_name)
    ActiveRecord::Base.connection.index_name_exists?(:answers, index_name)
  end

  after_discard do
    unless sub_business
      subbusiness_ids = Field.for_business(id).where(type: :sub_business).pluck(:reference_sub_business_id)
      Business.where(id: subbusiness_ids).discard_all
    end
  end

  after_undiscard do
    unless sub_business
      subbusiness_ids = Field.for_business(id).where(type: :sub_business).pluck(:reference_sub_business_id)
      Business.where(id: subbusiness_ids).undiscard_all
    end
  end

  def restore_parent
    parent&.update! deleted_at: nil
  end

  def not_updatable_sub_business
    errors.add(:sub_business, :cannot_be_updated) if id && sub_business != sub_business_was
  end
end
