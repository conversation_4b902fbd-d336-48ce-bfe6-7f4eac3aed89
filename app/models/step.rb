class Step < ApplicationRecord
  include SoftDeletable
  include Orderable
  include HasValidationUrl
  include HasVerificationUrl
  include Translatable
  include TranslatedAttribute

  has_paper_trail ignore: %i[id created_at updated_at], on: %i[create update destroy]

  default_scope -> { kept }

  belongs_to :business, optional: false
  belongs_to :step_for_revision, class_name: 'Step', optional: true, inverse_of: false

  has_many :step_templates, -> { order(order: :asc) }, dependent: :destroy, inverse_of: :step
  has_many :templates, through: :step_templates
  has_many :fields, through: :templates
  has_many :active_fields, through: :templates

  has_many :step_permissions, dependent: :destroy, inverse_of: :step
  has_many :dependent_reference_fields, dependent: :destroy, inverse_of: :step
  has_many :answers

  validates :name, presence: true, allow_blank: false
  validates :order, presence: true, allow_blank: false, numericality: { only_integer: true, greater_than_or_equal_to: 0 }

  validate :validate_step_revision, on: :update
  validate :only_steps_after, if: proc { steps_to_change_ids.present? && steps_to_change_ids.length.positive? }
  validate :not_self, if: proc { steps_to_change_ids.present? && steps_to_change_ids.length.positive? }
  validate :ensure_step_for_revision_ids_exists, if: proc { step_for_revision_ids.present? }

  before_validation :set_order, on: :create
  before_validation :sub_business_already_has_step, on: :create
  before_discard :last_active_step?
  after_discard :destroy_step_templates, :destroy_dependent_reference_fields, :remove_from_step_for_revision_ids
  #after_discard :destroy_dependent_field_rules
  after_update :rearrange_steps, if: proc { |step| step.saved_change_to_order? || step.saved_change_to_deleted_at? }
  after_update :update_content_keywords
  after_save :update_email_sending

  after_discard :delete_header_businesses

  SUBBUSINESS_LIMIT = 20

  scope :users_with_permission, ->(step_id) {
    step = Step.find(step_id)
    user_ids_direct = step.step_permissions.where.not(user_id: nil).pluck(:user_id)
    user_ids_from_departments = User.joins(:departments)
      .where(departments: { id: step.step_permissions.where.not(department_id: nil).pluck(:department_id) })
      .pluck(:id)

    User.kept.where(id: (user_ids_direct + user_ids_from_departments).uniq)
  }

  def user_emails_with_permission
    Step.users_with_permission(id).pluck(:email)
  end

  def delete_header_businesses
    BusinessHeader.where(step_id: id).destroy_all
  end

  def authorizer_users
    user_ids = User.joins(:departments).where(departments: { id: step_permissions.approvement.pluck(:department_id) }).pluck(:id)

    User.kept.where(id: step_permissions.approvement.pluck(:user_id) + user_ids).distinct
  end

  def first_of_business?
    Step.where(business_id: business_id).minimum(:order) == order
  end

  def default_field_values
    return {} unless business.fill_default_field_value

    default_values = active_fields.where.not(default_value: nil).select(:id, :default_value).map { |field| { field.id => field.default_value } }
    default_values.reduce({}, :merge)
  end

  def steps_to_change
    return [] if steps_to_change_ids.empty?

    Step.kept.where(id: steps_to_change_ids)
  end

  def steps_for_revision
    Step.where(id: step_for_revision_ids)
  end

  private

  def destroy_dependent_reference_fields
    DependentReferenceField.where("step_id = ? OR parent_step_id = ?", self.id, self.id).destroy_all
  end

  def destroy_step_templates
    step_templates.destroy_all
  end

  def last_active_step?
    return if business.active_steps.count != 1

    errors.add(:base, I18n.t('step.cannot_have_inactive_steps', scope: 'activerecord.errors.models'))

    throw :abort
  end

  def set_order
    self.order = business.active_steps.count if business_id.present?
  end

  def validate_step_revision
    current_step = Step.find_by(business_id: business_id, order: order_was)
    replaced_step = Step.find_by(business_id: business_id, order: order)

    errors.add(:base, I18n.t('step.error_moving', scope: 'activerecord.errors.models')) if order != order_was && current_step.present? && replaced_step.present? && (current_step.step_for_revision_ids.present? || replaced_step.step_for_revision_ids.present?) && ((current_step.steps_for_revision.present? && current_step.steps_for_revision.any? { |e| e.order.to_i >= order } ) || (replaced_step.steps_for_revision.present? && replaced_step.steps_for_revision.any? { |e| e.order.to_i >= order_was } ) )
  end

  def rearrange_steps
    old_value, new_value = saved_change_to_order

    if saved_change_to_deleted_at?
      steps = business.active_steps.where('steps.order > ?', order).where.not(id: id)

      steps.update_all('"order" = "order" - 1')
    elsif old_value > new_value
      steps = business.active_steps.where(order: new_value..old_value).where.not(id: id)

      steps.update_all('"order" = "order" + 1')
    else
      steps = business.active_steps.where(order: old_value..new_value).where.not(id: id)

      steps.update_all('"order" = "order" - 1')
    end

    steps = business.active_steps.where(steps: { order: order }).where.not(id: id)
    errors.add(:base, I18n.t('step.repeated_order', scope: 'activerecord.errors.models')) if steps.length.positive?
  end

  def update_content_keywords
    return unless saved_change_to_name

    Content.update_keywords("current_answer_id in ( select answers.id from answers where answers.step_id = '#{id}')")
  end

  def update_email_sending
    update(send_email_to_all_with_access: false, send_email_to_user_who_registered: false) if step_for_revision_ids.blank? && (send_email_to_all_with_access || send_email_to_user_who_registered)
  end

  def sub_business_already_has_step
    return unless business

    errors.add(:base, I18n.t('step.cannot_add_more_steps', scope: 'activerecord.errors.models')) if business.sub_business? && business.steps.exists?
  end

  def only_steps_after
    errors.add(:base, I18n.t('step.only_steps_after', scope: 'activerecord.errors.models')) if steps_to_change.pluck(:order).any? { |order| order < self.order }
  end

  def not_self
    errors.add(:base, I18n.t('step.cannot_add_not_self', scope: 'activerecord.errors.models')) if steps_to_change_ids.include?(id)
  end

  def remove_from_step_for_revision_ids
    sql = ActiveRecord::Base.sanitize_sql("step_for_revision_ids = ARRAY_REMOVE(step_for_revision_ids, '#{id}')")

    Step.unscoped.where('? = ANY(step_for_revision_ids)', id).update_all(sql)
  end

  def ensure_step_for_revision_ids_exists
    inexistent_ids = step_for_revision_ids - Step.where(id: step_for_revision_ids).pluck(:id)

    errors.add(:base, I18n.t('not_valid', scope: 'activerecord.errors.models.step.attributes.step_for_revision_ids', ids: inexistent_ids.join(', '))) if inexistent_ids.present?
  end
end
