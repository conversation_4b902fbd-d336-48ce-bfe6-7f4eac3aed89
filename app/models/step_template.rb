class StepTemplate < ApplicationRecord
  include Orderable

  has_paper_trail ignore: %i[id created_at updated_at], on: %i[create update destroy]

  belongs_to :step, optional: false
  belongs_to :template, optional: false

  before_validation :set_order, on: :create

  validates :template_id, uniqueness: { scope: :step_id }

  after_update :rearrange_templates, if: proc { |template| template.saved_change_to_order? }
  after_destroy :rearrange_templates_order
  after_destroy :create_elasticsearch_mapping

  after_save :create_elasticsearch_mapping, if: proc { |template| template.saved_change_to_template_id? }

  validate :not_repeated_subbusiness?, :exceed_subbusiness_limit?

  private

  def set_order
    last_order = StepTemplate.where(step_id: step_id).ascending_order.pluck(:order).last

    self.order = last_order.nil? ? 0 : last_order + 1
  end

  def rearrange_templates
    old_value, new_value = saved_change_to_order

    if old_value > new_value
      step_templates = StepTemplate.where(step_id: step_id).where(order: new_value..old_value).where.not(id: id)

      step_templates.update_all('"order" = "order" + 1')
    else
      step_templates = StepTemplate.where(step_id: step_id).where(order: old_value..new_value).where.not(id: id)

      step_templates.update_all('"order" = "order" - 1')
    end
  end

  def rearrange_templates_order
    order = 0

    StepTemplate.where(step_id: step_id).ascending_order.each do |step_template|
      step_template.update_attribute(:order, order)
      order += 1
    end
  end

  def create_elasticsearch_mapping
    return unless step.business.integrate_elastic?
    Elasticsearch::BusinessSetupWorker.perform_in(Time.zone.tomorrow.midnight - Time.current, Apartment::Tenant.current, step.business_id)
  end

  def not_repeated_subbusiness?
    return if template.blank?

    errors.add(:base, I18n.t('step_template.repeated_subbusiness', scope: 'activerecord.errors.models')) if template.fields.kept.sub_business.exists? && StepTemplate.where(template_id: template_id).where.not(step_id: step_id).exists?
  end

  def exceed_subbusiness_limit?
    return if template.blank? || step.blank?

    existing_sub_fields = step.fields.select(:id).where(type: :sub_business).kept.count
    new_sub_fields = template.fields.select(:id).where(type: :sub_business).kept.count

    errors.add(:base, I18n.t('field.max_subbusiness_reached', scope: 'activerecord.errors.models')) if (existing_sub_fields + new_sub_fields) > Step::SUBBUSINESS_LIMIT
  end
end
