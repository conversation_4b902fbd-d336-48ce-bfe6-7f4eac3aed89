class Notification < ApplicationRecord
  include Discard::Model
  belongs_to :user

  scope :without_destination, -> { where("destiny_users = '{}' AND destiny_departments = '{}'") }
  scope :for_user, ->(user_id) { where("? = ANY(destiny_users)", user_id) }
  scope :for_department, ->(departments) { where('destiny_departments @> ARRAY[?]::uuid[]', departments) }
  scope :not_dismissed, ->(user_id) { where.not('? = ANY(dismissed_by)', user_id) }

  validates :message, presence: true, allow_blank: false
  validates :title, presence: true, allow_blank: false
end
