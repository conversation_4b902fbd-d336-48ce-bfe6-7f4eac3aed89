class FieldValidation < ApplicationRecord
  include TranslateEnum

  has_paper_trail ignore: %i[id created_at updated_at], on: %i[create update destroy]

  SUPPORTED_TYPES = %i[text text_area upload sub_business multiple].freeze

  self.inheritance_column = :_type_disabled

  belongs_to :field, optional: false

  attribute :type, :integer
  enum :type, {
    number_of_characters: 0,
    content: 1,
    extension: 2,
    file_size: 3,
    regex: 4,
    number_of_records: 5
  }
  translate_enum :type

  attribute :operator, :integer
  enum :operator, {
    less_than: 0,
    equal_to: 1,
    greater_than: 2,
    contains: 3,
    not_contains: 4,
    match: 5,
    not_match: 6
  }, suffix: true
  translate_enum :operator

  validates :type, presence: true
  validates :operator, uniqueness: { scope: %i[type field_id] }, presence: true
  validates :data, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }, if: :number_of_characters?
  validates :data, presence: true, numericality: { greater_than: 0 }, if: :file_size?
  validates :data, presence: true, if: proc { content? || extension? || regex? }
  validates :error_message, presence: true

  validate :correct_field_type?, if: proc { field.present? }
  validate :correct_operators?, if: proc { type.present? && operator.present? }
  validate :upload_field?, if: proc { type.present? && field.present? }
  validate :regex_field?, if: proc { type.present? && field.present? }
  validate :valid_regex?, if: proc { regex? && data.present? }

  private

  def correct_operators?
    errors.add(:operator, I18n.t('field_validation.attributes.operator.number_of_characters', scope: 'activerecord.errors.models')) if number_of_characters? && (contains_operator? || not_contains_operator? || match_operator? || not_match_operator?)
    errors.add(:operator, I18n.t('field_validation.attributes.operator.content', scope: 'activerecord.errors.models')) if content? && (less_than_operator? || equal_to_operator? || greater_than_operator? || match_operator? || not_match_operator?)
    errors.add(:operator, I18n.t('field_validation.attributes.operator.extension', scope: 'activerecord.errors.models')) if extension? && (less_than_operator? || equal_to_operator? || greater_than_operator? || match_operator? || not_match_operator?)
    errors.add(:operator, I18n.t('field_validation.attributes.operator.file_size', scope: 'activerecord.errors.models')) if file_size? && (contains_operator? || not_contains_operator? || match_operator? || not_match_operator?)
    errors.add(:operator, I18n.t('field_validation.attributes.operator.regex', scope: 'activerecord.errors.models')) if regex? && (less_than_operator? || equal_to_operator? || greater_than_operator? || contains_operator? || not_contains_operator?)
  end

  def correct_field_type?
    errors.add(:field_id, I18n.t('field_validation.attributes.field_id.no_validations_allowed', scope: 'activerecord.errors.models')) unless SUPPORTED_TYPES.any? { |type| field.send("#{type}?") }
  end

  def upload_field?
    errors.add(:type, I18n.t('field_validation.attributes.type.unallowed_validation_for_upload', scope: 'activerecord.errors.models')) if !field.upload? && (extension? || file_size?)
  end

  def regex_field?
    errors.add(:type, I18n.t('field_validation.attributes.type.unallowed_validation_for_text', scope: 'activerecord.errors.models')) if !(field.text? || field.text_area? || field.multiple?) && regex?
  end

  def valid_regex?
    errors.add(:data, I18n.t('field_validation.attributes.data.valid_regex', data: data, scope: 'activerecord.errors.models')) if data.to_regexp.blank?
  end
end
