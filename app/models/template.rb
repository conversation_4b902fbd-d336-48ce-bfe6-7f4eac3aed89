class Template < ApplicationRecord
  include SoftDeletable
  include Translatable
  include TranslatedAttribute
  include Normalizable

  has_paper_trail ignore: %i[updated_at created_at id], on: %i[create update destroy]

  before_destroy :in_use?
  before_discard :in_use?

  validates :name, presence: true, allow_blank: false

  normalize_squish :name, :description, :variable

  has_many :fields, -> { order('fields.deleted_at NULLS FIRST').order(order: :asc, updated_at: :desc) }, inverse_of: :template

  has_many :active_fields, -> { kept.order(order: :asc, updated_at: :desc) }, class_name: 'Field', inverse_of: :template
  has_many :deleted_fields, -> { discarded.order(order: :asc, updated_at: :desc) }, class_name: 'Field', inverse_of: :template

  has_many :step_templates, dependent: :destroy
  has_many :steps, through: :step_templates
  has_many :businesses, through: :steps

  # rubocop:disable Naming/PredicateName
  def has_fields?
    !active_fields.count.zero?
  end
  # rubocop:enable Naming/PredicateName

  private

  def in_use?
    return unless step_templates.count.positive?

    errors.add(:base, I18n.t('template.already_taken', business_name: steps.map(&:business).pluck(:name).join(', '), scope: 'activerecord.errors.models'))

    throw :abort
  end
end
