class DependentReferenceField < ApplicationRecord
  has_paper_trail ignore: %i[id created_at updated_at], on: %i[create update destroy]

  belongs_to :parent_step, optional: false, class_name: 'Step', inverse_of: false
  belongs_to :step, optional: false, inverse_of: false
  belongs_to :parent_field, optional: false, class_name: 'Field', inverse_of: false
  belongs_to :field, optional: false,  inverse_of: false

  validates :field_id, presence: true, allow_blank: false, uniqueness: { scope: %i[step] }
  validate :valid_parent_field_reference_field?

  def valid_parent_field_reference_field?

    return if step.blank? || field.blank? || parent_field.blank? || parent_step.blank?

    return errors.add(:field, I18n.t('dependent_reference_field.field.must_be_reference', field_label: field.label, scope: 'activerecord.errors.models')) unless field.reference? || field.multiple_reference?
    return errors.add(:parent_field, I18n.t('dependent_reference_field.parent_field.must_be_reference', field_label: parent_field.label, scope: 'activerecord.errors.models')) unless parent_field.reference? || parent_field.multiple_reference?


    not_deleteds = field.reference_business.fields.select { |f| f.deleted_at.nil? }
    return if not_deleteds.any? { |f| f.reference_value_field_id == parent_field.reference_value_field_id }

    errors.add(:parent_field, I18n.t('dependent_reference_field.parent_field.invalid_parent_field_reference_field', field_reference_business_name: field.reference_business.name, parent_field_reference_business_name: parent_field.reference_business.name, parent_field_reference_value_field_label: parent_field.reference_value_field.label, scope: 'activerecord.errors.models'))
  end
end
