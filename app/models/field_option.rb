class FieldOption < ApplicationRecord
  belongs_to :field
  belongs_to :content

  validates :label, :order, :value, presence: true, allow_blank: false

  scope :for_dependent_parent_field, ->(target_field, parent_field, parent_value) do
    target_field_filter_id = nil

    if parent_field.reference_value_use_key_fields?
      target_field_filter_id = target_field.reference_business.fields.where('fields.reference_business_id = ?', parent_field.reference_business_id).pick(:id)
    else
      target_field_filter_id = target_field.reference_business.fields.where('fields.reference_value_field_id = ? and fields.reference_business_id = ?', parent_field.reference_value_field_id, parent_field.reference_business_id).pick(:id)
    end

    return self if target_field_filter_id.blank?
    
    if Field.find(target_field_filter_id).multiple_reference?
      joins("INNER JOIN content_values content_value_for_parent_field on (
        content_value_for_parent_field.content_id = field_options.content_id
        AND content_value_for_parent_field.field_id = '#{target_field_filter_id}'
      )").where("EXISTS (SELECT 1 FROM unnest(ARRAY[?]) AS term WHERE content_value_for_parent_field.value ~ ('(^|[\"])' || term || '($|[\"])'))", Array.wrap(parent_value))
    else
      joins("INNER JOIN content_values content_value_for_parent_field on (
        content_value_for_parent_field.content_id = field_options.content_id
        AND content_value_for_parent_field.field_id = '#{target_field_filter_id}'
      )").where('content_value_for_parent_field.value': parent_value)
    end
  end

  scope :with_active_contents, -> { joins(:content).where(contents: { deleted_at: nil }) }
end
