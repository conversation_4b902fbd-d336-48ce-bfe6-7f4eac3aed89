class Widget < ApplicationRecord
  include Orderable

  belongs_to :statistic, optional: false
  belongs_to :search, optional: false
  belongs_to :category_field, class_name: 'Field', inverse_of: false, optional: true
  belongs_to :value_field, class_name: 'Field', inverse_of: false, optional: true

  validates :category_field_id, presence: true, if: proc { (pie? || bar? || column? || line?) && !search_has_aggs? }
  validates :value_field_id, presence: true, if: proc { (pie? || bar? || column? || line? || number?) && !search_has_aggs? }

  validates :category_agg, presence: true, inclusion: { in: :get_all_keys }, if: proc { (pie? || bar? || column? || line?) && search_has_aggs? }
  validates :value_agg, presence: true, inclusion: { in: :get_all_keys }, if: proc { (pie? || bar? || column? || line? || number?) && search_has_aggs? }

  before_validation :set_order, on: :create
  before_save :set_attributes

  after_update :rearrange_widgets, if: proc { |widget| widget.saved_change_to_order? }
  after_destroy :rearrange_widgets

  attribute :size, :integer
  enum :size, {
    small: 0,
    medium: 1,
    large: 2
  }

  attribute :chart_type, :integer
  enum :chart_type, {
    pie: 0,
    bar: 1,
    column: 2,
    line: 3,
    number: 4,
    embedded: 5
  }

  private

  def search_has_aggs?
    parsed_query = JSON.parse(self.search.query) rescue {}
    parsed_query.keys.include?('aggs') || parsed_query.keys.include?('aggregations')
  end

  def set_attributes
    if search_has_aggs?
      self.category_field_id = nil
      self.value_field_id = nil
    else
      self.category_agg = nil
      self.value_agg = nil
    end
  end

  def get_all_keys(hash=JSON.parse(self.search.query))
    keys = []
    hash.each do |key, value|
      if value.is_a?(Hash)
        keys += [key] + get_all_keys(value)
      else
        keys << key
      end
    end
    keys - ElasticSearcherService::META_KEYS
  end

  def set_order
    self.order = statistic.widgets.count if statistic.present?
  end

  def rearrange_widgets
    scope = Widget.where(statistic_id: statistic_id).ascending_order

    if saved_change_to_order.present?
      old_value, new_value = saved_change_to_order

      scope = old_value.to_i > new_value.to_i ? scope.order('updated_at desc') : scope.order('updated_at asc')
    end

    scope.each_with_index do |record, index|
      record.update_attribute(:order, index)
    end
  end
end
