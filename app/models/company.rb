class Company < ApplicationRecord
  OPEN_ID_CONFIG_SCHEMA = Rails.root.join('config', 'schemas', 'companies', 'open_id_config.json_schema').to_s.freeze

  store_accessor :open_id_config, :host, :response_type, :identifier, :secret, prefix: true

  belongs_to :theme, optional: true

  validates :name, :subdomain, presence: true, uniqueness: { case_sensitive: false }, allow_blank: false
  validates :auth_domain, presence: true, allow_blank: false, if: proc { enable_google_oauth? || enable_microsoft_oauth? || enable_open_id? }
  validates :default_department_id, presence: true, if: :bypass_approval?
  validates :token_life_span_in_minutes, numericality: { only_integer: true, greater_than_or_equal_to: 5 }, presence: true, allow_blank: false
  validates :open_id_config, presence: true, allow_blank: false, if: :enable_open_id?
  validate :validate_open_id_config_json_schema, if: :enable_open_id?
  validate :validate_block_menus, if: proc { block_menus.present? }

  validates_with I<PERSON><PERSON><PERSON>da<PERSON>, if: :restrict_access_by_ip?

  has_one_attached :logo_image
  has_one_attached :background_image
  has_one_attached :internal_logo_image
  has_one_attached :favicon_image
  has_one_attached :custom_openid_logo
  has_many :bulk_destroying_contents, dependent: :destroy

  before_save :check_auth_domain
  before_save :delete_allowed_ips, unless: :restrict_access_by_ip?
  before_save :remove_open_id_config, unless: :enable_open_id?
  before_create :generate_api_key
  after_create :create_tenant
  after_destroy :drop_tenant
  after_save :update_allowed_ips_on_waf, if: proc { saved_change_to_restrict_access_by_ip? || saved_change_to_allowed_ips? }
  after_save_commit :create_twilio_subaccount, if: proc { saved_change_to_chat_enabled? && chat_enabled? && twilio_sid.blank? && twilio_auth_token.blank? }
  after_save_commit :remove_twilio_credentials, if: proc { saved_change_to_chat_enabled? && !chat_enabled? }

  def smtp_settings
    {
      address:              smtp_address,
      port:                 smtp_port.to_i,
      user_name:            smtp_username,
      password:             smtp_password,
      domain:               smtp_username&.match(/(?<=@)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/).to_s,
      authentication:       :plain,
      enable_starttls_auto: true,
      openssl_verify_mode:  "none",
      open_timeout:         5,
      read_timeout:         5
    }
  end

  def expire_password_after
    expire_password_after_in_days.days
  end

  def data_replacements
    records = []

    Apartment::Tenant.switch subdomain do
      records = DataReplacement.all.to_a
    end

    records
  end

  def self.current
    Company.find_by(subdomain: Apartment::Tenant.current)
  end

  def embed_welcome_video
    parse_video_url(welcome_video_url) if welcome_video_url.present?
  end

  def waf_ip_set_arn
    return if waf_ip_set_id.blank?

    "arn:aws:wafv2:#{Rails.application.credentials.aws_region}:#{Rails.application.credentials.aws_account_id}:global/ipset/#{subdomain}-#{Rails.env}/#{waf_ip_set_id}"
  end

  def waf_rule_group_arn
    return if waf_rule_group_id.blank?

    "arn:aws:wafv2:#{Rails.application.credentials.aws_region}:#{Rails.application.credentials.aws_account_id}:global/rulegroup/#{subdomain}-#{Rails.env}/#{waf_rule_group_id}"
  end

  def allowed_ips_cidr
    return [] if allowed_ips.compact.blank?

    allowed_ips.compact.map { |ip| NetAddr::IPv4Net.parse(ip).to_s }
  end

  private

  def validate_block_menus
    valid_menus = %w[dashboard search favorites bulk_saving_answers answer_versions report statistics]

    errors.add(:base, I18n.t('company.attributes.block_menus.unpermitted_value', scope: 'activerecord.errors.models')) if (block_menus - valid_menus).any?
  end

  def delete_allowed_ips
    self.allowed_ips = []
  end

  def remove_open_id_config
    self.open_id_config = nil
  end

  def create_tenant
    Apartment::Tenant.create(subdomain)
  end

  def drop_tenant
    Apartment::Tenant.drop(subdomain)
  end

  def generate_api_key
    self.api_key = SecureRandom.hex(64)
  end

  def check_auth_domain
    self.auth_domain = [] unless enable_google_oauth? || enable_microsoft_oauth? || enable_open_id?
  end

  def parse_video_url(url)
    youtube_formats = [
      %r{https?://youtu\.be/(.+)},
      %r{https?://www\.youtube\.com/watch\?v=(.*?)(&|#|$)},
      %r{https?://www\.youtube\.com/embed/(.*?)(\?|$)},
      %r{https?://www\.youtube\.com/v/(.*?)(#|\?|$)},
      %r{https?://www\.youtube\.com/user/.*?#\w/\w/\w/\w/(.+)\b}
    ]

    vimeo_formats = [%r{https?://vimeo.com/(\d+)}, %r{https?://(www\.)?vimeo.com/(\d+)}]

    url.strip!

    if url.include? 'youtu'
      youtube_formats.find { |format| url =~ format } && Regexp.last_match(1)

      "https://www.youtube.com/embed/#{Regexp.last_match(1)}"
    elsif @url.include? 'vimeo'
      vimeo_formats.find { |format| url =~ format } && Regexp.last_match(1)

      "https://player.vimeo.com/video/#{Regexp.last_match(1)}"
    end
  end

  def create_twilio_subaccount
    service = Twilio::SubaccountService.new

    service.create(name)

    return unless service.success?

    update_columns(twilio_sid: service.record.sid, twilio_auth_token: service.record.auth_token)

    service = Twilio::ConfigurationService.new(service.record.sid, service.record.auth_token)

    if twilio_api_key.blank? || twilio_api_secret.blank?
      service.create_key(name)

      update_columns(twilio_api_key: service.record.sid, twilio_api_secret: service.record.secret) if service.success?
    end

    service.fetch

    return unless service.success?

    update_columns(twilio_chat_service_sid: service.record.default_chat_service_sid)

    service.enable_reachability_indicator(service.record.default_chat_service_sid)
  end

  def remove_twilio_credentials
    update_columns(twilio_sid: nil, twilio_auth_token: nil, twilio_chat_service_sid: nil)
  end

  def update_allowed_ips_on_waf
    Aws::WafService.new(self).update_allowed_ips
  end

  def validate_open_id_config_json_schema
    return unless open_id_config.present?

    begin
      unless JSON::Validator.validate(OPEN_ID_CONFIG_SCHEMA, open_id_config)
        errors.add(:open_id_config, I18n.t('company.invalid_open_id_config', scope: 'activerecord.errors.models'))
      end
    rescue JSON::Schema::ValidationError => e
      errors.add(:open_id_config, I18n.t('company.invalid_open_id_config', scope: 'activerecord.errors.models'))
    end
  end
end
