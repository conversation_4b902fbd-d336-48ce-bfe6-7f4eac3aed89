class AlterationProcessing < ApplicationRecord
  include HasValidationUrl
  include HasVerificationUrl

  belongs_to :bulk_saving_answer

  validates :criterions, presence: true
  validates :alterations, presence: true, if: :updating?
  validates :verification_url, presence: true, if: :form_enrichment?
  validates :total_alterations, numericality: { allow_nil: false, greater_than: 0 }
  before_validation :fill_total_alterations, on: :create

  attribute :status, :integer
  enum :status, {
    created: 0,
    processing: 1,
    done: 2,
    failed: 3
  }

  attribute :bulk_action, :integer
  enum :bulk_action, {
    updating: 0,
    inactivate: 1,
    approve: 2,
    form_enrichment: 3
  }

  def fill_total_alterations
    self.total_alterations = BulkAnswerService.new.filter_by_criterions(bulk_saving_answer.business_id, criterions).count if criterions.present?
  end

  def self.increment_error(alteration_processing_id, content_id, array_of_errors)
    processing_error = "{\"content_id\": \"#{content_id}\", \"errors\": #{array_of_errors}}"

    sql = <<-SQL
      UPDATE alteration_processings
      SET processing_errors = processing_errors || ?::jsonb
      WHERE id = ?
    SQL

    ActiveRecord::Base.connection.execute(
      ActiveRecord::Base.send(:sanitize_sql_array, [sql, processing_error, alteration_processing_id])
    )
  end

  def self.increment_successes(alteration_processing_id)
    AlterationProcessing.where(id: alteration_processing_id).update_all('"successes" = "successes" + 1')
  end
end
