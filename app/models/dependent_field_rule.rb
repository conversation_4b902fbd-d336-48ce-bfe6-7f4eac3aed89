class DependentFieldRule < ApplicationRecord
  include SoftDeletable
  include TranslateEnum

  RULES_SCHEMA = Rails.root.join('config', 'schemas', 'rules', 'rules.json_schema').to_s.freeze
  RULE_ACTIONS_SCHEMA = Rails.root.join('config', 'schemas', 'rules', 'rule_actions.json_schema').to_s.freeze
  RESTRICTIONS_SCHEMA = Rails.root.join('config', 'schemas', 'rules', 'restrictions.json_schema').to_s.freeze

  has_paper_trail ignore: %i[id business_id created_at updated_at], on: %i[create update destroy]
  default_scope -> { kept }

  belongs_to :business
  belongs_to :step, optional: true, inverse_of: false
  belongs_to :field, optional: true, inverse_of: false

  validates :rule_type, presence: true, allow_blank: false
  # validate :parent_field_from_same_step?, if: :dynamic_dependent?
  # validate :parent_field_type_valid?, :field_type_valid?

  validates :description, length: { in: 0..50 }
  validates :rules, presence: true, allow_blank: false
  validate :validate_rules_json_schema
  validates :rule_actions, presence: true, allow_blank: false
  validate :validate_rule_actions_json_schema
  validate :validate_restrictions_json_schema

  before_validation :create_rules_overview

  scope :with_rule_key, ->(key) { where('CAST(rules as varchar) LIKE ?', "%#{key}%") }
  scope :with_rules_by_keys, ->(keys) {
    return all if keys.blank?

    conditions = []

    keys.length.times do
      conditions << 'CAST(rules as varchar) LIKE ?'
    end

    params = keys.map { |key| "%#{key}%" }

    where(conditions.join(' OR '), *params)
  }

  scope :with_rule_actions_by_keys, ->(keys) {
    return all if keys.blank?

    conditions = []

    keys.length.times do
      conditions << 'CAST(rule_actions as varchar) LIKE ?'
    end

    params = keys.map { |key| "%#{key}%" }

    where(conditions.join(' OR '), *params)
  }

  scope :with_access_for_user_and_departments, ->(user_id, department_ids) {
    conditions = []

    conditions << jsonb_path_condition('User', user_id) if user_id.present?
    conditions << jsonb_path_condition('Department', department_ids) if department_ids.present?

    return all if conditions.empty?

    where(conditions.reduce(:or))
  }

  scope :without_rule_key, ->(key) { where('CAST(rules as varchar) NOT LIKE ?', "%#{key}%") }
  scope :with_action_key, ->(key) { where("EXISTS(SELECT * FROM jsonb_array_elements(rule_actions) AS elem WHERE elem->>'field' LIKE ?)", "%#{key}%") }
  scope :with_action_field_prop, ->(step_id, field_id, field_prop, value = 'true') {
    where("EXISTS(
           SELECT * FROM jsonb_array_elements(rule_actions) AS elem
           WHERE elem->>'field' LIKE ?
           AND elem->> ? LIKE ?)",
          "%#{step_id}:#{field_id}",
          field_prop,
          value)
  }

  # TODO: remove dynamic_dependent once all rules are migrated
  attribute :rule_type, :integer
  enum :rule_type, {
    dependent: 0,
    validation: 1,
    dynamic_dependent: 2,
    access_control: 3
  }
  translate_enum :rule_type

  attribute :condition_operator, :integer
  enum :condition_operator, {
    equals: 0,
    lower: 1,
    greater: 2,
    regex_match: 3,
    regex_not_match: 4,
    contains: 5,
    not_contains: 6,
    not_equal: 7
  }, prefix: :condition
  translate_enum :condition_operator

  attribute :field_operator, :integer
  enum :field_operator, {
    equals: 0,
    lower: 1,
    greater: 2,
    regex_match: 3,
    regex_not_match: 4,
    contains: 5,
    not_contains: 6,
    not_equal: 7
  }, prefix: :field
  translate_enum :field_operator

  def self.jsonb_path_condition(target_class, target_ids)
    table = DependentFieldRule.arel_table
    rule_actions = table[:rule_actions]
    if target_ids.is_a?(Array)
      condition = target_ids.map { |id| "@.target.id == \"#{id}\"" }.join(' || ')
      jsonb_path = "$[*] ? (@.target.class == \"#{target_class}\" && (#{condition}))"
    else
      target_id = target_ids
      jsonb_path = "$[*] ? (@.target.class == \"#{target_class}\" && @.target.id == \"#{target_id}\")"
    end

    Arel::Nodes::NamedFunction.new("jsonb_path_exists", [rule_actions, Arel::Nodes.build_quoted(jsonb_path)])
  end

  private

  def create_rules_overview
    self.rules = process_rules_overview(rules)
  end

  def process_rules_overview(rules, keys = ('A'..'Z').to_a, overview = '', condition = '')
    if rules.is_a?(Hash)
      overview << '('
      hash_rules = rules
      hash_rules['rules'] = process_rules_overview(rules['rules'], keys, overview, rules['condition'])
      overview << ')'
    end

    if rules.is_a?(Array)
      array_rules = rules.map.with_index do |rule, index|
        overview << keys.first unless rule.key?('rules')
        rule = rule.key?('rules') ? process_rules_overview(rule, keys, overview) : rule.merge('overview_index' => keys.shift)
        overview << " #{condition == 'and' ? '&&' : '||'} " unless index + 1 == rules.length
        rule
      end
    end

    return rules['rules'].first['overview_index'] == 'A' ? hash_rules.merge('overview' => overview) : hash_rules if rules.is_a?(Hash)

    array_rules if rules.is_a?(Array)
  end

  # def parent_field_from_a_before_step?
  #   return if step.blank? || field.blank? || parent_field.blank? || parent_step.blank?

  #   return errors.add(:base, I18n.t('dependent_field_rule.must_belong_to_a_step_before_field', field_label: field.label, step_order: step.order, step_name: step.name, parent_field_label: parent_field.label, parent_step_order: parent_step.order, parent_step_name: parent_step.name, scope: 'activerecord.errors.models')) unless parent_step.order < step.order
  # end

  # def parent_field_from_same_step?
  #   return if step.blank? || field.blank? || parent_field.blank? || parent_step.blank?

  #   return errors.add(:base, I18n.t('dependent_field_rule.must_belong_to_a_same_step_field', field_label: field.label, step_order: step.order, step_name: step.name, parent_field_label: parent_field.label, parent_step_order: parent_step.order, parent_step_name: parent_step.name, scope: 'activerecord.errors.models')) unless parent_step.order == step.order
  # end

  # def parent_field_type_valid?
  #   return if parent_field.blank?
  #   return errors.add(:base, I18n.t('dependent_field_rule.cannot_be_multiple', scope: 'activerecord.errors.models')) if %i[multiple link sub_business multiple_reference].any? { |invalid_type| parent_field.type.to_sym == invalid_type }
  # end

  # def field_type_valid?
  #   return if field.blank?
  #   return errors.add(:base, I18n.t('dependent_field_rule.cannot_be_updated', scope: 'activerecord.errors.models')) if %i[multiple link sub_business multiple_reference].any? { |invalid_type| field.type.to_sym == invalid_type }
  # end

  def validate_rules_json_schema
    return if rules.blank?

    begin
      errors.add(:rules, 'is not valid according to the JSON schema') unless JSON::Validator.validate(RULES_SCHEMA, rules)
    rescue JSON::Schema::ValidationError => e
      errors.add(:rules, "is not valid according to the JSON schema: #{e.message}")
    end
  end

  def validate_rule_actions_json_schema
    return if rule_actions.blank?

    begin
      errors.add(:rule_actions, 'is not valid according to the JSON schema') unless JSON::Validator.validate(RULE_ACTIONS_SCHEMA, rule_actions)
    rescue JSON::Schema::ValidationError => e
      errors.add(:rule_actions, "is not valid according to the JSON schema: #{e.message}")
    end
  end

  def validate_restrictions_json_schema
    return if restrictions.empty?

    begin
      errors.add(:restrictions, 'is not valid according to the JSON schema') unless JSON::Validator.validate(RESTRICTIONS_SCHEMA, restrictions)
    rescue JSON::Schema::ValidationError => e
      errors.add(:restrictions, "is not valid according to the JSON schema: #{e.message}")
    end
  end
end
