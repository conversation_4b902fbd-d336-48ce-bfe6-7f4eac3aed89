require 'digest'
require 'securerandom'
class User < ApplicationRecord
  has_paper_trail ignore: %i[
    confirmation_sent_at
    confirmation_token
    current_sign_in_at
    confirmation_token
    current_sign_in_at
    current_sign_in_ip
    failed_attempts
    last_active_at
    last_sign_in_at
    last_sign_in_ip
    password_changed_at
    reset_password_sent_at
    reset_password_token
    sign_in_count
    unconfirmed_email
    unlock_token
    updated_at
    tokens
    updated_at
    created_at
    id
    uid
  ], on: %i[create destroy update]

  attr_accessor :will_save_change_to_departments

  # Include default devise modules.
  devise :database_authenticatable, :registerable, :recoverable, :rememberable, :trackable, :secure_validatable, :lockable,
         :omniauthable, :password_expirable, :password_archivable, :confirmable, omniauth_providers: %i[google_oauth2 entra_id openid_connect]

  include DeviseTokenAuth::Concerns::User
  include SoftDeletable
  include PgSearch::Model
  include Normalizable

  has_and_belongs_to_many :departments, -> { distinct }

  validates :name, presence: true, allow_blank: false

  has_many :permissions, class_name: 'StepPermission', dependent: :destroy, inverse_of: :user
  has_many :department_permissions, through: :departments, class_name: 'StepPermission', source: :permissions
  has_many :answers, as: :step_authorizer
  has_many :departments_user, class_name: 'DepartmentsUser'

  normalize_squish :name, :ddi, :ddd, :phone, :email

  validates :email, presence: true, uniqueness: { case_sensitive: false }, allow_blank: false
  validate :validade_updating_email, on: :update
  validate :validate_email_domain_valid
  validate :validate_block_menus, if: proc { block_menus.present? }

  validates :ddi, length: { in: 1..4 }, allow_blank: false, if: :phone_present?
  validates :ddd, length: { in: 1..4 }, allow_blank: true
  validates :ddd, presence: true, allow_blank: false, if: proc { phone_present? && [BRAZIL_DDI, USA_DDI].include?(ddi) }
  validates :phone, length: { in: 7..9 }, allow_blank: false, if: :phone_present?

  before_validation :unmask_phone, if: :phone_present?

  before_save :set_current_departments, if: proc { will_save_change_to_departments }
  before_create :approve_user, if: proc { Company.current.bypass_approval? }

  after_create_commit :send_email_requesting_approvement
  after_update :send_email_when_user_is_approved
  after_save :save_twilio_user, if: proc { (saved_change_to_name? || saved_change_to_chat_enabled?) && chat_enabled? }
  after_create :update_generate_token

  scope :approved, -> { where(approved: true) }

  pg_search_scope :search, against: { name: 'A', email: 'B' }, ignoring: :accents, using: { tsearch: { prefix: true, any_word: true } }

  KEEP_NUMBERS_REGEX = /\D/

  BRAZIL_DDI = '55'.freeze
  USA_DDI = '1'.freeze

  def update_generate_token
    self.update_column("authorization_token", generate_token)
  end

  def generate_token
    random_value = SecureRandom.hex(32)
    Digest::SHA2.hexdigest(random_value)
  end

  def full_phone_number
    return '' unless [ddi, phone].all? { |attribute| attribute.present? }

    ['+', ddi, ddd, phone].compact_blank.join
  end

  def active_for_authentication?
    errors.clear

    if need_change_password?
      errors.add(:base, I18n.t('change_required', scope: 'devise.password_expired'))

      return false
    end

    if discarded? || access_locked?
      errors.add(:base, I18n.t('devise.failure.locked'))

      return false
    end

    super
  end

  def can_access?(step_id)
    user_permission = Rails.cache.fetch("user_#{id}_step_#{step_id}_permissions", expires_in: 8.hours) do
      permissions.exists?(step_id: step_id)
    end

    department_permission = false

    departments.any? do |department|
      department_permission = Rails.cache.fetch("department_#{department.id}_step_#{step_id}_permissions", expires_in: 8.hours) do
        department_permissions.exists?(step_id: step_id)
      end
    end

    user_permission || department_permission
  end

  def expire_password_after
    return 1000.years if ['google_oauth2', 'entra_id', 'openid_connect'].include?(provider)

    Company.current&.expire_password_after
  end

  def send_email_requesting_approvement
    UserApprovementMailer.request_approvement_for_user(self).deliver_later unless approved
  end

  def send_email_when_user_is_approved
    UserApprovementMailer.user_approved(self).deliver_later if saved_change_to_approved? && approved
  end

  def approve_user
    self.approved = true

    self.department_ids = [Company.current.default_department_id] if Company.current.default_department_id.present? && department_ids.empty?
  end

  def chat_enabled?
    Company.current.chat_enabled? && super
  end

  # :nocov:
  def token_validation_response
    as_json(except: %i[tokens created_at updated_at]).merge('chat_enabled' => chat_enabled?)
  end
  # :nocov:

  private

  def phone_present?
    phone_attributes.any? { |attribute| self[attribute].present? }
  end

  def unmask_phone
    phone_attributes.each { |attribute| self[attribute] = self[attribute].to_s.gsub(KEEP_NUMBERS_REGEX, '') }
  end

  def phone_attributes
    %i[phone ddd ddi]
  end

  def validade_updating_email
    errors.add(:base, I18n.t('updating_email', scope: 'devise.oauth')) if (provider == 'google_oauth2' || provider == 'entra_id') && email_change.present?
  end

  def validate_email_domain_valid
    return if email.blank? || changes.keys == ['tokens']

    email_domain = email.split('@').last

    if provider == 'email' && Company.current.auth_domain.include?(email_domain)
      errors.add(:base, I18n.t('devise.omniauth.domain_already_registered', email_domain: email_domain))
    end
  end

  def validate_block_menus
    valid_menus = %w[dashboard search favorites bulk_saving_answers answer_versions report statistics]

    errors.add(:base, I18n.t('user.attributes.block_menus.unpermitted_value', scope: 'activerecord.errors.models')) if (block_menus - valid_menus).any?
  end

  def save_twilio_user
    Twilio::UserService.new.save(self)
  end

  def set_current_departments
    self.current_departments = Department.where(id: department_ids).order(name: :asc).pluck(:name)
  end
end
