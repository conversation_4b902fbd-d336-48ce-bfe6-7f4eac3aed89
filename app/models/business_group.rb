class BusinessGroup < ApplicationRecord
  include Discard::Model
  include SoftDeletable
  include Translatable
  include TranslatedAttribute
  include Normalizable

  has_paper_trail ignore: %i[updated_at created_at id], on: %i[create update destroy]

  has_many :businesses, -> { not_sub_business }, dependent: :destroy, inverse_of: :business_group
  has_many :businesses_with_subs, dependent: :destroy, inverse_of: :business_group, class_name: 'Business'

  normalize_squish :name
  validates :name, presence: true, allow_blank: false
end
