class Contact < ApplicationRecord
  validates :description, :email, presence: true, allow_blank: false
  validates :ddi, length: { in: 1..4 }, allow_blank: false, if: :phone_present?
  validates :ddd, length: { in: 1..4 }, allow_blank: true
  validates :phone, length: { in: 7..9 }, allow_blank: false, if: :phone_present?
  validate :validate_phone_number, if: :phone_present?

  before_validation :unmask_phone

  KEEP_NUMBERS_REGEX = /\D/

  BRAZIL_DDI = '55'
  USA_DDI = '1'

  def full_phone_number
    return unless self.ddi.present? && self.phone.present?

    data = ["+#{ddi}"]
    data << ddd if ddd.present?
    data << phone

    data.join
  end

  private

  def phone_present?
    self.phone.present? || self.ddd.present? || self.ddi.present?
  end

  def validate_phone_number
    if (self.ddi == BRAZIL_DDI || self.ddi == USA_DDI)
      return errors.add( :ddd, :required ) unless self.ddd.present?
    end
  end

  def unmask_phone
    self.ddi = ddi.to_s.gsub(KEEP_NUMBERS_REGEX, '')
    self.ddd = ddd.to_s.gsub(KEEP_NUMBERS_REGEX, '')
    self.phone = phone.to_s.gsub(KEEP_NUMBERS_REGEX, '')
  end
end
