json.array! @records do |record|
  json.id record.id
  json.content_id record.content_id
  json.business_id record.content.business_id
  json.business_name record.content.business.name
  json.step_name record.step.name
  json.status record.content.status

  key_fields = record.content.business&.key_fields&.map do |key_field|
    { label: key_field.label, value: record.values&[key_field.id] }
  end

  key_fields = key_fields.select { |hash| hash[:value].present? }

  json.key_fields key_fields do |key_field|
    json.label key_field[:label]
    json.value key_field[:value]
  end

  json.updated_at record.updated_at.iso8601
  json.user record.user.try(:name) || record.created_by.try(:name) || record.content.created_by.try(:name)
end
