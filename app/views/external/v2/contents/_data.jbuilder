json.updated_at content.updated_at.iso8601
json.content_id content.id
json.parent_id content.parent_id if content.parent_id.present?

json.steps content.steps do |step|
  json.id step.id
  json.name step.name
  json.updated_at step.answers.find_by(content_id: content.id).updated_at.iso8601
  json.templates step.templates do |template|
    json.id template.id
    json.variable template.variable
    json.fields template.active_fields do |field|
      json.partial! 'external/v2/contents/field', field: field, content: content, step: step
    end
  end
end
