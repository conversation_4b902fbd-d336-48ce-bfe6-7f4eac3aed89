class TemplateS<PERSON><PERSON>
  def initialize(parameters = {})
    @parameters = parameters
  end

  def search
    return filtered_scope.order(Arel.sql('(deleted_at IS NULL) DESC')).order(:name) unless deleted_filter?

    filtered_scope.order(:name)
  end

  private

  def filtered_scope
    scope = Template.all

    scope = add_deleted_filter(scope) if @parameters.present? && !@parameters[:deleted].nil?

    scope
  end

  def deleted_filter?
    @parameters[:deleted].present? && @parameters[:deleted].in?(['true', true, 'false', false])
  end

  def add_deleted_filter(scope)
    val = @parameters[:deleted]

    return scope.discarded if val == true  || val == 'true'
    return scope.kept      if val == false || val == 'false'

    scope
  end
end
