class StepSearcher
  def initialize(parameters = {})
    @parameters = parameters || {}
  end

  def search
    base_scope
      .then { |s| filter_by_business(s) }
      .then { |s| filter_by_ids(s) }
      .then { |s| filter_by_kept(s) }
      .then { |s| apply_includes(s) }
      .then { |s| order_scope(s) }
  end

  private

  attr_reader :parameters

  def base_scope
    Step.all
  end

  def filter_by_business(scope)
    return scope unless parameters[:business_id].present?

    scope.where(business_id: parameters[:business_id])
  end

  def filter_by_ids(scope)
    return scope unless parameters[:ids].present?

    scope.where(id: parameters[:ids])
  end

  def filter_by_kept(scope)
    return scope unless parameters[:kept].present?

    scope.kept
  end

  def apply_includes(scope)
    scope.includes(:business)
  end


  def order_scope(scope)
    scope.order(order: :asc)
  end
end
