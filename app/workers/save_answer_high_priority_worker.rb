class SaveAnswerHighPriorityWorker
  include Sidekiq::Worker

  sidekiq_options retry: 0, queue: :save_answer_high_priority_worker, lock: :until_executed, lock_ttl: 12.hours

  # rubocop:disable Style/OptionalBooleanParameter
  def perform(tenant, answer_processing_id, reprocess = false)
    Apartment::Tenant.switch! tenant

    answer_processing = AnswerProcessing.find_by(id: answer_processing_id)

    return update_bulk_saving_answer(answer_processing) if !reprocess && !answer_processing.created?

    answer_processing.processing!
    answer_processing.bulk_saving_answer.processing!

    content = load_content(answer_processing.bulk_saving_answer.step, answer_processing.bulk_saving_answer.user_id, answer_processing.data)

    content.reorder_answers

    answer_processing.update!(answer_id: content.answers.find_by(step_id: answer_processing.bulk_saving_answer.step_id).try(:id))

    save_answer(answer_processing, content)

    answer_processing.done!

    update_bulk_saving_answer(answer_processing)
  rescue ActiveRecord::ConnectionTimeoutError => e
    Sidekiq.logger.error "[SaveAnswerHighPriorityWorker] Connection timeout: #{e.message}"
    raise Sidekiq::Retry::RetryJob.new(e)
  rescue StandardError => e
    Sidekiq.logger.error "[SaveAnswerHighPriorityWorker] error: #{e.message}"
    answer_processing.update(processing_errors: e.message, status: :failed)
    update_bulk_saving_answer(answer_processing)
  end

  def update_bulk_saving_answer(answer_processing)
    return if AnswerProcessing.exists?(bulk_saving_answer_id: answer_processing.bulk_saving_answer_id, status: %i[created processing])

    answer_processing.bulk_saving_answer.update(status: :done, end_at: Time.zone.now)
  end

  def load_content(step, user_id, processing_data)
    raise StandardError, I18n.t('unconfigured_primary_key', business_name: step.business&.name, scope: 'activerecord.errors.workers') if step&.business&.key_field_ids.blank?
    raise StandardError, I18n.t('unconfigured_primary_key', business_name: step.business.parent&.name, scope: 'activerecord.errors.workers') if step.business.sub_business? && step.business.parent.key_field_ids.blank?

    parent_id = Content.for_pks(step.business.parent&.id, processing_data).first.try(:id) if step.business.sub_business?
    raise StandardError, I18n.t('no_parent_record_found', scope: 'activerecord.errors.workers') if step.business.sub_business? && parent_id.blank?

    content_for_pk_scope = Content.for_pks(step.business_id, processing_data)
    raise StandardError, I18n.t('no_record_found', scope: 'activerecord.errors.workers') if content_for_pk_scope.blank? && !step.first_of_business?

    content_for_pk_scope = content_for_pk_scope.where(parent_id:) if parent_id.present?
    content_for_pk = content_for_pk_scope.first

    raise StandardError, I18n.t('record_with_same_keys', keys: content_url(content_for_pk), scope: 'activerecord.errors.workers') if step.business.bulk_insert_on_first_step_validates_pk? && content_for_pk.present? && step.first_of_business?
    return content_for_pk if content_for_pk.present?

    create_params = { business_id: step.business_id, created_by_id: user_id, parent_id: }

    service = ContentService.new(create_params)

    service.create

    raise StandardError, service.errors.join(',') unless service.success

    service.record
  end

  def content_url(content)
    content.edit_url
  end

  def save_answer(answer_processing, content)
    business = content.business
    bulk_saving_answer = answer_processing.bulk_saving_answer
    skip_verification_url = !bulk_saving_answer.enable_verification_url?
    skip_business_validations = !bulk_saving_answer.enable_business_validations? && business.allow_to_skip_validations_when_bulking?
    skip_field_validations = !bulk_saving_answer.enable_field_validations? && business.allow_to_skip_validations_when_bulking?

    answer = content.answers.find_by(step_id: answer_processing.bulk_saving_answer.step_id)
    answer.values ||= {}
    answer.values = answer.values.merge(answer_processing.treated_data)

    answer_decorated = answer.decorate(
      context: { skip_verification_url: }, current_user: answer_processing.bulk_saving_answer.user
    ).values.with_indifferent_access
    answer_processing.update!(data_with_verification_url_response: answer_decorated)

    answer_params = {
      content_id: content.id,
      user_id: bulk_saving_answer.user_id,
      skip_business_validations:,
      skip_field_validations:,
      origin: Answer.origins[:bulk_saving]
    }.merge(answer_decorated).merge(answer_processing.treated_data)

    answer_service = AnswerService.new(answer_params.with_indifferent_access)
    answer_service.update(answer.id)

    raise StandardError, answer_service.errors.join(',') unless answer_service.success

    answer_service.record
  end
end
