module Dashboard
  class BusinessStatsDecorator
    attr_reader :id, :name, :pending_count, :done_count, :changing_count, 
                :under_review_count, :waiting_authorization_count, :count, :inactives

    def initialize(stats_hash)
      @id = stats_hash[:id]
      @name = stats_hash[:name]
      @pending_count = stats_hash[:pending_count]
      @done_count = stats_hash[:done_count]
      @changing_count = stats_hash[:changing_count]
      @under_review_count = stats_hash[:under_review_count]
      @waiting_authorization_count = stats_hash[:waiting_authorization_count]
      @count = stats_hash[:count]
      @inactives = stats_hash[:inactives]
    end
  end
end
