module Dashboard
  class BusinessGroupDecorator < Draper::Decorator
    delegate :id, :name, to: :object

    def inactives
      business_stats.sum { |stats| stats[:inactives] }
    end

    def pending_count
      business_stats.sum { |stats| stats[:pending_count] }
    end

    def done_count
      business_stats.sum { |stats| stats[:done_count] }
    end

    def count
      business_stats.sum { |stats| stats[:count] }
    end

    def changing_count
      business_stats.sum { |stats| stats[:changing_count] }
    end

    def under_review_count
      business_stats.sum { |stats| stats[:under_review_count] }
    end

    def waiting_authorization_count
      business_stats.sum { |stats| stats[:waiting_authorization_count] }
    end

    def businesses
      @businesses ||= object.businesses.not_sub_business.with_permission_for_user(current_user).where(show_on_dashboard: true)
    end

    def top_doing
      @top_doing ||= business_stats.sort_by { |stats| stats[:pending_count] }.reverse.map do |stats|
        Dashboard::BusinessStatsDecorator.new(stats)
      end
    end

    private

    def business_stats
      @business_stats ||= Dashboard::BusinessStatsService.new(businesses, current_user).call
    end

    delegate :current_user, to: :h
  end
end
