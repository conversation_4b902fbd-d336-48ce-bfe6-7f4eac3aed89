class BusinessHeaderDecorator < Draper::Decorator
  delegate_all

  def initialize(options = {})
    @options = options
  end

  def fields_with_values
    headers = BusinessHeader.includes(field: :translations).where(business_id: @options[:business_id])

    values_index = fetch_content_values_index(headers)

    headers.map do |header|
      next if header.field.blank?

      cv = values_index[[header.step_id, header.field_id]]

      {
        field: header.field,
        value: cv ? ContentValueDecorator.decorate(cv).try(:value) : ''
      }
    end.compact
  end

  private

  def fetch_content_values_index(headers)
    step_ids = headers.map(&:step_id).uniq
    field_ids = headers.map(&:field_id).uniq

    return {} if step_ids.empty? || field_ids.empty?

    ContentValue
      .joins(:answer)
      .includes(:answer, :field)
      .where(answers: { step_id: step_ids })
      .where(content_id: @options[:content_id], field_id: field_ids)
      .index_by { |cv| [cv.answer.step_id, cv.field_id] }
  end
end