class AnswerDecorator < Draper::Decorator
  delegate_all

  def initialize(object, options = {})
    @object = object
    @original_object_values = @object&.data&.dig('values') || {}
    @options = options
    @context = options[:context] || {}
    @current_user = options[:current_user]
    @parents = @context[:parents] || {}

    @input_url_service = will_call_input_url? ? instanciate_input_url_service : nil
    @input_business_rules_service = will_call_business_rules? ? instanciate_business_rules_service : nil
  end

  # Values should have the following priority:
  # 1. Input URL
  # 2. Already saved answer values
  # 3. Business rules
  # 4. Default value
  def values
    default_values.merge(business_rules_values.merge(original_object_values.merge(input_url_values)))
  end

  def sub_contents
    sub_business_fields = Field.kept.for_step(object.step_id).where(type: :sub_business)
    return {} if sub_business_fields.empty?
    
    subcontent_ids = object.sub_contents.pluck(:id)
    content_values_by_content_id = ContentValue.includes(:field)
                                              .where(content_id: subcontent_ids)
                                              .order('fields.order')
                                              .group_by(&:content_id)
    
    sub_business_fields.map do |field|
      subcontents_values = subcontent_ids.map do |id|
        content_value = content_values_by_content_id[id]&.first
        { content_id: id, name: content_value&.value }
      end

      { field.id => subcontents_values }
    end.reduce({}, :merge)
  end

  def values_with_sub_contents
    (values || {}).merge(sub_contents)
  end

  # Field attributes should have the following priority:
  # 1. Input URL
  # 2. Business rules
  # 3. Default attribute saved in database
  #
  # Field attributes that can be modified by URL:
  # . Label
  # . Tooltip
  # . Required
  # . Enabled
  # . Visible
  # . Size
  # . Height
  # . Options
  #
  # Field attributes that can be modified by business rules:
  # . Required
  # . Enabled
  # . Visible
  def templates
    return [] if forbidden_user?

    templates = []
    object.step.step_templates.ascending_order.each do |step_template|
      template = step_template.template.as_json
      template['order'] = step_template.order
      fields = []
      step_template.template.active_fields.each do |field|
        field_hash = field.as_json
        input_url_field_attributes = @input_url_service&.attributes_of_field(field_hash).to_h || {}
        input_business_rules_field_attributes = @input_business_rules_service&.attributes_of_field(field_hash) || {}

        field_modification = input_business_rules_field_attributes.merge(input_url_field_attributes)

        next fields << field_hash if field_modification.blank?

        assign_value(field_hash, 'label', field_modification)
        assign_value(field_hash, 'tooltip', field_modification)

        assign_boolean(field_hash, 'required', field_modification)
        assign_boolean(field_hash, 'enabled', field_modification)
        assign_boolean(field_hash, 'visible', field_modification)
        field_hash['size'] = Field.sizes[field_modification['size'].to_s] unless field_modification['size'].nil?
        field_hash['height'] = Field.heights[field_modification['height'].to_s] unless field_modification['height'].nil?
        field_hash['type'] = Field.types[field_hash['type'].to_s]
        custom_options = @input_url_service&.get_options(field_hash)
        field_hash['options'] = custom_options.nil? || custom_options&.empty? ? field.options : custom_options

        fields << field_hash
      end
      template['fields'] = fields
      templates << template
    end
    templates
  end

  def verification_url_error?
    @input_url_service&.verification_url_error? || false
  end

  def forbidden_user?
    @input_url_service&.forbidden_user? || false
  end

  def forbidden_user_error
    @input_url_service&.forbidden_user_error
  end

  private

  # Values
  def original_object_values
    return {} if @original_object_values.blank?

    field_ids = @original_object_values.keys
    fields_by_id = Field.where(id: field_ids).index_by(&:id)

    @original_object_values.each_with_object({}) do |(field_id, value), hash|
      field = fields_by_id[field_id]
      next if field.blank?

      hash[field_id] = field.parse_value(value)
    end
  end

  def input_url_values
    @input_url_service.nil? ? {} : @input_url_service.values_for_answer(@object)
  end

  def business_rules_values
    @input_business_rules_service.nil? ? {} : @input_business_rules_service.changed_values
  end

  def default_values
    return {} unless will_use_default_values?
    @object.step.fields.map.with_object({}) do |field, hash|
      next if field.default_value.blank?
      new_default_value = field.parse_value(field.default_value)
      hash[field.id] = new_default_value
    end
  end

  # Flags
  def will_call_input_url?
    return false if object.step.verification_url.blank?
    return false if context[:skip_verification_url].to_s == "true"

    true
  end

  def will_call_business_rules?
    return false unless @object.content.business.apply_field_rule
    return false if context[:skip_input_rules].to_s == "true"
    return false if context[:skip_business_rules].to_s == "true"

    true
  end

  def will_use_default_values?
    @object.content.business.fill_default_field_value
  end

  #Instantiators
  def instanciate_input_url_service
    @object.data = { 'values' => default_values.merge(original_object_values) }
    VerificationUrlService.new(@object, @object.step.verification_url, @context, @current_user)
  end

  def instanciate_business_rules_service
    @object.data = { 'values' => default_values.merge(original_object_values.merge(input_url_values)) }
    BusinessRulesService.new(@object, :input, @current_user, [], @parents)
  end

  # Assigners
  def assign_boolean(field, field_attribute, field_modification)
    current_attribute_modification = field_modification[field_attribute]
    return if current_attribute_modification.nil?

    field[field_attribute] = [true, 'true'].include?(current_attribute_modification) ? true : false

    nil
  end

  def assign_value(field, field_attribute, field_modification)
    field[field_attribute] = field_modification[field_attribute] unless field_modification[field_attribute].nil?
    nil
  end

end
