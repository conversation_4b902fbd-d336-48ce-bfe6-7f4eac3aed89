class OperatorDependentFieldRulesValidator
  def initialize(hash)
    @hash = hash
  end

  def valid?
    rules = @hash.dig('rules', 'rules')
    return false if rules.nil?

    all_rules_valid?(rules)
  end

  private
  
  def all_rules_valid?(rules)
    rules.all? do |rule|
      single_rule_valid?(rule)
    end
  end

  def single_rule_valid?(rule)
    nested_rules = rule.dig('rules')
    if nested_rules.nil? # Reached the bottom rules group
      rule['operator'].present? && !greater_or_lower_with_array?(rule)
    else
      all_rules_valid?(nested_rules)
    end
  end

  def greater_or_lower_with_array?(rule)
    rule['value'].is_a?(Array) && rule['operator'].in?(['greater', 'lower'])
  end
end
