class UserService < ApplicationService
  service_for :user

  def create
    record = model.new(@parameters)
    provider = @parameters[:provider] || 'email'

    record.provider = provider
    record.skip_confirmation!

    save_record(record)
  end

  def update(id)
    record = model.find(id)

    will_save_change_to_departments = !@parameters[:department_ids].nil? && @parameters[:department_ids] != record.department_ids

    record.assign_attributes(@parameters.merge(will_save_change_to_departments: will_save_change_to_departments))

    save_record(record)
  end

  def create_unconfirmed(confirm_success_url)
    record = model.new(@parameters)

    save_record(record)

    record.send_confirmation_instructions({ redirect_url: confirm_success_url }) if @success
  rescue ActiveRecord::RecordNotFound => e
    @success = false
    @errors = e.message
  end

  def lock(user_id)
    user = User.find(user_id)

    user.assign_attributes(locked_at: Time.zone.now)
    user.save!

    @success = true
    @record = user.reload
  end

  def unlock(user_id)
    user = User.find(user_id)

    user.assign_attributes(locked_at: nil, unlock_token: nil, failed_attempts: 0)
    user.save!

    @success = true
    @record = user.reload
  end

  def token_to_confirm_step
    user = [true, 'true'].include?(@parameters['administrator']) ? Administrator.find_by(email: @parameters['email']) : User.find_by(email: @parameters['email'])
    includes_user = [true, 'true'].include?(@parameters['administrator']) || Step.joins(:step_permissions).merge(StepPermission.for_user(user&.id)).distinct.pluck(:id).include?(@parameters['step_id'])

    if includes_user && user.present?
      token = SecureRandom.hex(3).upcase
      step = Step.find(@parameters['step_id'])
      Answer.find(@parameters['answer_id']).update_column(:authorizer_token, token)
      
      UserStepTokenMailer.send_token(user, step, token).deliver_later
      @success = true
    else
      @success = false
      @errors = [I18n.t('activerecord.errors.models.step.informed_user_has_no_access')]
    end
  end
end
