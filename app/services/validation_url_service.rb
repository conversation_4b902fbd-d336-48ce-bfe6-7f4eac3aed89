require 'async'
require 'async/http/internet'
require 'ostruct'

class ValidationUrlService
  REQUEST_TIMEOUT = 150
  CATEGORY = 'validation'.freeze

  def initialize(answer, validation_url)
    @answer = answer
    @validation_url = validation_url
  end

  def validate
    return if @answer.pending? || @validation_url.blank? || skip_validation_url

    Rails.logger.info "[VALIDATION POST] #{@validation_url}"

    data = {
      content_id: @answer.content_id,
      answer_id: @answer.id,
      business_id: @answer.content.business_id,
      step_name: @answer.step.name,
      type: 'validation_url'
    }

    troubleshooting = Troubleshooting.create!(thread_id: Thread.current.object_id, subdomain: Apartment::Tenant.current, start_at: Time.zone.now, url: @validation_url, external: true, data:)

    response = do_request(troubleshooting)

    duration = Time.zone.now - troubleshooting.start_at

    if response.code == '200'
      Rails.logger.info "[VALIDATION POST RESPONSE][#{duration}s] #{response.body}"

      troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: duration, response_code: response.code)
      xml_doc = Nokogiri::XML(response.body)

      raise I18n.t('validation_url.invalid_response', scope: 'activerecord.errors.services') if xml_doc.at_xpath('//validation').nil?

      unless [true, 'true'].include?(xml_doc.at_xpath('//validation')['success'])
        errors = []

        xml_doc.xpath('//validation//error').each do |error|
          errors.push(error.content)
        end

        raise errors.join(', ')
      end
    else
      Rails.logger.error "ERROR ON VALIDATION URL[#{duration}s] #{response.code} - #{response}"

      troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: duration, response_code: response.code)

      raise I18n.t('validation_url.comunication_error', validation_url: @validation_url, code: response.code, scope: 'activerecord.errors.services')
    end
  end

  def skip_validation_url
    processing = AnswerProcessing.where(answer_id: @answer.id).processing.order(:created_at).last

    return true unless @answer.content.business.enable_validation_web_service?
    return true if processing && !processing.bulk_saving_answer.enable_validation_url

    false
  end

  def do_request(troubleshooting)
    builder = ValidationXmlBuilder.new(@answer, @answer.user)
    xml_body = builder.generate

    headers = [
      ['env', Rails.env],
      ['tenant', Apartment::Tenant.current],
      ['category', CATEGORY],
      ['Content-Type', 'application/xml'],
      ['Accept', 'application/xml']
    ]

    response, response_body, status_code = nil

    Sync do |task|
      task.with_timeout(REQUEST_TIMEOUT) do
        internet = Async::HTTP::Internet.new
        response = internet.post(@validation_url, headers, xml_body)

        response_body = response&.read
        status_code = response&.status.to_s
      ensure
        response&.close
        internet&.close
      end
    end

    raise StandardError, 'Request failed' if response.nil?

    raise InternalServerError.new(response_body, status_code) if status_code == '500'
    raise BadRequestError.new(response_body, status_code) if status_code == '400'
    raise InvalidReturnError.new(response_body, status_code) if status_code != '200'

    OpenStruct.new(code: status_code, body: response_body)
  rescue Async::TimeoutError
    duration = Time.zone.now - troubleshooting.start_at

    Rails.logger.error "TIMEOUT ERROR ON VALIDATION URL[#{duration}s] #{@validation_url}"

    troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: duration, response_code: 'timeout')

    raise I18n.t('validation_url.timeout_error', validation_url: @validation_url, scope: 'activerecord.errors.services')
  rescue BadRequestError, InternalServerError => e
    body = e.body
    code = e.code
    duration = Time.zone.now - troubleshooting.start_at
    Rails.logger.error "ERROR ON VALIDATION URL[#{duration}s] #{body}"
    troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: duration, response_code: body)
    raise I18n.t('validation_url.comunication_error', validation_url: @validation_url, code:, scope: 'activerecord.errors.services')
  rescue InvalidReturnError => e
    duration = Time.zone.now - troubleshooting.start_at

    Rails.logger.error "ERROR ON VALIDATION URL[#{duration}s] #{e.message}"

    troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: duration, response_code: e.message)

    raise I18n.t('validation_url.invalid_return', scope: 'activerecord.errors.services')
  rescue StandardError => e
    duration = Time.zone.now - troubleshooting.start_at

    Rails.logger.error "ERROR ON VALIDATION URL[#{duration}s] #{e.message}"

    troubleshooting.update!(end_at: Time.zone.now, duration_in_seconds: duration, response_code: e.message)

    raise I18n.t('validation_url.simple_comunication_error', validation_url: @validation_url, message: e.message, scope: 'activerecord.errors.services')
  end
end
