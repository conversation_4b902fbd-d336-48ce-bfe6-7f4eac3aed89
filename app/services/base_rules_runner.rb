# The BaseRulesRunner abstract class is responsible for retrieving and parsing rules based on the context.
# It provides methods to get rules for different contexts and to parse field values based on their types.
#
# Methods:
# - get_rules: Retrieves rules based on the context.
# - parse_field_value: Parses the value of a field based on its type.
# - get_access_control_rules: Abstract method to be implemented for retrieving access control rules.
# - get_input_rules: Abstract method to be implemented for retrieving input rules.
# - get_dynamic_rules: Abstract method to be implemented for retrieving dynamic rules.
#
# Example usage:
#   class MyRulesRunner < BaseRulesRunner
class BaseRulesRunner
  def filter_restrictions(rules, current_user=nil)
    return rules if current_user.nil?
    user_id = current_user.id
    user_department_ids = current_user.departments.map(&:id).to_set

    rules.select do |rule|
      next true if rule.restrictions.blank?

      grouped_restrictions = rule.restrictions.group_by { |r| r["class"] }

      user_restriction = grouped_restrictions['User'].any? { |r| r["id"] == user_id } if grouped_restrictions['User']
      department_restriction = grouped_restrictions['Department'].any? { |r| user_department_ids.include?(r["id"]) } if grouped_restrictions['Department']

      return true if user_restriction.nil? && department_restriction.nil?

      user_restriction || department_restriction || false
    end.to_set
  end

  def unmatched_rules
    @all_rules - @matched_rules
  end

  def format_value(value)
    value = value.pluck('value') if value.is_a?(Array) && value.any? { |hash| hash.is_a?(Hash) && hash['value'].present? }

    value
  end

  def this_step_business_rules
    DependentFieldRule.where(
      business_id: get_business_id_from_content(@answer.content_id)
    ).with_action_key(@answer.step_id)
  end

  def get_business_id_from_content(content_id)
    Content.where(id: content_id).pick(:business_id)
  end

  # Loads all values from the content into an instance variable.
  # The variable is a hash with the structure {step_id:field_id => value}
  def get_content_values
    content_values = {}

    if @answer.content.parent.present?
      @answer.content.parent.answers.each do |answer|
        answer.data["values"]&.each do |key, value|
          content_values["#{answer.step_id}:#{key}"] = value
        end
      end
    end

    @answer.content.answers.where.not(id: @answer.id).each do |answer|
      answer.data["values"]&.each do |key, value|
        content_values["#{answer.step_id}:#{key}"] = value
      end
    end

    @answer.data["values"]&.each do |key, value|
      if key.include?(":")
        content_values[key] = value
      else
        content_values["#{@answer["step_id"]}:#{key}"] = value
      end
    end

    @parents&.each do |key, value|
      content_values[key] = value
    end

    content_values
  end

  def rule_match?(rule)
    condition_set_match?(rule.rules)
  end

  # Evaluates a condition set and returns true if the condition is met.
  #
  # The condition set is an array of rules, and a condition. The condition
  # is one of 'and' or 'or'. The rules are evaluated in order, and if a
  # rule is itself a condition set, it is evaluated recursively.
  #
  # @param [Hash] condition_set The condition set to evaluate.
  # @return [Boolean] true if the condition is met, false otherwise.
  def condition_set_match?(condition_set)
    conditions_result = []

    condition_set['rules'].each do |rule|
      if rule.key?('rules')
        conditions_result << condition_set_match?(rule)
      else
        conditions_result << condition_match?(rule)
      end
    end

    case condition_set['condition']
      when 'and'
        conditions_result.all?
      when 'or'
        conditions_result.any?
      else
        false
    end
  end

  # Returns true if the condition matches the content value
  def condition_match?(condition)
    field_id = condition['field']&.split(':')[1]
    field = Field.find(field_id)
    field_type = field.type

    parsed_actual_value = parse_field_value(field, @content_values[condition['field']])

    parsed_condition_value = if ['regex_match', 'regex_not_match'].include?(condition['operator'])
      condition['value'].to_regexp
    else
      parse_field_value(field, condition['value'])
    end
    not_allowed_operator_for_nil_value = ['equals', 'not_equal'].exclude?(condition['operator'])
    return false if (parsed_actual_value.nil? || parsed_condition_value.nil?) && not_allowed_operator_for_nil_value

    case condition['operator']
    when 'equals'
      parsed_actual_value == parsed_condition_value
    when 'not_equal'
      parsed_actual_value != parsed_condition_value
    when 'lower'
      return parsed_actual_value < parsed_condition_value unless parsed_actual_value.is_a?(Array)
      parsed_actual_value.all? { |value| value < parsed_condition_value }
    when 'greater'
      return parsed_actual_value > parsed_condition_value unless parsed_actual_value.is_a?(Array)
      parsed_actual_value.all? { |value| value > parsed_condition_value }
    when 'regex_match'
      if parsed_actual_value.is_a?(Array)
        parsed_actual_value.all? { |value| parsed_condition_value.match?(value) }
      else
        parsed_condition_value.match?(parsed_actual_value)
      end
    when 'regex_not_match'
      if parsed_actual_value.is_a?(Array)
        parsed_actual_value.all? { |value| !(parsed_condition_value.match?(value)) }
      else
        !(parsed_condition_value.match?(parsed_actual_value))
      end
    when 'contains'
      if parsed_actual_value.is_a?(Array)
        parsed_condition_value.all? { |value| parsed_actual_value.include?(value) }
      else
        parsed_actual_value.include?(parsed_condition_value)
      end
    when 'not_contains'
      if parsed_actual_value.is_a?(Array)
        parsed_condition_value.all? { |value| parsed_actual_value.exclude?(value) }
      else
        parsed_actual_value.exclude?(parsed_condition_value)
      end
    else
      false
    end
  end

  def get_rules
    case @context
    when :input
      get_input_rules
    when :dynamic
      get_dynamic_rules
    when :access_control
      get_access_control_rules
    when :validation
      get_validation_rules
    else
      raise ArgumentError, "Invalid context: #{@context}"
    end
  end

  def parse_field_value(field, value)
    return field.empty_value if value.blank?

    case field.type
    when 'date'
      begin
        Date.parse(value)
      rescue Date::Error
        nil
      end
    when 'integer'
      value.to_i
    when 'decimal'
      value.to_f
    when 'multiple_reference'
      if value.is_a?(String)
        value = Array(value)
      elsif value.any? { |hash| hash.is_a?(Hash) && hash['value'].present? }
        value.map! { |hash| hash['value'] }
      end

      value&.sort!
    else
      value
    end
  end

  def get_access_control_rules
    raise NotImplementedError, 'You must implement the get_access_control_rules method'
  end

  def get_input_rules
    raise NotImplementedError, 'You must implement the get_input_rules method'
  end

  def get_dynamic_rules
    raise NotImplementedError, 'You must implement the get_dynamic_rules method'
  end

  def get_validation_rules
    raise NotImplementedError, 'You must implement the get_validation_rules method'
  end
end
