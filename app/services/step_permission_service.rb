class StepPermissionService
  attr_reader :success, :errors, :record

  BATCH_SIZE = 1000

  def initialize(parameters = {})
    @parameters = parameters.to_h.symbolize_keys
    @success = false
    @errors = []
  end

  def create
    return create_single_with_update if single_create_params?

    steps_ids, user_ids, department_ids, scope_value = fetch_bulk_params

    return set_invalid_params_error unless valid_bulk_params?(steps_ids, user_ids, department_ids)

    now = Time.current

    ActiveRecord::Base.transaction do
      if user_ids.present?
        update_holder_permissions(:user, steps_ids, user_ids, scope_value)
        bulk_create_for?(:user, steps_ids, user_ids, scope_value, now)
      end

      if department_ids.present?
        update_holder_permissions(:department, steps_ids, department_ids, scope_value)
        bulk_create_for?(:department, steps_ids, department_ids, scope_value, now)
      end

      @success = true
    end
  rescue StandardError => e
    @errors = [e.message]
    @success = false
  end

  def destroy(step_permission_id)
    step_permission = StepPermission.find(step_permission_id)
    return @success = true if step_permission.destroy

    @success = false
    @errors = step_permission.errors.full_messages
  end

  private

  def single_create_params?
    @parameters[:step_id].present? && (@parameters[:user_id].present? ^ @parameters[:department_id].present?)
  end

  def create_single
    selector = @parameters.slice(:step_id, :user_id, :department_id).compact
    step_permission = StepPermission.find_or_initialize_by(selector)
    step_permission.scope = resolve_scope(@parameters[:scope])

    if step_permission.save
      return (@record = step_permission.reload
              @success = true)
    end

    @success = false
    @errors = step_permission.errors.full_messages
  end

  # Unified single: update existing, then create missing
  def create_single_with_update
    sid = @parameters[:step_id]
    uid = @parameters[:user_id]
    did = @parameters[:department_id]
    scope_val = resolve_scope(@parameters[:scope])

    ActiveRecord::Base.transaction do
      if uid.present?
        update_holder_permissions(:user, [sid], [uid], scope_val)
        records = build_records_to_insert([sid], [uid], scope_val, holder_type: :user, timestamp: Time.current)
        bulk_insert?(records, holder_type: :user)
        @record = StepPermission.find_by(step_id: sid, user_id: uid)
      else
        update_holder_permissions(:department, [sid], [did], scope_val)
        records = build_records_to_insert([sid], [did], scope_val, holder_type: :department, timestamp: Time.current)
        bulk_insert?(records, holder_type: :department)
        @record = StepPermission.find_by(step_id: sid, department_id: did)
      end
      @success = true
    end
  rescue StandardError => e
    @errors = [e.message]
    @success = false
  end


  def array_param(array_key, fallback_single: nil)
    arr = Array(@parameters[array_key]).compact_blank.map(&:to_s)
    return [@parameters[fallback_single].to_s] if arr.blank? && fallback_single && @parameters[fallback_single].present?

    arr
  end

  def resolve_scope(scope)
    return StepPermission.scopes[:edit] if scope.blank?

    value = StepPermission.scopes[scope]
    raise ArgumentError, I18n.t('errors.messages.invalid_scope') if value.nil?

    value
  end

  def build_records_to_insert(steps_ids, holder_ids, scope_value, holder_type:, timestamp:)
    steps_ids.product(holder_ids).map do |step_id, holder_id|
      base = { step_id: step_id, scope: scope_value, created_at: timestamp, updated_at: timestamp }
      if holder_type == :user
        base.merge(user_id: holder_id)
      else
        base.merge(department_id: holder_id)
      end
    end
  end

  def bulk_insert?(records, holder_type:)
    return false if records.blank?

    relation = StepPermission.where(step_id: records.pluck(:step_id).uniq)
    relation = if holder_type == :user
                 relation.where(user_id: records.pluck(:user_id).compact.uniq)
               else
                 relation.where(department_id: records.pluck(:department_id).compact.uniq)
               end

    existing = relation.pluck(:step_id, (holder_type == :user ? :user_id : :department_id), :scope)
    scope_to_int = ->(s) { s.is_a?(Integer) ? s : StepPermission.scopes[s.to_s] }
    existing_keys = existing.map { |sid, hid, s| [sid, hid, scope_to_int.call(s)] }

    filtered = records.reject do |r|
      key = [r[:step_id], (holder_type == :user ? r[:user_id] : r[:department_id]), r[:scope]]
      existing_keys.include?(key)
    end

    return false if filtered.blank?

    filtered.each_slice(BATCH_SIZE) { |chunk| StepPermission.insert_all(chunk) }

    invalidate_cache_for_records(filtered, holder_type: holder_type)
    warm_cache_for_records(filtered, holder_type: holder_type)

    true
  end

  def invalidate_cache_for_records(records, holder_type:)
    return if records.blank?

    keys = records.filter_map do |r|
      holder_id = (holder_type == :user ? r[:user_id] : r[:department_id])
      next if holder_id.blank?

      permission_cache_key(holder_type, holder_id, r[:step_id])
    end

    keys.each { |k| Rails.cache.delete(k) }
  end

  def warm_cache_for_records(records, holder_type:)
    return if records.blank?

    groups = records.group_by { |r| [r[:step_id], (holder_type == :user ? r[:user_id] : r[:department_id])] }

    groups.each_slice(BATCH_SIZE) do |slice|
      payload = {}

      slice.each_key do |(step_id, holder_id)|
        scopes = if holder_type == :user
                   StepPermission.where(step_id: step_id, user_id: holder_id).pluck(:scope)
                 else
                   StepPermission.where(step_id: step_id, department_id: holder_id).pluck(:scope)
                 end

        key = permission_cache_key(holder_type, holder_id, step_id)
        payload[key] = scopes
      end

      if Rails.cache.respond_to?(:write_multi)
        Rails.cache.write_multi(payload)
      else
        payload.each { |k, v| Rails.cache.write(k, v) }
      end
    rescue StandardError => e
      Rails.logger.warn("warm_cache_for_records failed: #{e.message}") if defined?(Rails)
    end
  end

  def warm_cache_batch_for_update(holder_type:, step_ids:, holder_ids:)
    return if step_ids.blank? || holder_ids.blank?

    if holder_type == :user
      rows = StepPermission.where(step_id: step_ids, user_id: holder_ids).pluck(:step_id, :user_id, :scope)
      grouped = rows.group_by { |sid, uid, _| [sid, uid] }
      grouped.each do |(sid, uid), entries|
        Rails.cache.write("user_#{uid}_step_#{sid}_permissions", entries.map { |_, _, scope| scope })
      end
    else
      rows = StepPermission.where(step_id: step_ids, department_id: holder_ids).pluck(:step_id, :department_id, :scope)
      grouped = rows.group_by { |sid, did, _| [sid, did] }
      grouped.each do |(sid, did), entries|
        Rails.cache.write("department_#{did}_step_#{sid}_permissions", entries.map { |_, _, scope| scope })
      end
    end
  end

  def fetch_bulk_params
    [steps_ids, user_ids, department_ids, scope_value]
  end

  def valid_bulk_params?(steps_ids, user_ids, department_ids)
    steps_ids.present? && (user_ids.present? || department_ids.present?)
  end

  def set_invalid_params_error
    @errors = [I18n.t('errors.messages.invalid_parameters')]
    @success = false
  end

  def steps_ids
    @steps_ids ||= array_param(:steps_id, fallback_single: :step_id)
  end

  def user_ids
    @user_ids ||= array_param(:users_id, fallback_single: :user_id)
  end

  def department_ids
    @department_ids ||= array_param(:departments_id, fallback_single: :department_id)
  end

  def scope_value
    @scope_value ||= resolve_scope(@parameters[:scope])
  end

  def holder_foreign_key(type)
    type == :user ? :user_id : :department_id
  end

  def permission_cache_key(type, holder_id, step_id)
    prefix = (type == :user ? 'user' : 'department')
    "#{prefix}_#{holder_id}_step_#{step_id}_permissions"
  end

  def relation_for(type, step_ids, holder_ids)
    fk = holder_foreign_key(type)
    StepPermission.where(step_id: step_ids).where(fk => holder_ids)
  end

  def bulk_create_for?(holder_type, steps_ids, holder_ids, scope_value, timestamp)
    records = build_records_to_insert(steps_ids, holder_ids, scope_value, holder_type: holder_type, timestamp: timestamp)
    bulk_insert?(records, holder_type: holder_type)
  end

  def update_holder_permissions(holder_type, steps_ids, holder_ids, scope_value)
    relation = if holder_type == :user
                 StepPermission.where(step_id: steps_ids, user_id: holder_ids)
               else
                 StepPermission.where(step_id: steps_ids, department_id: holder_ids)
               end

    relation.update_all(scope: scope_value, updated_at: Time.current)

    pairs = if holder_type == :user
              relation.pluck(:step_id, :user_id)
            else
              relation.pluck(:step_id, :department_id)
            end

    pairs.each do |sid, hid|
      cache_key = holder_type == :user ? "user_#{hid}_step_#{sid}_permissions" : "department_#{hid}_step_#{sid}_permissions"
      Rails.cache.delete(cache_key) if hid.present?
    end

    begin
      warm_cache_batch_for_update(holder_type: holder_type, step_ids: steps_ids, holder_ids: holder_ids)
    rescue StandardError => e
      Rails.logger.warn("warm_cache_batch_for_update(#{holder_type}) failed: #{e.message}") if defined?(Rails)
    end
  end
end
