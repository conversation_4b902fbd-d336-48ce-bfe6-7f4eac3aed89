module External
  module V2
    class ContentService
      attr_reader :success, :errors, :record, :sub_content_ids
      attr_accessor :has_sub_business, :sub_params

      def initialize(parameters)
        @parameters = parameters
        @current_user = parameters['current_user']
        @remote_ip = parameters['remote_ip']
        @content_id = parameters.delete(:content_id)
        @has_sub_business = false
        @parent_content_id = nil
        @sub_params = []
        @sub_content_ids = []
        @errors = []
      end

      def save
        ActiveRecord::Base.transaction do
          response = @parameters.is_a?(ActionController::Parameters) ? @parameters.to_unsafe_h.to_h : @parameters

          if response['business_id'].present?
            business = Business.find(response['business_id'])

            content_id = @content_id
            content = create_or_load_content(business, response, content_id)

            answer_errors = []

            step_ids = response['steps']&.map { |step| step['id'] }

            invalid_steps = invalid_steps(step_ids)
            @errors << I18n.t('content.invalid_steps', invalid_steps: invalid_steps, scope: 'activerecord.errors.services') unless invalid_steps.empty?

            content.current_answers.select { |answer| step_ids.include?(answer.step_id) }.each do |answer|
              answer.reload
              step_id = answer.step_id
              step_params = response['steps']&.find { |step| step['id'] == step_id }

              skip_mandatory_fields = step_params&.dig('skip_mandatory_fields') || false
              answer.skip_mandatory_fields = skip_mandatory_fields

              if answer.available_at.present?
                validate_answer_per_fields(step_params)
                update_answer(answer, step_params)
              else
                answer_errors << I18n.t('content.step_is_not_available_for_filling', step_name: answer.step.name, scope: 'activerecord.errors.services')
              end
            end

            @errors.concat(answer_errors)

            if @has_sub_business
              @parent_content_id = content.id if @parent_content_id.nil?
              process_sub_business_contents(content)
            end

            @success = @errors.empty?
            raise ActiveRecord::Rollback unless @errors.empty?

            @success = true
            @record = content
          else
            @errors << I18n.t('content.business_not_fount', guid: response['business_id'], scope: 'activerecord.errors.services')
          end
        rescue StandardError => e
          @success = false
          @errors << I18n.t('api.v2.contents.errors.save_error') if @errors.empty?
        end
      end

      private

      def invalid_steps(step_ids)
        existing_ids = Step.where(id: step_ids).pluck(:id)
        step_ids - existing_ids
      end

      def process_sub_business_contents(parent_content)
        return unless @has_sub_business && @sub_params.present?
        field_references = Field.where(id: @sub_params.map { |sub| sub[:field_id] })
                                .pluck(:id, :reference_sub_business_id)
                                .to_h
        sub_business_name = parent_content&.business.name
        @sub_params.each do |sub_business|
          reference_sub_business_id = field_references[sub_business[:field_id]]
          sub_business[:value].each do |sub_param|
            sub_service = self.class.new(
              sub_param.merge(
                business_id: reference_sub_business_id,
                parent_id: parent_content.id,
                current_user: @current_user,
                remote_ip: @remote_ip
              )
            )
            sub_service.save
            if sub_service.success
              @sub_content_ids << sub_service.record.id
            else
              sub_errors = {
                sub_business_id: reference_sub_business_id,
                sub_business_name: sub_business_name,
                errors: sub_service.errors
              }
              @errors << sub_errors
              raise ActiveRecord::Rollback
            end
          end
        end
      end

      def create_or_load_content(business, params, content_id)
        content = Content.find_by(id: content_id) if content_id.present?

        if content.blank?
          service = ::ContentService.new(
                                          business_id: business&.id,
                                          parent_id: params['parent_id'],
                                          created_by_id: params['created_by'],
                                          remote_ip: @remote_ip,
                                          params: params,
                                          user: @current_user,
                                          content_id: content_id
                                        )

          service.create

          if service.success
            content = service.record
          else
            @errors << I18n.t('content.error_saving_xml_content', content_xml: content_xml.to_xml, errors: service.errors.join(','), scope: 'activerecord.errors.services')
          end
        end

        content
      end

      def update_answer(answer, step_params)
        answer_id = answer&.id

        skip_business_rules = step_params&.dig('skip_business_rules') || false
        skip_input_rules = step_params&.dig('skip_input_rules') || false
        skip_validation_rules = step_params&.dig('skip_validation_rules') || false
        skip_external_validation = step_params&.dig('skip_external_validation') || false
        skip_external_input = step_params&.dig('skip_external_input') || false
        skip_mandatory_fields = step_params&.dig('skip_mandatory_fields') || false
        skip_field_validations = step_params&.dig('skip_field_validations') || false
        skip_webhook = step_params&.dig('skip_webhook') || false

        steps_to_change_ids = step_params&.dig('steps_to_change_ids') || nil
        steps_to_change_ids_errors = validate_steps_to_change_ids(steps_to_change_ids).join(", ")

        @errors << steps_to_change_ids_errors if steps_to_change_ids_errors.present?
        @has_sub_business = sub_business_value?(step_params)

        if value_key?(step_params)
          answer = update_answer_with_json(answer.content, step_params).find(answer_id)
        end

        answer.skip_mandatory_fields = skip_mandatory_fields

        answer_params = { user_id: @current_user&.id, remote_ip: @remote_ip, content_id: answer.content&.id }
        answer_decorated = answer.decorate(
          current_user: User.new(id: answer_params['user_id']),
          context: {
            skip_verification_url: skip_external_input,
            skip_business_rules: skip_business_rules,
            skip_input_rules: skip_input_rules,
          }
        ).values || {}

        answer_params = answer_decorated.merge(build_step_params(step_params)).merge(answer_params)

        service = ::AnswerService.new(
          answer_params.merge(
            'skip_field_validations' => skip_field_validations,
            'skip_verification_url' => skip_external_input,
            'skip_mandatory_fields' => answer.skip_mandatory_fields,
            'skip_business_rules' => skip_business_rules,
            'skip_validation_rules' => skip_validation_rules,
            'skip_webhook' => skip_webhook,
            'steps_to_change_ids' => steps_to_change_ids,
            'upload_base64_files' => has_base64_key?(step_params),
            'origin' => Answer.origins[:api]
            )
          )
        service.update(answer&.id)

        if service.errors.present?
          @errors.concat(service.errors)
        end
      end

      def validate_steps_to_change_ids(steps_to_change_ids)
        errors = []
        if !steps_to_change_ids.nil? && !steps_to_change_ids.is_a?(Array)
          errors << I18n.t('content.steps_to_change_ids_should_be_array', scope: 'activerecord.errors.services')
        end

        return [] if steps_to_change_ids.blank?

        regex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
        steps_to_change_ids.each do |step_id|
          errors << I18n.t('content.step_id_should_be_string', step_id: step_id, scope: 'activerecord.errors.services') if !step_id.is_a?(String)
          errors << I18n.t('content.step_id_should_be_a_guid', step_id: step_id, scope: 'activerecord.errors.services') if step_id !~ regex
        end
        errors
      end

      def has_base64_key?(step_params)
        if step_params.is_a?(Array)
          step_params.any? { |element| has_base64_key?(element) }
        elsif step_params.is_a?(Hash)
          step_params.key?("base64") || step_params.values.any? { |value| has_base64_key?(value) }
        else
          false
        end
      end

      def validate_answer_per_fields(step_params)
        return if step_params['templates'].blank?

        step_params['templates'].each_with_object({}) do |template, hash|
          template['fields'].each do |param_field|
            next if param_field['value'].blank?

            field = Field.find(param_field['id'])

            if param_field.key?('value')
              case field.type
              when 'multiple', 'multiple_reference', 'upload'
                @errors << I18n.t('api.v2.contents.errors.field_validation.array_required', field_id: field.id) unless param_field['value'].is_a?(Array)
              when 'integer'
                @errors << I18n.t('api.v2.contents.errors.field_validation.integer_required', field_id: field.id) unless param_field['value'].is_a?(Integer)
              when 'decimal'
                @errors << I18n.t('api.v2.contents.errors.field_validation.decimal_required', field_id: field.id) unless param_field['value'].is_a?(Float)
              when 'text', 'text_area', 'dropdown', 'telephone', 'reference'
                @errors << I18n.t('api.v2.contents.errors.field_validation.string_required', field_id: field.id) unless param_field['value'].is_a?(String)
              when 'date'
                @errors << I18n.t('api.v2.contents.errors.field_validation.date_required', field_id: field.id) unless param_field['value'].match?(/^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/)
              when 'link'
                param_field['value'] = param_field['value'].to_json
              end
            else
              @errors << I18n.t('api.v2.contents.errors.field_validation.value_not_provided', field_id: field.id)
            end
          end
        end
      end

      def build_step_params(step_params)
        return {} if step_params['templates'].blank?

        step_params['templates'].each_with_object({}) do |template, hash|
          template['fields'].each do |field|
            hash[field['id']] = field['value']
          end
        end
      end

      def value_key?(hash)
        return false if hash.nil?
        hash['templates']&.any? do |template|
          template['fields']&.any? { |field| field.key?('value') }
        end
      end

      def sub_business_value?(hash)
        return false if hash.nil?
        hash['templates']&.each do |template|
          template['fields'].each do |field|
            value = field['value']
            next if !value.is_a?(Array) || value.empty? || value.all? { |item| item.is_a?(String) }

            if value.all? { |item| item.key?("steps") }
              @sub_params << { field_id: field['id'], value: value }
            end
          end
        end
      end

      def update_answer_with_json(content, step_params)
        return content if step_params['templates'].blank?

        content&.answers.each_with_index do |answer, index|
          answers_step = step_params['templates'].flat_map { |param| param['fields'] }.flatten
          values = {
            values: answers_step.each_with_object({}) do |item, acc|
              acc[item['id']] = item['value']
            end
          }

          answer.data = values
          answer.user_id = @current_user&.id
        end
      end
    end
  end
end
