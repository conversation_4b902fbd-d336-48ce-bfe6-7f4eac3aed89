module Dashboard
  class BusinessStatsService
    def initialize(businesses_relation, current_user)
      @businesses_relation = businesses_relation
      @current_user = current_user
    end

    def call
      return [] if @businesses_relation.empty?

      business_ids = @businesses_relation.pluck(:id)
      businesses_data = @businesses_relation.select(:id, :name).index_by(&:id)
      content_counts = calculate_content_counts(business_ids)
      inactive_counts = calculate_inactive_counts(business_ids)

      business_ids.map do |business_id|
        business = businesses_data[business_id]
        counts = content_counts[business_id] || {}

        {
          id: business.id,
          name: business.name,
          pending_count: counts[:pending] || 0,
          done_count: counts[:done] || 0,
          changing_count: counts[:changing] || 0,
          under_review_count: counts[:under_review] || 0,
          waiting_authorization_count: counts[:waiting_authorization] || 0,
          count: counts.values.sum,
          inactives: inactive_counts[business_id] || 0
        }
      end
    end

    private

    def calculate_content_counts(business_ids)
      base_query = Content.kept
                          .joins(:business)
                          .where(business_id: business_ids)
                          .where(businesses: { sub_business: false })
                          .where(draft: false)

      filtered_query = apply_user_filters(base_query)
      counts = filtered_query.group(:business_id, :status).count

      result = {}
      counts.each do |(business_id, status), count|
        result[business_id] ||= {}
        result[business_id][status.to_sym] = count
      end

      result
    end

    def calculate_inactive_counts(business_ids)
      Content.with_discarded
             .discarded
             .where(business_id: business_ids)
             .group(:business_id)
             .count
    end

    def apply_user_filters(query)
      return query unless @current_user

      if @current_user.limited?
        query.where(created_by_id: @current_user.id)
      elsif @current_user.departments.any?
        if @current_user.departments.all?(&:limited?)
          department_ids = @current_user.departments.pluck(:id)
          user_ids = User.joins(:departments)
                        .where(departments: { id: department_ids })
                        .pluck(:id)
          query.where(created_by_id: user_ids).distinct
        else
          query
        end
      else
        query
      end
    end
  end
end
