class AnswerVersionDatatable < AjaxDatatablesRails::ActiveRecord
  include ActionView::Helpers::<PERSON><PERSON>elper

  def view_columns
    @view_columns ||= {
      id: { source: 'AnswerVersion.id', cond: :like },
      business_name: { source: 'Business.name', cond: :like },
      step_name: { source: 'Step.name', cond: :like },
      changes: { source: 'AnswerVersion.object_changes', cond: :like },
      content_id: { source: 'Content.id', cond: :like },
      key_fields: { source: 'key_fields', cond: filter_key_fields }
    }
  end

  private

  def data
    records.map do |record|
      {
        id: record.id,
        event: record.event,
        content_id: record.answer.content_id,
        filled_at: I18n.l(record.filled_at, format: :short_datatable),
        created_at: I18n.l(record.created_at, format: :short_datatable),
        user_name: record.whodunnit.name,
        ip: record.ip.presence || I18n.t('ip_not_obtained', scope: 'datatables.answer_version'),
        step_name: record.answer.with_deleted_step&.name,
        business_name: record.answer.with_deleted_content.business.name,
        business_id: record.answer.with_deleted_content.business.id,
        changes: changes(record),
        key_fields: key_fields(record),
        step_authorizer_name: record.step_authorizer&.name,
        step_authorizer_email: record.step_authorizer&.email,
        parent_content_id: record.answer.content.parent_id,
        parent_business_id: record.answer.content.business.parent&.id,
        deletion_reason: record.answer.content.deletion_reason,
        old_status: get_status_answer(record.old_status),
        new_status: get_status_answer(record.new_status)
      }
    end.delete_if { |record| record[:event] == 'update' && (record[:changes].empty? || record[:changes] == '[]') }
  end

  def get_status_answer(status)
    Answer.statuses.key(status)
  end

  def changes(record)
    return [] if record.object_changes.blank?

    record.object_changes.map do |key, values|
      next if values.any? { |value| is_subcontent_value?(value) }

      [key, values.first, values.second]
    end.compact_blank.to_json
  end


  def key_fields(record)
    answer = record.answer
    content = answer.content

    content.business.key_fields.order('businesses_fields.created_at').map do |key_field|
      { label: key_field.label, value: content.answers&.first&.values&.dig(key_field.id) }
    end.select { |hash| hash[:value].present? }.to_json
  end

  def is_subcontent_value?(value)
    [value].flatten.any? { |e| e.is_a?(Hash) && e.key?('content_id') }
  end

  def get_raw_records
    scope = base_scope

    scope = search_join(scope) if searching?
    scope = scope.includes(answer: [:with_deleted_step, { with_deleted_content: :business }])

    scope = scope.where(contents: { business_id: params[:business_id] }) if params[:business_id].present?
    scope = scope.where(answers: { step_id: params[:step_id] }) if params[:step_id].present?
    scope = scope.where(whodunnit: params[:whodunnit_id]) if params[:whodunnit_id].present?

    if params[:start_on].present?
      start_time = params[:start_at].present? ? Time.zone.parse("#{params[:start_on]} #{params[:start_at]}") : params[:start_on].to_date.beginning_of_day
      scope = scope.where(answer_versions: { created_at: start_time.. })
    end

    if params[:end_on].present?
      end_time = params[:end_at].present? ? Time.zone.parse("#{params[:end_on]} #{params[:end_at]}") : params[:end_on].to_date.end_of_day
      scope = scope.where(answer_versions: { created_at: ..end_time })
    end
    scope.order('answer_versions.created_at DESC')
  end

  def filter_key_fields
    ->(column, _value) do
      term = column.search.value.to_s
      return nil if term.blank?

      escaped = ActiveRecord::Base.sanitize_sql_like(term)
      pattern = "%#{escaped}%"
      quoted = ActiveRecord::Base.connection.quote(pattern)

      Arel.sql("(answer_versions.form_values::text ILIKE #{quoted} OR answer_versions.values::text ILIKE #{quoted})")
    end
  end

  def searching?
    params.dig(:search, :value).present?
  end

  def filter_records(records)
    records.where(build_conditions).distinct
  end

  def base_scope
    @base_scope ||= AnswerVersion.unscoped.joins(:whodunnit, answer: [:with_deleted_step, { with_deleted_content: :business }])
  end

  def search_join(scope)
    scope.joins(:content_values).where.not(content_values: { value: [nil, ''] })
  end
end
