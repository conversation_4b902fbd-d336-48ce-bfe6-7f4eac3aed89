source 'https://rubygems.org'

ruby file: '.ruby-version'

git_source(:github) do |repo_name|
  repo_name = "#{repo_name}/#{repo_name}" unless repo_name.include?('/')
  "https://github.com/#{repo_name}.git"
end

gem 'rails', '~> 7'
gem 'aasm'
gem 'ajax-datatables-rails'
gem 'appsignal'
gem 'async-http'
gem 'aws-sdk-cloudwatch'
gem 'aws-sdk-s3'
gem 'aws-sdk-wafv2'
gem 'brakeman'
gem 'devise'
gem 'devise-security'
gem 'devise_token_auth'
gem 'discard'
gem 'draper'
gem 'elasticsearch'
gem 'falcon'
gem 'ffi'
gem 'fog-aws'
gem 'jbuilder'
gem 'json-schema'
gem 'keisan'
gem 'memory_profiler'
gem 'multi_json'
gem 'netaddr'
gem 'oj'
gem 'omniauth'
gem 'omniauth-entra-id'
gem 'omniauth-google-oauth2'
gem 'omniauth_openid_connect'
gem 'paper_trail'
gem 'pg'
gem 'pg_search'
gem 'psych'
gem 'redis'
gem 'rest-client'
gem 'rack-cors', require: 'rack/cors'
gem 'rack-robustness'
gem 'rails-pg-extras'
gem 'rollbar'
gem 'ros-apartment', require: 'apartment'
gem 'ruby-progressbar'
gem 'rswag-api'
gem 'rswag-ui'
gem 'rswag-specs'
gem 'rubyXL'
gem 'sentry-rails'
gem 'sentry-ruby'
gem 'sidekiq'
gem 'sidekiq_alive'
gem 'sidekiq-bulk'
gem 'sidekiq-scheduler'
gem 'sidekiq-unique-jobs'
gem 'to_regexp'
gem 'translate_enum'
gem 'twilio-ruby'
gem 'tzinfo-data'
gem 'validate_url'
gem 'webrick'
gem 'will_paginate'
gem 'pundit'

group :test do
  gem 'airborne'
  gem 'webmock'

  gem 'faker'
  gem 'rails-controller-testing'
  gem 'rspec-activemodel-mocks'
  gem 'rspec-collection_matchers'
  gem 'rspec-rails'
  gem 'shoulda-matchers'
  gem 'simplecov'
  gem 'timecop'

  gem 'factory_bot_rails'
  gem 'fuubar'
  gem 'rubycritic', require: false
  gem 'should_not'
end

group :development, :test do
  gem 'letter_opener'
  gem 'letter_opener_web'
  gem 'parallel_tests'
end

group :development do
  gem 'bullet'
  gem 'bundler-audit'
  gem 'byebug'
  gem 'fasterer'
  gem 'i18n_generators'
  gem 'listen'
  gem 'net-scp'
  gem 'net-ssh'
  gem 'overcommit'
  gem 'rails_best_practices'
  gem 'rubocop'
  gem 'rubocop-rails'
  gem 'spring'
  gem 'thor'
  gem 'benchmark-memory'
end
